package org.biosino.task;


import cn.hutool.core.util.StrUtil;
import org.biosino.task.service.task.FastQCTaskService;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Properties;

import static org.springframework.amqp.rabbit.core.RabbitAdmin.QUEUE_MESSAGE_COUNT;

@SpringBootTest
public class NodeTaskApplicationTest {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private RabbitAdmin rabbitAdmin;

    @Autowired
    private FastQCTaskService fastQCTaskService;

    @Test
    public void testPriority() {
        for (int i = 0; i < 10; i++) {
            int finalI = i;
            rabbitTemplate.convertAndSend("node_delay", "fastqc_task_start_routing_key", StrUtil.format("优先级为{}", i), message -> {
                message.getMessageProperties().setPriority(finalI);
                return message;
            });
        }
    }

    @Test
    public void testRabbitAdmin() {
        Properties properties = rabbitAdmin.getQueueProperties("fastqc_task_start_queue");
        Object o = properties.get(QUEUE_MESSAGE_COUNT);
    }

    @Test
    public void pushFastQCTaskStartMsg() {
        fastQCTaskService.pushFastQCTaskStartMsg();
    }

}
