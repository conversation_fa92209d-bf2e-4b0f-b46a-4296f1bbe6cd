package org.biosino.task.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.ArchiveEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.task.repository.DataCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@RequiredArgsConstructor
public class DataCustomRepositoryImpl implements DataCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public List<String> getExistNos(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(Criteria.where("dat_no").in(dataNos));
        Query query = new Query(new Criteria().andOperator(condition));
        query.fields().include("dat_no");
        return mongoTemplate.findDistinct(query, "dat_no", Data.class, String.class);
    }

    @Override
    public List<Data> findByDatNoIn(Collection<String> dataNos) {
        if (CollUtil.isEmpty(dataNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(Criteria.where("dat_no").in(dataNos));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Data.class);
    }

    @Override
    public Optional<Data> findFirstUnarchivedByMd5(String md5, String creator) {
        if (StrUtil.isBlank(md5) || StrUtil.isBlank(creator)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("security").in(SecurityEnum.includeAllSecurity()));
        condition.add(Criteria.where("md5").is(md5));
        condition.add(Criteria.where("creator").is(creator));
        condition.add(Criteria.where("archived").is(ArchiveEnum.no.name()));
        Query query = new Query(new Criteria().andOperator(condition));
        Data data = mongoTemplate.findOne(query, Data.class);
        return Optional.ofNullable(data);
    }
}
