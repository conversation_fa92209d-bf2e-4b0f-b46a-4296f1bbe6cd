package org.biosino.task.util;

import cn.hutool.core.io.FileUtil;
import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.exception.ServiceException;

/**
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class RsyncUtils {

    private final String RSYNC_CMD = "rsync -avL --no-o --no-g %s %s";

    @SneakyThrows
    public void copyFile(String path, String destPath) {
        copyFile(path, destPath, false);
    }

    @SneakyThrows
    public void copyFileIgnore(String path, String destPath) {
        copyFile(path, destPath, true);
    }

    @SneakyThrows
    public void copyFile(String path, String destPath, boolean ignore) {
        log.debug("source path: [{}] -----> dest path : [{}]", path, destPath);
        if (!FileUtil.exist(path)) {
            if (ignore) {
                log.info("用户指定的文件不存在，文件路径为：{}", path);
                return;
            } else {
                log.error("用户指定的文件不存在，文件路径为：{}", path);
                throw new ServiceException("目标文件不存在");
            }
        }

        FileUtil.touch(destPath);

        final ProcessBuilder rsync = new ProcessBuilder("rsync", "-avL", path, destPath);
        String cmd = String.format(RSYNC_CMD, path, destPath);
        try {
            log.debug("进行文件移动：command: {}", cmd);
            rsync.start().waitFor();
        } catch (Exception e) {
            log.error("执行文件移动命令出错：command: {}", cmd);
            throw new ServiceException("rsync移动文件错误");
        }
    }
}
