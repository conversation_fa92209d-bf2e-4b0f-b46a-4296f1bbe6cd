package org.biosino.task.mq;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.BussinessTaskTypeEnum;
import org.biosino.system.api.msg.BusinessTaskMsg;
import org.biosino.task.service.task.FtpUploadTask;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/1/4
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class BusinessTaskListener {

    private final FtpUploadTask ftpUploadTask;

    @RabbitListener(queues = "business_task_queue")
    @RabbitHandler
    public void businessTaskCreate(@Payload BusinessTaskMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.debug("business_task_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            String taskType = msg.getTaskType();
            if (taskType.equals(BussinessTaskTypeEnum.ftp_upload.name())) {
                ftpUploadTask.handleFtpUploadTask(msg);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

}
