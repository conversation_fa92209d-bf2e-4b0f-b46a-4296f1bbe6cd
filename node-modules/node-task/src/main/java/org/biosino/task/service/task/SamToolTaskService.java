package org.biosino.task.service.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.Constants;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.SamToolTask;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.SamToolTaskStartMsg;
import org.biosino.common.rabbitmq.msg.SamToolTaskStatusMsg;
import org.biosino.common.redis.service.RedisService;
import org.biosino.task.repository.DataRepository;
import org.biosino.task.repository.RunRepository;
import org.biosino.task.repository.SamToolTaskRepository;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/5/21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SamToolTaskService {

    private final RabbitAdmin rabbitAdmin;

    private final MessageSender messageSender;

    private final SamToolTaskRepository samToolTaskRepository;

    private final DataRepository dataRepository;

    private final RunRepository runRepository;

    private final RedisService redisService;

    private final static String PUSH_SAMTOOL_TASK_KEY = "push_samtool_task_key";

    private final static String PUSH_SAMTOOL_HP_TASK_KEY = "push_samtool_hp_task_key";

    private final static Long EXPIRE_TIME = 5L;

    public synchronized void pushSamToolTaskStartMsg() {
        if (redisService.getCacheObject(PUSH_SAMTOOL_TASK_KEY) != null) {
            log.info("pushSamToolTaskStartMsg is running");
            return;
        }
        redisService.setCacheObject(PUSH_SAMTOOL_TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);

        try {
            Properties startProps = rabbitAdmin.getQueueProperties("samtool_task_start_queue");
            // 证明队列不存在，
            if (startProps == null) {
                log.warn("samtool_task_start_queue not exist");
                throw new ServiceException("samtool_task_start_queue not exist");
            }
            int messageCount = (int) startProps.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            // 如果队列中还有超过40条消息，不重新推送消息
            if (messageCount > 40) {
                log.info("samtool_task_start_queue messageCount: {}", messageCount);
                return;
            }

            int pageSize = 40;
            List<SamToolTask> taskList = samToolTaskRepository.getReadyTasksAndPriorityLte(4, pageSize);

            if (CollUtil.isEmpty(taskList)) {
                log.info("There are no tasks ready to start");
                return;
            }

            List<SamToolTaskStartMsg> startMsgs = new ArrayList<>();
            for (SamToolTask samToolTask : taskList) {
                SamToolTaskStartMsg startMsg = new SamToolTaskStartMsg();
                String fileSuffix = getSamFileSuffix(samToolTask.getDataFileName());

                startMsg.setDataNo(samToolTask.getDataNo());
                Date dataCreateDate = samToolTask.getDataCreateDate();
                String baseDir = DateUtils.dateTimeFormat4Ftp(dataCreateDate);
                startMsg.setResultBaseDir(baseDir);
                startMsg.setFileType(fileSuffix);
                startMsg.setDataFilePath(samToolTask.getDataFilePath());
                if (samToolTask.getPriority() >= 5) {
                    startMsg.setPriority("high");
                } else {
                    startMsg.setPriority("low");
                }
                startMsg.setPriorityInt(samToolTask.getPriority());
                String dataFileName = StrUtil.replaceIgnoreCase(samToolTask.getDataFileName(), fileSuffix, "");
                startMsg.setDataFileName(dataFileName);
                startMsgs.add(startMsg);

                samToolTask.setStatus(FastQCTaskStatusEnum.queuing.name());
                samToolTask.setUpdateDate(new Date());
            }

            samToolTaskRepository.saveAll(taskList);

            for (SamToolTaskStartMsg startMsg : startMsgs) {
                messageSender.sendDelayPriorityMsg("samtool_task_start_routing_key", startMsg, startMsg.getPriorityInt());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.deleteObject(PUSH_SAMTOOL_TASK_KEY);
        }
    }

    public synchronized void pushSamToolHpTaskStartMsg() {
        if (redisService.getCacheObject(PUSH_SAMTOOL_HP_TASK_KEY) != null) {
            log.info("pushSamToolHpTaskStartMsg is running");
            return;
        }
        redisService.setCacheObject(PUSH_SAMTOOL_HP_TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);

        try {
            Properties startProps = rabbitAdmin.getQueueProperties("samtool_hp_task_start_queue");
            // 证明队列不存在，
            if (startProps == null) {
                log.warn("samtool_hp_task_start_queue not exist");
                throw new ServiceException("samtool_hp_task_start_queue not exist");
            }
            int messageCount = (int) startProps.get(RabbitAdmin.QUEUE_MESSAGE_COUNT);
            // 如果队列中还有超过40条消息，不重新推送消息
            if (messageCount > 40) {
                log.info("samtool_hp_task_start_queue messageCount: {}", messageCount);
                return;
            }

            int pageSize = 40;
            List<SamToolTask> taskList = samToolTaskRepository.getReadyTasksAndPriorityGte(5, pageSize);

            if (CollUtil.isEmpty(taskList)) {
                log.info("There are no tasks ready to start");
                return;
            }

            List<SamToolTaskStartMsg> startMsgs = new ArrayList<>();
            for (SamToolTask samToolTask : taskList) {
                SamToolTaskStartMsg startMsg = new SamToolTaskStartMsg();
                String fileSuffix = getSamFileSuffix(samToolTask.getDataFileName());

                startMsg.setDataNo(samToolTask.getDataNo());
                Date dataCreateDate = samToolTask.getDataCreateDate();
                String baseDir = DateUtils.dateTimeFormat4Ftp(dataCreateDate);
                startMsg.setResultBaseDir(baseDir);
                startMsg.setFileType(fileSuffix);
                startMsg.setDataFilePath(samToolTask.getDataFilePath());
                if (samToolTask.getPriority() >= 5) {
                    startMsg.setPriority("high");
                } else {
                    startMsg.setPriority("low");
                }
                startMsg.setPriorityInt(samToolTask.getPriority());
                String dataFileName = StrUtil.replaceIgnoreCase(samToolTask.getDataFileName(), fileSuffix, "");
                startMsg.setDataFileName(dataFileName);
                startMsgs.add(startMsg);

                samToolTask.setStatus(FastQCTaskStatusEnum.queuing.name());
                samToolTask.setUpdateDate(new Date());
            }

            samToolTaskRepository.saveAll(taskList);

            for (SamToolTaskStartMsg startMsg : startMsgs) {
                messageSender.sendDelayPriorityMsg("samtool_hp_task_start_routing_key", startMsg, startMsg.getPriorityInt());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            redisService.deleteObject(PUSH_SAMTOOL_HP_TASK_KEY);
        }
    }

    public void handlerTaskCreate(List<String> dataNos) {
        // 查询有哪些dataNo已经有了SAM任务
        Map<String, SamToolTask> dataNotoSamToolTaskMap = samToolTaskRepository.findByDataNoIn(dataNos).stream().collect(Collectors.toMap(SamToolTask::getDataNo, Function.identity(), (existingValue, newValue) -> existingValue));
        // 查询这些Data
        List<Data> dataList = dataRepository.findByDatNoIn(dataNos);
        // 查询这些Data对应的Run
        List<String> runNos = dataList.stream().map(Data::getRunNo).distinct().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        Map<String, Run> runNoToRunMap = runRepository.findAllByRunNoIn(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<SamToolTask> saveSamToolTasks = new ArrayList<>();
        for (Data data : dataList) {
            SamToolTask existTask = dataNotoSamToolTaskMap.get(data.getDatNo());
            // 如果已经存在samtool的任务，看对应的任务的expNo、sapNo是否需要补充
            if (existTask != null) {
                if ((data.getRunNo() != null
                        && runNoToRunMap.containsKey(data.getRunNo())
                        && StrUtil.startWith(data.getRunNo(), "OER"))) {
                    Run run = runNoToRunMap.get(data.getRunNo());
                    existTask.setExpNo(run.getExpNo());
                    existTask.setSapNo(run.getSapNo());
                }
                if (StrUtil.isNotBlank(data.getAnalNo())) {
                    existTask.setAnalNo(data.getAnalNo());
                }
                existTask.setSubNo(data.getSubNo());
                // 如果文件名不相同，那么需要更新文件名，得重新跑samtool
                String dataName = data.getTempData() != null ? data.getTempData().getName() : data.getName();
                if (!StrUtil.equals(existTask.getDataFileName(), dataName)) {
                    existTask.setDataFileName(dataName);
                    existTask.setStatus(FastQCTaskStatusEnum.ready.name());
                }
                saveSamToolTasks.add(existTask);
            } else {
                if (StrUtil.endWithAnyIgnoreCase(data.getName(), Constants.BAM_SUFFIX)
                        || StrUtil.endWithAnyIgnoreCase(data.getFileName(), Constants.BAM_SUFFIX)) {
                    SamToolTask samToolTask = new SamToolTask();
                    samToolTask.setPriority(5);
                    samToolTask.setDataNo(data.getDatNo());
                    samToolTask.setDataFileName(data.getName());
                    samToolTask.setDataFilePath(data.getFilePath());
                    samToolTask.setDataFileSize(data.getFileSize());
                    samToolTask.setDataCreateDate(data.getCreateDate());
                    samToolTask.setSubNo(data.getSubNo());
                    samToolTask.setCreateDate(new Date());
                    samToolTask.setStatus(FastQCTaskStatusEnum.ready.name());
                    saveSamToolTasks.add(samToolTask);
                }
            }
        }
        for (SamToolTask saveSamToolTask : saveSamToolTasks) {
            if (saveSamToolTask.getPriority() != null && saveSamToolTask.getPriority() < 5) {
                saveSamToolTask.setPriority(5);
            }
        }

        if (CollUtil.isNotEmpty(saveSamToolTasks)) {
            samToolTaskRepository.saveAll(saveSamToolTasks);
        }
    }

    public void handlerStatusChange(SamToolTaskStatusMsg msg) {
        SamToolTask samToolTask = samToolTaskRepository.findFirstByDataNo(msg.getDataNo())
                .orElseThrow(() -> new ServiceException(StrUtil.format("task {} not exist", msg.getDataNo())));

        if (StrUtil.equals(FastQCTaskStatusEnum.running.name(), msg.getStatus())) {
            samToolTask.setStatus(FastQCTaskStatusEnum.running.name());
            samToolTask.setUpdateDate(new Date());
            samToolTaskRepository.save(samToolTask);
            return;
        }

        if (StrUtil.equals(FastQCTaskStatusEnum.failed.name(), msg.getStatus())) {
            samToolTask.setStatus(FastQCTaskStatusEnum.failed.name());
            samToolTask.setUpdateDate(new Date());
            samToolTask.setFailCause(msg.getFailCause());
            samToolTask.setExitCode(msg.getExitCode());
            if (StrUtil.isNotBlank(msg.getErrorLogPath())) {
                samToolTask.setErrorLogPath(msg.getErrorLogPath());
            }
            samToolTask.setUseTime(null);
            samToolTaskRepository.save(samToolTask);
            return;
        }

        if (StrUtil.equals(FastQCTaskStatusEnum.success.name(), msg.getStatus())) {
            handlerTaskSuccess(msg);
            return;
        }
    }

    private void handlerTaskSuccess(SamToolTaskStatusMsg msg) {
        SamToolTask samToolTask = samToolTaskRepository.findFirstByDataNo(msg.getDataNo())
                .orElseThrow(() -> new ServiceException(StrUtil.format("task {} not exist", msg.getDataNo())));

        try {
            String useTime = DateUtil.formatBetween(samToolTask.getUpdateDate(), new Date(), BetweenFormatter.Level.MINUTE);
            samToolTask.setUseTime(useTime);
            samToolTask.setStatus(FastQCTaskStatusEnum.success.name());
            samToolTask.setUpdateDate(new Date());
            // 清除无关数据
            samToolTask.setFailCause(null);
        } catch (Exception e) {
            e.printStackTrace();
            samToolTask.setStatus(FastQCTaskStatusEnum.failed.name());
            samToolTask.setUpdateDate(new Date());
            samToolTask.setFailCause(e.getMessage());
        } finally {
            samToolTaskRepository.save(samToolTask);
        }
    }

    public String getSamFileSuffix(String filename) {
        for (String samSuffix : Constants.BAM_SUFFIX) {
            if (StrUtil.endWithIgnoreCase(filename, samSuffix)) {
                return samSuffix;
            }
        }
        return null;
    }
}
