package org.biosino.task.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.task.service.task.FastQCTaskService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@RestController
@RequestMapping("/fastqc")
@RequiredArgsConstructor
public class FastQcController {

    private final FastQCTaskService fastQCTaskService;

    @RequestMapping("/pushFastQcTaskStartMsg")
    public AjaxResult pushFastQcTaskStartMsg() {
        fastQCTaskService.pushFastQCTaskStartMsg();
        return AjaxResult.success();
    }

    @RequestMapping("/pushFastQcHpTaskStartMsg")
    public AjaxResult pushFastQcHpTaskStartMsg() {
        fastQCTaskService.pushFastQCHpTaskStartMsg();
        return AjaxResult.success();
    }
}
