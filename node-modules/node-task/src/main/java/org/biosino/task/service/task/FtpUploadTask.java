package org.biosino.task.service.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.system.SystemUtil;
import com.alibaba.fastjson.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.WebConstants;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.system.api.domain.task.TransferTask;
import org.biosino.system.api.dto.SendEmailDTO;
import org.biosino.system.api.msg.BusinessTaskMsg;
import org.biosino.task.repository.DataRepository;
import org.biosino.task.service.FtpFileLogService;
import org.biosino.task.service.TransferTaskService;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.attribute.PosixFilePermissions;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> Li
 * @date 2024/1/4
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FtpUploadTask {
    private final DataRepository dataRepository;
    private final FtpFileLogService ftpFileLogService;
    private final TransferTaskService transferTaskService;
    private final RemoteNotificationService remoteNotificationService;

    public void handleFtpUploadTask(BusinessTaskMsg msg) {
        String taskId = msg.getTransferTaskId();
        TransferTask task = transferTaskService.getById(taskId);

        // 同步文件
        rsyncFile(task);

        // 更新FtpFileLog状态
        FtpFileLogStatus status = updateFtpFileLogStatus(task);

        // 第三步：判断文件是否转移成功
        if (FtpFileLogStatus.uploaded.equals(status)) {
            // 1.保存 data
            saveData(task);

            // 2.删除用户ftp下的目录
            File file = new File(task.getSourcePath());
            if (file.isFile()) {
                FileUtil.del(file);
            }
            File md5file = new File(task.getSourcePath().concat(".md5"));
            if (md5file.isFile()) {
                FileUtil.del(md5file);
            }
            File logfile = new File(task.getLogPath());
            if (logfile.isFile()) {
                FileUtil.del(logfile);
            }
        }
    }

    private Data saveData(TransferTask task) {
        Data data = new Data();

        data.setId(IdUtil.objectId());
        data.setName(task.getSourceName());
        data.setFileName(task.getSourceName());

        String filePath = task.getDestPath();
        data.setFilePath(MyFileUtils.absoluteToRelativePath(filePath, DirConstants.DATA_HOME));
        data.setFileSize(FileUtil.size(new File(filePath)));
        data.setUploadType(UploadType.ftp.name());
        data.setDataType(FileTypeUtils.getDataTypeByName(task.getSourceName()));

        data.setSecurity(SecurityEnum._private.getDesc());
        data.setCreator(task.getUserId());

        Date now = new Date();
        data.setCreateDate(now);
        data.setUpdateDate(now);
        data.setArchived(ArchiveEnum.no.name());
        data.setOperator(task.getCreator());
        data.setOwnership(OwnershipEnum.self_support.getDesc());
        data.setSourcePath(task.getSourcePath());

        FtpFileLog ftpFileLog = ftpFileLogService.findFirstByCreatorAndPath(task.getCreator(), task.getSourcePath());
        if (ftpFileLog != null) {
            data.setMd5(ftpFileLog.getMd5());
        }
        dataRepository.save(data);
        // 保存tempData
        Data tempData = new Data();
        BeanUtil.copyProperties(data, tempData);
        data.setTempData(tempData);

        dataRepository.save(data);
        return data;
    }

    private void rsyncFile(TransferTask task) {

        String[] cmd = {"/bin/bash", "-c", task.getCommand()};

        log.info("handleFtpUploadTask: exec command:[{}]", StringUtils.join(cmd, " "));

        Process process = null;
        try {
            task.setStartTime(new Date());
            // 设置任务信息，是在哪一台机器上进行的
            task.setStatus(ProgressEnums.progressing.name());
            transferTaskService.saveOrUpdate(task);
            // 如果是windows系统，则用FileUtil.copy()
            boolean result = false;
            if (SystemUtil.getOsInfo().isWindows()) {
                FileUtil.copy(task.getSourcePath(), task.getDestPath(), true);
                result = true;
            } else {
                process = Runtime.getRuntime().exec(cmd);
                // 服务器上拷贝速度大概是 100M/s，这里设置 1 小时超时，大概可以拷贝 360 G 以下的文件。
                result = process.waitFor(12, TimeUnit.HOURS);
            }

            // rsync 进程正常结束
            if (result) {

                File sourceFile = new File(task.getSourcePath());
                File descFile = new File(task.getDestPath());

                // 如果 目標文件存在，并且 源文件 和 目标文件 大小一致，认为拷贝成功
                if (descFile.exists() && sourceFile.length() == descFile.length()) {
                    task.setStatus(ProgressEnums.success.name());
                } else {
                    task.setStatus(ProgressEnums.error.name());
                    log.error("Err in handleFtpUploadTask: rsync log file validate error! TaskId = {}", task.getId());
                }
                // 3.更新文件权限
                try {
                    Files.setPosixFilePermissions(descFile.toPath(), PosixFilePermissions.fromString("rw-r--r--"));
                } catch (Exception e) {
                    log.error("更新目标文件权限失败：{}", e.getMessage());
                }
            } else {
                log.error("Err in handleFtpUploadTask: Timeout! TaskId = {}", task.getId());
                task.setStatus(ProgressEnums.error.name());
            }
        } catch (Exception e) {
            task.setStatus(ProgressEnums.error.name());
            log.error("exec command:[{}] fail! taskId = {}", StringUtils.join(cmd, " "), task.getId(), e);
            e.printStackTrace();
        } finally {
            task.setUpdateTime(new Date());
            transferTaskService.saveOrUpdate(task);

            if (process != null) {
                process.destroyForcibly();
            }
        }
    }

    private FtpFileLogStatus updateFtpFileLogStatus(TransferTask task) {

        FtpFileLog ftpFileLog = ftpFileLogService.findFirstByCreatorAndPath(task.getCreator(), task.getSourcePath());
        FtpFileLog ftpMd5FileLog = ftpFileLogService.findFirstByCreatorAndPath(task.getCreator(), task.getSourcePath().concat(".md5"));

        FtpFileLogStatus status = FtpFileLogStatus.checkFail;
        if (StringUtils.equalsAnyIgnoreCase(ProgressEnums.success.name(), task.getStatus())) {
            status = FtpFileLogStatus.uploaded;
        }

        if (ftpFileLog != null) {

            ftpFileLog.setUpdateTime(new Date());
            // 如果系统内部错误，就不修改状态
            if (status.equals(FtpFileLogStatus.checkFail)) {
                // 设置系统错误信息
                ftpFileLog.setFailCause("File transfer failed");
                // 通知系统错误
                Map<String, Object> map = new LinkedHashMap<>();
                map.put("msg", "File transfer failed");
                map.put("module", "node-task");
                map.put("content", JSON.toJSONString(task));
                SendEmailDTO mailContent = SendEmailDTO.builder()
                        .toEmail(WebConstants.SUPPORT_EMAIL)
                        .mailTemplate(MailTemplate.System_Error)
                        .params(map)
                        .build();
                remoteNotificationService.sendEmail(mailContent, SecurityConstants.INNER);
            } else {
                // 将状态修改为成功
                ftpFileLog.setStatus(status.getStatus());
            }
            ftpFileLog.setUpdateTime(new Date());
            ftpFileLogService.saveOrUpdate(ftpFileLog);
        } else {
            log.error("get ftpFileLog Failed! creator = " + task.getCreator() + ", path=" + task.getSourcePath());
        }

        if (ftpMd5FileLog != null) {
            // md5文件是否需要更新状态
            ftpMd5FileLog.setStatus(status.getStatus());
            ftpMd5FileLog.setUpdateTime(new Date());
            ftpFileLogService.saveOrUpdate(ftpMd5FileLog);
        } else {
            log.error("get ftpFileLog Failed! creator = " + task.getCreator() + ", path=" + task.getSourcePath().concat(".md5"));
        }

        return status;
    }
}
