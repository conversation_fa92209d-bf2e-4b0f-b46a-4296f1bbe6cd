package org.biosino.task.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.task.mapper.FtpFileLogMapper;
import org.biosino.task.service.FtpFileLogService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2023/12/22
 */
@Service
public class FtpFileLogServiceImpl extends ServiceImpl<FtpFileLogMapper, FtpFileLog> implements FtpFileLogService {


    @Override
    public FtpFileLog findFirstByCreatorAndPath(String creator, String path) {
        return this.baseMapper.findFirstByCreatorAndPath(creator, path);
    }


    @Override
    public FtpFileLog findFirstByPath(String path) {
        return this.baseMapper.findFirstByPath(path);
    }

    @Override
    public FtpFileLog getByIdAndCreator(String id, String creator) {
        FtpFileLog ftpFileLog = getById(id);
        if (ftpFileLog == null) {
            return null;
        }
        if (!ftpFileLog.getCreator().equals(creator)) {
            return null;
        }
        return ftpFileLog;
    }
}
