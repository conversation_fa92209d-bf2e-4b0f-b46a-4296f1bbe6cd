package org.biosino.task.mq;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.VipMapSyncTask;
import org.biosino.task.service.VipmapTaskService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR> @date 2024/7/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VipmapTaskListener {

    private final VipmapTaskService vipmapTaskService;

    @RabbitListener(queues = "vipmap_sync_task_queue")
    @RabbitHandler
    public void handlerTaskProcess(@Payload VipMapSyncTask syncTask, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("vipmap_sync_task_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(syncTask));
        try {
            vipmapTaskService.handlerTaskProcess(syncTask);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

}
