package org.biosino.task.controller;

import lombok.RequiredArgsConstructor;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.task.service.task.SamToolTaskService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/5/23
 */
@RestController
@RequestMapping("/samtool")
@RequiredArgsConstructor
public class SamToolController {

    private final SamToolTaskService samToolTaskService;

    @RequestMapping("/pushSamToolTaskStartMsg")
    public AjaxResult pushSamToolTaskStartMsg() {
        samToolTaskService.pushSamToolTaskStartMsg();
        return AjaxResult.success();
    }

    @RequestMapping("/pushSamToolHpTaskStartMsg")
    public AjaxResult pushSamToolHpTaskStartMsg() {
        samToolTaskService.pushSamToolHpTaskStartMsg();
        return AjaxResult.success();
    }
}
