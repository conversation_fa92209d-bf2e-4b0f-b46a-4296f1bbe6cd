package org.biosino.task.repository;

import org.biosino.common.mongo.entity.FastQCTask;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface FastQCTaskCustomRepository {
    List<FastQCTask> getReadyTasksAndPriorityGte(int priority, int pageSize);

    List<FastQCTask> getReadyTasksAndPriorityLte(int priority, int pageSize);

    List<String> getExistDataNos(Collection<String> dataNos);
}
