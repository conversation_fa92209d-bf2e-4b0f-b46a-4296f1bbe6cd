package org.biosino.task;

import org.biosino.common.security.annotation.EnableCustomConfig;
import org.biosino.common.security.annotation.EnableRyFeignClients;
import org.biosino.common.swagger.annotation.EnableCustomSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * Node Task模块
 *
 * <AUTHOR>
@EnableCustomConfig
@EnableCustomSwagger2
@EnableRyFeignClients
@SpringBootApplication
@ComponentScan(basePackages = {"org.biosino.common.mongo.entity", "org.biosino.task"})
public class NodeTaskApplication {

    public static void main(String[] args) {
        SpringApplication.run(NodeTaskApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  NODE-Task用户服务模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
