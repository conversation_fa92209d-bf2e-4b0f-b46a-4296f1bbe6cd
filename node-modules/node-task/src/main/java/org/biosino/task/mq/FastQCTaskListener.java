package org.biosino.task.mq;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.FastQCTaskStatusMsg;
import org.biosino.task.service.task.FastQCTaskService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FastQCTaskListener {

    private final FastQCTaskService fastQCTaskService;

    private final MessageSender messageSender;

    @RabbitListener(queues = "fastqc_task_create_queue")
    @RabbitHandler
    public void handlerTaskCreate(@Payload List<String> msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("fastqc_task_create_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            fastQCTaskService.handlerTaskCreate(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    // @RabbitListener(queues = "fastqc_task_start_queue")
    // @RabbitHandler
    // public void handlerTaskStart(@Payload FastQCTaskStartMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
    //     log.info("fastqc_task_start_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
    //     try {
    //         FastQCTaskStatusMsg runningMsg = new FastQCTaskStatusMsg();
    //         runningMsg.setTaskNo(msg.getTaskNo());
    //         runningMsg.setStatus(FastQCTaskStatusEnum.running.name());
    //         messageSender.sendDelayMsg("fastqc_task_status_routing_key", runningMsg);
    //
    //         FastQCTaskStatusMsg successMsg = new FastQCTaskStatusMsg();
    //         successMsg.setTaskNo(msg.getTaskNo());
    //         successMsg.setStatus(FastQCTaskStatusEnum.success.name());
    //         FastQCTaskStatusMsg.ResultData resultData = new FastQCTaskStatusMsg.ResultData();
    //         resultData.setFastqcHtmlFilepath("/********/FASTQC00000001/fastqc/test_1_fastqc.html");
    //         resultData.setFastqcReportFilepath("/********/FASTQC00000001/fastqc/test_1.fastq_trimming_report.txt");
    //         resultData.setFastqcZipFilepath("/********/FASTQC00000001/fastqc/test_1_fastqc.zip");
    //         resultData.setSeqkitFilepath("/********/FASTQC00000001/seqkit/test.seqkit.stats.txt");
    //         ThreadUtil.safeSleep(60 * 1000);
    //         successMsg.setData(resultData);
    //         messageSender.sendDelayMsg("fastqc_task_status_routing_key", successMsg);
    //     } catch (Exception e) {
    //         log.error(e.getMessage(), e);
    //     } finally {
    //         // 确认消息
    //         channel.basicAck(tag, false);
    //     }
    // }

    @RabbitListener(queues = "fastqc_task_status_queue")
    @RabbitHandler
    public void handlerTaskStatus(@Payload FastQCTaskStatusMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("fastqc_task_status_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            fastQCTaskService.handlerStatusChange(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

}
