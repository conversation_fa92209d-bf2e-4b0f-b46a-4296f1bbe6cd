package org.biosino.task.mq;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.system.api.msg.VerificationCreateMsg;
import org.biosino.system.api.msg.VerificationResultMsg;
import org.biosino.task.service.VerificationTaskService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/1/2
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VerificationTaskListener {

    private final VerificationTaskService verificationTaskService;

    @RabbitListener(queues = "verification_create_queue")
    @RabbitHandler
    public void verificationCreate(@Payload VerificationCreateMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.debug("verification_create {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            verificationTaskService.handleVerificationCreate(msg);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    @RabbitListener(queues = "verification_result_queue", concurrency = "1")
    @RabbitHandler
    public void verificationResult(@Payload VerificationResultMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.debug("verification_result_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            verificationTaskService.handleVerificationResult(msg);

        } catch (Exception e) {
            log.error(e.getMessage(), e);

        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }
}
