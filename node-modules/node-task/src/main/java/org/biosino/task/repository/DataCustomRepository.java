package org.biosino.task.repository;

import org.biosino.common.mongo.entity.Data;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface DataCustomRepository {
    List<String> getExistNos(Collection<String> dataNos);

    List<Data> findByDatNoIn(Collection<String> notExistNos);

    Optional<Data> findFirstUnarchivedByMd5(String md5, String creator);
}
