package org.biosino.task.repository.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Run;
import org.biosino.task.repository.RunCustomRepository;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/15
 */
@RequiredArgsConstructor
public class RunCustomRepositoryImpl implements RunCustomRepository {
    private final MongoTemplate mongoTemplate;

    @Override
    public List<Run> findAllByRunNoIn(Collection<String> runNos) {
        if (CollUtil.isEmpty(runNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(
                Criteria.where("run_no").in(runNos),
                Criteria.where("used_ids").in(runNos)
        ));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Run.class);
    }
}
