package org.biosino.task.repository;

import org.biosino.common.mongo.entity.FastQCTask;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FastQCTaskRepository extends MongoRepository<FastQCTask, String>, FastQCTaskCustomRepository {
    Optional<FastQCTask> findFirstByDataNo(String taskId);

    List<FastQCTask> findByDataNoIn(List<String> dataNos);
}
