package org.biosino.task.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.*;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.uuid.IdUtils;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Md5OpLog;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.CheckMd5Msg;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.domain.sftp.FtpFileLog;
import org.biosino.system.api.domain.task.TransferTask;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.msg.BusinessTaskMsg;
import org.biosino.system.api.msg.VerificationCreateMsg;
import org.biosino.system.api.msg.VerificationResultMsg;
import org.biosino.task.repository.DataRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR> Li
 * @date 2024/1/3
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VerificationTaskService {

    private final FtpFileLogService ftpFileLogService;

    private final TransferTaskService transferTaskService;

    private final MessageSender messageSender;

    private final DataRepository dataRepository;

    private final Md5OpLogRepository md5OpLogRepository;

    private final RemoteMemberService remoteMemberService;

    private final String RSYNC_CMD = "rsync -aL --no-o --no-g --progress '%s' '%s' >%s";


    public void handleVerificationCreate(VerificationCreateMsg msg) {
        FtpFileLog ftpFileLog = ftpFileLogService.getByIdAndCreator(msg.getId(), msg.getMemberId());
        if (ftpFileLog == null) {
            log.error("未找到对应的文件记录，memberId: {}, filepath: {}", msg.getMemberId(), msg.getFilepath());
            return;
        }
        // 如果md5不为空,则是上传的时候已经计算好md5了
        if (StrUtil.isNotBlank(ftpFileLog.getMd5()) && StrUtil.equals(ftpFileLog.getMd5FileContent(), ftpFileLog.getMd5())) {
            // 如果文件md5算的一致，则根据后缀判断文件是否完整
            if (StrUtil.endWithAnyIgnoreCase(ftpFileLog.getPath(), ".gz", ".zip")) {
                CheckMd5Msg checkMd5Msg = new CheckMd5Msg(ftpFileLog.getId(), "file", msg.getFilepath(), ftpFileLog.getMd5(), DateUtil.date().getTime());
                messageSender.sendDelayMsg("verification_delay_key", checkMd5Msg);
                return;
            }
            Optional<Data> optional = dataRepository.findFirstUnarchivedByMd5(ftpFileLog.getMd5(), ftpFileLog.getCreator());
            if (optional.isPresent()) {
                // 已经有了相同md5的数据
                String cause = "Already have same md5 Unarchived Data, please delete this file in your ftp home!";
                ftpFileLog.setStatus(FtpFileLogStatus.checkFail.getStatus());
                ftpFileLog.setFailCause(cause);
                ftpFileLog.setUpdateTime(new Date());
                ftpFileLogService.saveOrUpdate(ftpFileLog);
                return;
            }
            ftpFileLog.setUpdateTime(new Date());
            ftpFileLog.setVerifyTime(new Date());
            ftpFileLog.setStatus(FtpFileLogStatus.checkSuccess.getStatus());
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            // 通知移动文件
            transferFtpFileToNode(ftpFileLog.getCreator(), ftpFileLog.getPath());
            return;
        }
        // 消息给计算后端开始校验md5
        CheckMd5Msg checkMd5Msg = new CheckMd5Msg(ftpFileLog.getId(), "file", msg.getFilepath(), null, DateUtil.date().getTime());
        messageSender.sendDelayMsg("verification_delay_key", checkMd5Msg);
    }

    // 添加@Transactional，防止出现锁超时
    @Transactional
    public void handleVerificationResult(VerificationResultMsg msg) {
        String id = msg.getId();
        String filepath = msg.getFilepath();
        String result = msg.getResult();
        String md5 = msg.getMd5value();
        String cause = msg.getCause();

        Optional<Md5OpLog> optional = md5OpLogRepository.findFirstByFilePath(filepath);
        Md5OpLog md5OpLog;
        if (!optional.isPresent()) {
            md5OpLog = new Md5OpLog();
        }
        md5OpLog = optional.get();
        md5OpLog.setFilePath(filepath);
        md5OpLog.setMd5Value(md5);
        md5OpLog.setFailCause(cause);
        md5OpLog.setReplyDate(new Date());
        md5OpLog.setStatus(result);
        md5OpLogRepository.save(md5OpLog);

        FtpFileLog ftpFileLog = ftpFileLogService.getById(id);
        ftpFileLog.setUpdateTime(new Date());
        if (ftpFileLog == null) {
            log.error("未找到对应的文件记录，id: {}", id);
            return;
        }
        ftpFileLog.setUpdateTime(new Date());
        // 如果返回的消息是checking，修改状态后直接返回
        if (result.equals("checking")) {
            ftpFileLog.setStatus(FtpFileLogStatus.checking.getStatus());
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return;
        }
        ftpFileLog.setVerifyTime(new Date());
        // 判断当前被校验的文件还在不在
        if (!new File(filepath).exists()
                && !StrUtil.equals(ftpFileLog.getStatus(), FtpFileLogStatus.uploaded.getStatus())) {
            log.error("Info in handleVerificationResult: Err filePath! file not Found! filepath = {}", filepath);
            ftpFileLog.setStatus(FtpFileLogStatus.deleted.getStatus());
            ftpFileLog.setFailCause("file not exists");
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return;
        }

        // 判断md5是否生成成功
        if (!result.equals("success")) {
            log.error("Get md5 from md5 module failed, status:{}", cause);
            ftpFileLog.setStatus(FtpFileLogStatus.checkFail.getStatus());
            ftpFileLog.setFailCause(cause);
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return;
        }

        // 判断发送过来的md5是否为空
        if (StrUtil.isBlank(md5)) {
            log.error("Info in handleVerificationResult: Err md5! md5 is blank!");
            ftpFileLog.setStatus(FtpFileLogStatus.checkFail.getStatus());
            ftpFileLog.setFailCause("get md5 value from md5 module failed!");
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return;
        }

        // 判断md5是否一致
        if (!StrUtil.equals(md5, ftpFileLog.getMd5FileContent())) {
            log.error("Info in handleVerificationResult: Err md5! md5 is not equals! md5 = {}, ftpFileLog.md5 = {}", md5, ftpFileLog.getMd5());
            ftpFileLog.setStatus(FtpFileLogStatus.checkFail.getStatus());
            // 把后端计算的md5保存
            ftpFileLog.setMd5(md5);
            ftpFileLog.setFailCause("Inconsistent MD5");
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return;
        }

        Optional<Data> dataOptional = dataRepository.findFirstUnarchivedByMd5(ftpFileLog.getMd5(), ftpFileLog.getCreator());
        if (dataOptional.isPresent()) {
            // 已经有了相同md5的数据
            ftpFileLog.setStatus(FtpFileLogStatus.checkFail.getStatus());
            ftpFileLog.setMd5(md5);
            ftpFileLog.setFailCause("Already have same md5 Unarchived Data, please delete this file in your ftp home!");
            ftpFileLogService.saveOrUpdate(ftpFileLog);
            return;
        }

        ftpFileLog.setMd5(md5);
        ftpFileLog.setStatus(FtpFileLogStatus.checkSuccess.getStatus());
        ftpFileLog.setFailCause(null);
        ftpFileLogService.saveOrUpdate(ftpFileLog);

        // 通知移动文件
        transferFtpFileToNode(ftpFileLog.getCreator(), filepath);
    }

    private void transferFtpFileToNode(String creator, String filepath) {
        R<MemberDTO> r = remoteMemberService.getOneMemberByMemberId(creator, "FtpUser", SecurityConstants.INNER);
        MemberDTO member = r.getData();

        File source = new File(filepath);
        if (!source.exists()) {
            throw new ServiceException("source path :" + filepath + " not exits");
        }
        if (!source.isFile()) {
            throw new ServiceException("source path :" + filepath + " is not file");
        }


        String id = IdUtils.getShortUUID();
        String dataHome = DirConstants.DATA_HOME;

        String yyyyMMdd = DateUtil.format(new Date(), "yyyyMMdd");
        String extension = FilenameUtils.getExtension(filepath);
        extension = StrUtil.isNotBlank(extension) ? "." + extension : "";
        // 使用雪花id当作文件名
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        String relativePath = DirectoryEnum.data.name() + File.separator
                + yyyyMMdd + File.separator
                + nextIdStr + extension;
        String destPath = dataHome + File.separator + relativePath;
        String logPath = dataHome + File.separator + "rsync_log" + File.separator + yyyyMMdd + File.separator + id + ".out";

        File logFile = new File(logPath);
        if (!logFile.getParentFile().exists()) {
            logFile.getParentFile().mkdirs();
        }

        File destFile = new File(destPath);
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }

        TransferTask task = new TransferTask();
        task.setId(id);
        task.setSourceName(source.getName());
        task.setSourcePath(filepath);
        task.setCreator(member.getId());
        task.setCommand(String.format(RSYNC_CMD, filepath, destPath, logPath));
        task.setLogPath(logPath);
        task.setDestPath(destPath);
        task.setUserId(member.getId());
        task.setCreateTime(new Date());
        task.setStatus(ProgressEnums.waiting.name());
        task.setAnalyzeTaskId(null);
        task.setUploadType(UploadType.ftp.name());
        transferTaskService.save(task);

        BusinessTaskMsg msg = new BusinessTaskMsg(BussinessTaskTypeEnum.ftp_upload.name(), id);
        messageSender.sendDelayMsg("business_task_key", msg);
    }
}
