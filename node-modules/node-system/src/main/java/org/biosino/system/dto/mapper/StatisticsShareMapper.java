package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsShare;
import org.biosino.system.vo.ShareStatVO;
import org.biosino.system.vo.excel.ShareExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface StatisticsShareMapper extends CommonMapper {
    StatisticsShareMapper INSTANCE = Mappers.getMapper(StatisticsShareMapper.class);

    @Mapping(source = "shareFileSize", target = "shareFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "reviewFileSize", target = "reviewFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "requestFileSize", target = "requestFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "requestAuthorizedFileSize", target = "requestAuthorizedFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "gsaFileSize", target = "gsaFileSize", qualifiedByName = "formatGBSize")
    ShareExcel dbToExcel(StatisticsShare data);

    ShareExcel dbToExcelNoFormat(StatisticsShare data);

    @Mapping(source = "shareFileSize", target = "shareFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "reviewFileSize", target = "reviewFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "requestFileSize", target = "requestFileSize", qualifiedByName = "readableFileSize")
    ShareStatVO dbToVO(StatisticsShare data);

}
