package org.biosino.system.vo.fd;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 特殊数据集 node前端首页统计数据
 *
 * <AUTHOR>
 */
@Data
public class FdHomeStatVO implements Serializable {
    private List<FdStatItem> humanResourceStat;
    private List<FdStatItem> microbeResourceStat;
    private List<FdStatItem> omicsResourceStat;

    @Data
    public static class FdStatItem implements Serializable {
        private String name;
        private String svgName;
        private int number = 0;
        private int sapNumber = 0;
        private long size = 0;
        private String readSize = "";
        private String readSizeUnit = "";

        private String cat1Val;
//        private StatDTO statDTO;

        public void setSize(long size) {
            final String str = FileUtil.readableFileSize(size);
            int i = str.indexOf(StrUtil.SPACE);
            if (i > -1) {
                setReadSize(str.substring(0, i));
                setReadSizeUnit(str.substring(i + StrUtil.SPACE.length()));
            } else {
                setReadSize(str);
            }
            this.size = size;
        }
    }

    public static String initIco(String name) {
        if (name == null) {
            return StrUtil.EMPTY;
        }
        // 驼峰、空格、短横线转下划线，然后转小写
        return StrUtil.toUnderlineCase(name.replace(" ", "_").replace("-", "_")).toLowerCase();
    }

}
