package org.biosino.system.vo.metadata;

import lombok.Data;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/12
 */
@Data
public class AnalysisVO {
    private String analysisNo;
    private String name;
    private String description;
    private String analysisType;
    private String customAnalysisType;
    private List<String> usedIds;
    private List<AnalysisTarget> target;
    private List<CustomTarget> customTarget;
    private List<Pipeline> pipeline;
    private List<PublishVO> publish;
    private String creator;

    private Submitter submitter;
}
