package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * 样本基础统计
 *
 * <AUTHOR>
 */
@Data
public class SampleExcel {
    // 月份
    @Excel(name = "Month")
    private String month;

    @Excel(name = "Type")
    private String type;

    @Excel(name = "Total", cellType = Excel.ColumnType.NUMERIC)
    private Long total = 0L;

    @Excel(name = "Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long accessibleNum = 0L;

    @Excel(name = "UnAccessible", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessibleNum = 0L;

    @Excel(name = "Total Growth", cellType = Excel.ColumnType.NUMERIC)
    private Long totalGrowth = 0L;

    @Excel(name = "Accessible Growth", cellType = Excel.ColumnType.NUMERIC)
    private Long accessibleGrowth = 0L;

    @Excel(name = "UnAccessible Growth", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessibleGrowth = 0L;

    @Excel(name = "Total File Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double totalFileSize = 0D;

    @Excel(name = "Accessible File Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double accessibleFileSize = 0D;

    @Excel(name = "UnAccessible File Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessibleFileSize = 0D;

    @Excel(name = "Total File Size Growth (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double totalFileSizeGrowth = 0D;

    @Excel(name = "Accessible File Size Growth (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double accessibleFileSizeGrowth = 0D;

    @Excel(name = "UnAccessible File Size Growth (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessibleFileSizeGrowth = 0D;

    // 分子：SAMPLE_TYPE_HUMAN_007 分母：DATA_VOLUMN_RAW_DATA_008
    @Excel(name = "Total File Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float totalFileSizeRate = 0F;

    // 分子：SAMPLE_TYPE_HUMAN_001 分母：DATA_VOLUMN_SAMPLE_009
    @Excel(name = "Total Quantity Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float totalRate = 0F;

}
