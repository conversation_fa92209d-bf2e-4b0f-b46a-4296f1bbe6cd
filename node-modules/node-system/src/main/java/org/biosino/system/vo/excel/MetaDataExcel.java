package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
public class MetaDataExcel {
    @Excel(name = "Month")
    private String month;

    @Excel(name = "Project Total", cellType = Excel.ColumnType.NUMERIC)
    private Long projTotal = 0L;
    @Excel(name = "Project Accessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long projAccessible = 0L;
    @Excel(name = "Project UnAccessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long projUnAccessible = 0L;
    @Excel(name = "Project Accessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float projAccessiblePercentage = 0F;
    @Excel(name = "Project UnAccessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float projUnAccessiblePercentage = 0F;
    @Excel(name = "Project Total Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long projTotalIncrement = 0L;
    @Excel(name = "Project Accessible Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long projAccessibleIncrement = 0L;

    @Excel(name = "Experiment Total", cellType = Excel.ColumnType.NUMERIC)
    private Long expTotal = 0L;
    @Excel(name = "Experiment Accessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long expAccessible = 0L;
    @Excel(name = "Experiment UnAccessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long expUnAccessible = 0L;
    @Excel(name = "Experiment Accessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float expAccessiblePercentage = 0F;
    @Excel(name = "Experiment UnAccessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float expUnAccessiblePercentage = 0F;
    @Excel(name = "Experiment Total Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long expTotalIncrement = 0L;
    @Excel(name = "Experiment Accessible Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long expAccessibleIncrement = 0L;

    @Excel(name = "Sample Total", cellType = Excel.ColumnType.NUMERIC)
    private Long sapTotal = 0L;
    @Excel(name = "Sample Accessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long sapAccessible = 0L;
    @Excel(name = "Sample UnAccessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long sapUnAccessible = 0L;
    @Excel(name = "Sample Accessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float sapAccessiblePercentage = 0F;
    @Excel(name = "Sample UnAccessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float sapUnAccessiblePercentage = 0F;
    @Excel(name = "Sample Total Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long sapTotalIncrement = 0L;
    @Excel(name = "Sample Accessible Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long sapAccessibleIncrement = 0L;

    @Excel(name = "Run Total", cellType = Excel.ColumnType.NUMERIC)
    private Long runTotal = 0L;
    @Excel(name = "Run Accessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long runAccessible = 0L;
    @Excel(name = "Run UnAccessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long runUnAccessible = 0L;
    @Excel(name = "Run Accessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float runAccessiblePercentage = 0F;
    @Excel(name = "Run UnAccessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float runUnAccessiblePercentage = 0F;
    @Excel(name = "Run Total Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long runTotalIncrement = 0L;
    @Excel(name = "Run Accessible Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long runAccessibleIncrement = 0L;

    @Excel(name = "Analysis Total", cellType = Excel.ColumnType.NUMERIC)
    private Long analTotal = 0L;
    @Excel(name = "Analysis Accessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long analAccessible = 0L;
    @Excel(name = "Analysis UnAccessible Total", cellType = Excel.ColumnType.NUMERIC)
    private Long analUnAccessible = 0L;
    @Excel(name = "Analysis Accessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float analAccessiblePercentage = 0F;
    @Excel(name = "Analysis UnAccessible Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float analUnAccessiblePercentage = 0F;
    @Excel(name = "Analysis Total Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long analTotalIncrement = 0L;
    @Excel(name = "Analysis Accessible Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long analAccessibleIncrement = 0L;

}
