package org.biosino.system.dto.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SamToolTaskQueryDTO extends BaseQuery {
    private String status;
    private Integer priority;
    private List<String> dataNos;
    private List<String> expNos;
    private List<String> sapNos;
    private List<String> analNos;
    private List<String> subNos;
    private String failCause;
    private Integer exitCode;

    public void checkNonQuery() {
        if (StrUtil.isBlank(this.status)
                && priority == null
                && CollUtil.isEmpty(this.dataNos)
                && CollUtil.isEmpty(this.expNos)
                && CollUtil.isEmpty(this.sapNos)
                && CollUtil.isEmpty(this.analNos)
                && CollUtil.isEmpty(this.subNos)
                && StrUtil.isBlank(this.failCause)
                && exitCode == null) {
            throw new ServiceException("Please enter at least one query condition");
        }
    }
}
