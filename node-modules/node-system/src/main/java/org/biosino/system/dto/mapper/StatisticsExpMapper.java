package org.biosino.system.dto.mapper;

import cn.hutool.core.io.FileUtil;
import org.biosino.common.core.utils.NodeUtils;
import org.biosino.common.mongo.entity.statistics.StatisticsExp;
import org.biosino.system.vo.ExpStatVO;
import org.biosino.system.vo.excel.ExperimentExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
@Mapper
public interface StatisticsExpMapper extends CommonMapper {
    StatisticsExpMapper INSTANCE = Mappers.getMapper(StatisticsExpMapper.class);

    @Mapping(source = "totalFileSize", target = "totalFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "accessibleFileSize", target = "accessibleFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "unAccessibleFileSize", target = "unAccessibleFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "totalFileSizeGrowth", target = "totalFileSizeGrowth", qualifiedByName = "formatSize")
    @Mapping(source = "accessibleFileSizeGrowth", target = "accessibleFileSizeGrowth", qualifiedByName = "formatSize")
    @Mapping(source = "unAccessibleFileSizeGrowth", target = "unAccessibleFileSizeGrowth", qualifiedByName = "formatSize")
    ExperimentExcel dbToExcel(StatisticsExp data);

    @Mapping(source = "totalFileSize", target = "totalFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "accessibleFileSize", target = "accessibleFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "unAccessibleFileSize", target = "unAccessibleFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "totalFileSizeGrowth", target = "totalFileSizeGrowth", qualifiedByName = "readableFileSize")
    @Mapping(source = "accessibleFileSizeGrowth", target = "accessibleFileSizeGrowth", qualifiedByName = "readableFileSize")
    @Mapping(source = "unAccessibleFileSizeGrowth", target = "unAccessibleFileSizeGrowth", qualifiedByName = "readableFileSize")
    ExpStatVO dbToVO(StatisticsExp data);

    List<ExpStatVO> dbToVO(List<StatisticsExp> data);

}
