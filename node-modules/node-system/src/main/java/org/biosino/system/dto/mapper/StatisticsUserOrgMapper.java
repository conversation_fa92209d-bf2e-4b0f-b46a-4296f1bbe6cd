package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsUserOrg;
import org.biosino.system.vo.excel.UserOrgStatExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/5
 */
@Mapper
public interface StatisticsUserOrgMapper extends CommonMapper {
    StatisticsUserOrgMapper INSTANCE = Mappers.getMapper(StatisticsUserOrgMapper.class);

    @Mapping(source = "rawFileSize", target = "rawFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "rawPrivateFileSize", target = "rawPrivateFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "analysisFileSize", target = "analysisFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "analysisPrivateFileSize", target = "analysisPrivateFileSize", qualifiedByName = "formatGBSize")
    UserOrgStatExcel dbToExcel(StatisticsUserOrg data);

    List<UserOrgStatExcel> dbToExcel(List<StatisticsUserOrg> data);

}
