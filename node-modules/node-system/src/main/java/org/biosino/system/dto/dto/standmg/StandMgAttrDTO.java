package org.biosino.system.dto.dto.standmg;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.annotation.Excel;
import org.biosino.common.core.enums.dict.BaseAttrType;
import org.biosino.common.core.enums.dict.ExpSampleDataType;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.validator.ValidEnum;
import org.biosino.common.mongo.entity.ExpSampleType;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StandMgAttrDTO implements Serializable {

    private String id;

    @ValidEnum(enumClass = ExpSampleTypeEnum.class, allowNull = false, message = "Illegal type")
    private String type;

    /**
     * 外层字典id
     */
    @NotBlank(message = "The standard id cannot be empty")
    private String standId;

    @Excel(name = "Attribute Name")
    @NotBlank(message = "The attribute name cannot be empty")
    @Pattern(regexp = "^(?!\\s+$)[-()/\\w ]{1,60}$", message = "Illegal attribute name format")
    private String attributesName;

    @Excel(name = "Attribute Field")
    @NotBlank(message = "The attribute field cannot be empty")
    @Pattern(regexp = "^(?!\\s+$)[-()/\\w ]{1,60}$", message = "Illegal attribute field format")
    private String attributesField;

    @Excel(name = "Description", wrapText = true, height = 20)
    private String description;

    @Excel(name = "Chinese Description", wrapText = true, height = 20)
    private String zhDescription;

    @Excel(name = "Value Format")
    private String valueFormat;

    @Excel(name = "Value Regex")
    private String valueRegex;

    // sample中的分组
    @Excel(name = "Group")
    private Integer group;

    @Excel(name = "Sort")
    @NotNull(message = "The sort num cannot be empty")
    private Integer sort;

    @Excel(name = "Status", combo = {"disable", "enable"})
    @ValidEnum(enumClass = DataStatusEnum.class, allowNull = false, message = "Illegal attribute status")
    private String status;

    /**
     * @see BaseAttrType
     */
    @Excel(name = "Required", combo = {"optional", "required", "recommend", "none"})
    @ValidEnum(enumClass = BaseAttrType.class, allowNull = false, message = "Illegal required type")
    private String required;

    /**
     * @see ExpSampleDataType
     */
    @ValidEnum(enumClass = ExpSampleDataType.class, allowNull = false, message = "Illegal attribute input type")
    private String dataType;

    /**
     * excel 导入（导出）时使用
     *
     * @see ExpSampleDataType
     */
    @Excel(name = "Input Type", combo = {"Input", "Select", "Two Level Select", "Integer", "Float", "Date", "Textarea", "Custom"})
    private String dataTypeStr;

    private List<Object> valueRange;

    /**
     * 下拉框数据范围值，使用换行符分割
     */
    @Excel(name = "Select values", wrapText = true)
    private String selectStr;

    /**
     * 二级下拉框数据范围值，使用json格式
     */
    @Excel(name = "JSON of Two Level Select", width = 22)
    private String jsonStr;

    // 下拉框数据是否允许自定义内容
    private boolean allowCreate = false;

    @Excel(name = "Allow custom input", width = 20, combo = {"TRUE", "FALSE"})
    private String allowCreateStr = Boolean.FALSE.toString().toUpperCase();

    /**
     * Custom类型下 数据来源
     */
    @Excel(name = "Data Source of Custom type", width = 23)
    private String dataSource;


    private ExpSampleType expSampleType;

}
