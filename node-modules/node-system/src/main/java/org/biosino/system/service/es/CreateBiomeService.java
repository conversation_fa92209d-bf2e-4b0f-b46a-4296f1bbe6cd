package org.biosino.system.service.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.es.entity.BiomeEs;
import org.biosino.common.es.mapper.BiomeMapper;
import org.biosino.common.mongo.entity.Biome;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class CreateBiomeService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BiomeMapper biomeMapper;

    private void reCreateIndex() {
        final Boolean exists = biomeMapper.existsIndex(BiomeEs.BIOME_INDEX_NAME);
        if (Boolean.TRUE.equals(exists)) {
            biomeMapper.deleteIndex(BiomeEs.BIOME_INDEX_NAME);
        }
        biomeMapper.createIndex();
    }


    /**
     * 创建dict_biome索引
     */
    public void createBiome(String type) {
        if (StrUtil.isBlank(type)) {
            return;
        }
        // 重建索引
        reCreateIndex();

        Query query = new Query();
        query.fields().include("type").include("value");
        long total = mongoTemplate.count(query, Biome.class);
        // 单次读取数量
        int size = 30000;
        // 读取次数
        long page = total / size;
        int skip;
        for (int i = 0; i <= page; i++) {
            skip = i * size;
            List<Biome> biomes = mongoTemplate.find(query.limit(size).skip(skip), Biome.class);

            if (CollUtil.isEmpty(biomes)) {
                break;
            }

            List<BiomeEs> saveList = new ArrayList<>();
            for (Biome item : biomes) {
                if (StrUtil.isBlank(item.getValue())) {
                    continue;
                }
                BiomeEs esItem = new BiomeEs();
                BeanUtil.copyProperties(item, esItem);
                saveList.add(esItem);
            }
            biomeMapper.insertBatch(saveList);
            log.warn("Dict Biome index synchronized：{}", (i + 1) * size);
        }
        log.warn("Dict Biome index synchronization completed");
    }

}

