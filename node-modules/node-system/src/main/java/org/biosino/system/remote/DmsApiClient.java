package org.biosino.system.remote;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(value = "dmsApiClient", url = "${node.api.dms-api:https://idc.biosino.org/dms}")
public interface DmsApiClient {
    String EXTRA_FIELDS_LINEAGE = "lineage";

    /*@GetMapping("/ref/dict/brief/entities")
    String getEntitiesByAbbreviation(@RequestParam("abbreviation") String abbreviation,
                                     @RequestParam("currentPage") int currentPage,
                                     @RequestParam("pageSize") int pageSize);*/

    @GetMapping("/ref/dict/brief/entities")
    String getEntitiesByAbbreviation(@RequestParam("abbreviation") String abbreviation,
                                     @RequestParam("currentPage") int currentPage,
                                     @RequestParam("pageSize") int pageSize,
                                     @RequestParam("extraFields") String extraFields);
}
