package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

@Data
public class TempDataExcel {
    // 月份
    @Excel(name = "Month")
    private String month;

    // 存量 DATA_UNARCHIVED_001，data表中archived为“no”的记录数
    @Excel(name = "Unarchived File", cellType = Excel.ColumnType.NUMERIC)
    private Long unarchivedFile = 0L;

    // 存量 DATA_UNARCHIVED_002，DATA_UNARCHIVED_001 记录的file size之和
    @Excel(name = "Unarchived File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unarchivedFileSize = 0D;

    // 存量 DATA_UNARCHIVED_003，data表中archived为“no” 且 submission_date超过6个月 的记录数
    @Excel(name = "Unarchived 6 Month File", cellType = Excel.ColumnType.NUMERIC)
    private Long unarchived6MonthFile = 0L;

    // 存量 DATA_UNARCHIVED_004，DATA_UNARCHIVED_003 记录的file size之和
    @Excel(name = "Unarchived 6 Month File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unarchived6MonthFileSize = 0D;

    // 存量 DATA_UNARCHIVED_005，data表中archived为“no” 且 submission_date超过12个月 的记录数
    @Excel(name = "Unarchived 1 Year File", cellType = Excel.ColumnType.NUMERIC)
    private Long unarchived1YearFile = 0L;

    // 存量 DATA_UNARCHIVED_006，DATA_UNARCHIVED_005 记录的file size之和
    @Excel(name = "Unarchived 1 Year File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unarchived1YearFileSize = 0D;

    // 存量 DATA_PRIVATE_001，data表中security为“Private”的记录数
    @Excel(name = "UnAccessible File", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessibleFile = 0L;

    // 存量 DATA_PRIVATE_002，DATA_PRIVATE_001 记录的file size之和
    @Excel(name = "UnAccessible File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessibleFileSize = 0D;

    // 存量 DATA_PRIVATE_003，data表中security为“Private” 且 submission_date超过6个月 的记录数
    @Excel(name = "UnAccessible 6 Month File", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessible6MonthFile = 0L;

    // 存量 DATA_PRIVATE_004，DATA_PRIVATE_003 记录的file size之和
    @Excel(name = "UnAccessible 6 Month File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessible6MonthFileSize = 0D;

    // 存量 DATA_PRIVATE_005，data表中security为“Private” 且 submission_date超过12个月 的记录数
    @Excel(name = "UnAccessible 1 Year File", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessible1YearFile = 0L;

    // 存量 DATA_PRIVATE_006，DATA_PRIVATE_005 记录的file size之和
    @Excel(name = "UnAccessible 1 Year File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessible1YearFileSize = 0D;
}
