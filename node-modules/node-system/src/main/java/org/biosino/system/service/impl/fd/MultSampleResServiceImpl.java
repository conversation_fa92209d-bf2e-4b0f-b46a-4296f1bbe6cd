package org.biosino.system.service.impl.fd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.EsTreeItemDTO;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.biosino.common.security.utils.ExpSampleTokenUtils;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.es.api.dto.FdMultipleResDTO;
import org.biosino.es.api.dto.FeatureDataPrjDTO;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.MultipleSampleVO;
import org.biosino.system.repository.MultipleSampleResourceRepository;
import org.biosino.system.service.fd.IMultSampleResService;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.system.service.impl.fd.SingleSapResServiceImpl.removeNoPermission;

/**
 * 特殊数据集 多样本
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MultSampleResServiceImpl implements IMultSampleResService {
    private final RemoteNodeESSearchService remoteNodeESSearchService;
    private final MultipleSampleResourceRepository multipleSampleResourceRepository;

    @Override
    public PageImpl<MultipleSampleResource> list(MultipleSampleQueryVO searchVO) {
        return multipleSampleResourceRepository.multSampleList(searchVO);
    }

    @Override
    public MultipleSampleVO dialogList(MultipleSampleQueryVO searchVO) {
        final R<FdMultipleResDTO> resultR = remoteNodeESSearchService.searchMultipleSample(searchVO, SecurityConstants.INNER);
        if (resultR == null || R.isError(resultR)) {
            return null;
        }
        final FdMultipleResDTO data = resultR.getData();
        final MultipleSampleVO vo = new MultipleSampleVO();

        // 删除没有权限的样本类型
        List<EsTreeItemDTO> esTreeItems = data.getEsTreeItems();
        removeNoPermission(esTreeItems, ExpSampleTokenUtils.getAllSampleName());
        vo.setEsTreeItems(esTreeItems);

        vo.setDefaultCheckedKeys(data.getDefaultCheckedKeys());
        final EsPageInfo<FeatureDataPrjDTO> pageInfo = data.getPageInfo();
        if (pageInfo != null) {
            final List<FeatureDataPrjDTO> list = pageInfo.getList();
            final List<MultipleSampleResource> listVo = new ArrayList<>();
            if (CollUtil.isNotEmpty(list)) {
                for (FeatureDataPrjDTO dto : list) {
                    final MultipleSampleResource res = new MultipleSampleResource();
                    res.setId(dto.getTypeId());
                    res.setProjID(dto.getTypeId());
                    res.setProjName(dto.getName());
                    res.setSapTypes(dto.getRelaSampleType());
                    res.setSubmitter(dto.getSubName());
                    res.setModifiedDate(dto.getModifiedDate());
                    res.setDes(dto.getDescription());
                    listVo.add(res);
                }
            }
            final EsPageInfo<MultipleSampleResource> page = EsPageInfo.of(listVo);
            page.setTotal(pageInfo.getTotal());
            vo.setPageInfo(page);
        }
        return vo;
    }

    /**
     * 保存
     */
    @Override
    public boolean save(List<String> prjIds, String username) {
        if (CollUtil.isEmpty(prjIds)) {
            throw new ServiceException("未找到项目");
        }
        final List<String> uploadIds = new ArrayList<>(prjIds);
        final R<List<FeatureDataPrjDTO>> prjResult = remoteNodeESSearchService.searchMultSampleByPrjNos(prjIds, SecurityConstants.INNER);
        if (prjResult == null || R.isError(prjResult)) {
            throw new ServiceException("es api错误");
        }
        final List<FeatureDataPrjDTO> data = prjResult.getData();
        if (CollUtil.isEmpty(data)) {
            throw new ServiceException("未找到项目");
        }

        prjIds = data.stream().map(FeatureDataPrjDTO::getTypeId).collect(Collectors.toList());
        uploadIds.removeAll(prjIds);
        if (CollUtil.isNotEmpty(uploadIds)) {
            throw new ServiceException("找不到项目id: " + JSON.toJSONString(uploadIds));
        }

        final Map<String, MultipleSampleResource> resMap = multipleSampleResourceRepository.findMapByProjIDIn(prjIds);
        final List<MultipleSampleResource> newData = new ArrayList<>();
        final Date now = new Date();
        for (FeatureDataPrjDTO item : data) {
            final String projectNo = item.getTypeId();
            if (!resMap.containsKey(projectNo)) {
                final MultipleSampleResource res = new MultipleSampleResource();
                res.setProjID(projectNo);
                res.setProjName(item.getName());
                res.setSubmitter(item.getSubName());
                res.setDes(item.getDescription());
                res.setSapTypes(item.getRelaSampleType());
                res.setStatus(DataStatusEnum.disable.name());
                res.setCreator(username);
                res.setCreateTime(now);
                res.setModifiedDate(now);
                newData.add(res);
            }
        }
        if (CollUtil.isNotEmpty(newData)) {
            multipleSampleResourceRepository.saveAll(newData);
        }
        return true;
    }

    /**
     * 导出
     */
    @Override
    public void download(HttpServletRequest request, HttpServletResponse response) {
        final List<MultipleSampleResource> all = multipleSampleResourceRepository.findAll();
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("没有数据");
        }
        File multSampleFile = null;
        BigExcelWriter bigWriter = null;
        try {
            multSampleFile = Files.createTempFile("multSample", ".xlsx").toFile();
            if (multSampleFile.exists()) {
                multSampleFile.delete();
            }
            bigWriter = cn.hutool.poi.excel.ExcelUtil.getBigWriter(multSampleFile);
            List<Map<String, Object>> rows = new ArrayList<>();
            for (MultipleSampleResource item : all) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("Project ID", item.getProjID());
                row.put("Sample Type", CollUtil.join(item.getSapTypes(), "、"));
                row.put("Project Name", item.getProjName());
                row.put("Description", item.getDes());
                row.put("Submitter", item.getSubmitter());
                row.put("Modified Date", DateUtils.formatDateTime(item.getModifiedDate()));
                rows.add(row);
            }
            bigWriter.write(rows, true);
            bigWriter.close();
            DownloadUtils.download(request, response, multSampleFile);
        } catch (Exception e) {
            log.error("多组学资源数据导出出错", e);
        } finally {
            IoUtil.close(bigWriter);
            if (multSampleFile != null && multSampleFile.isFile()) {
                FileUtil.del(multSampleFile);
            }
        }
    }

    @Override
    public boolean batchUpdateStatus(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        List<MultipleSampleResource> list;
        if (CollUtil.isNotEmpty(ids)) {
            list = multipleSampleResourceRepository.findAllByIdIn(ids);
        } else {
            list = multipleSampleResourceRepository.findAll();
        }
        final boolean disable = featureDataDTO.isDisable();
        final Date now = new Date();
        for (MultipleSampleResource item : list) {
            item.setStatus(disable ? DataStatusEnum.disable.name() : DataStatusEnum.enable.name());
            item.setModifiedDate(now);
        }
        multipleSampleResourceRepository.saveAll(list);
        return true;
    }

    /**
     * 批量添加
     */
    @Override
    public boolean batchImport(FeatureDataDTO featureDataDTO, String username) {
        final String batchImportNosStr = StrUtil.trimToNull(featureDataDTO.getBatchImportNosStr());
        if (batchImportNosStr == null) {
            throw new ServiceException("项目ID不能为空");
        }
        final List<String> prjIds = Arrays.stream(batchImportNosStr.split("\n")).map(StrUtil::trimToNull).filter(Objects::nonNull).collect(Collectors.toList());
        save(prjIds, username);
        return true;
    }

    /**
     * 批量删除
     */
    @Override
    public boolean batchDelete(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            multipleSampleResourceRepository.deleteByIdIn(ids);
        }
        return true;
    }

}
