package org.biosino.system.mapper;

import java.util.List;

import org.biosino.system.api.domain.SysOperLog;

/**
 * 操作日志 数据层
 *
 * <AUTHOR>
 */
public interface SysOperLogMapper {
    /**
     * 新增操作日志
     *
     * @param operLog 操作日志对象
     */
    public int insertOperlog(SysOperLog operLog);

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    public List<SysOperLog> selectOperLogList(SysOperLog operLog);

    /**
     * 批量删除系统操作日志
     *
     * @param ids 需要删除的操作日志ID
     * @return 结果
     */
    public int deleteOperLogByIds(Long[] ids);

    /**
     * 查询操作日志详细
     *
     * @param id 操作ID
     * @return 操作日志对象
     */
    public SysOperLog selectOperLogById(Long id);

    /**
     * 清空操作日志
     */
    public void cleanOperLog();
}
