package org.biosino.system.service.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.es.entity.DiseaseEs;
import org.biosino.common.es.mapper.DiseaseMapper;
import org.biosino.common.mongo.entity.HumanDiseaseOntology;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static org.biosino.common.es.entity.DiseaseEs.DISEASE_INDEX_NAME;

@Slf4j
@Service
public class CreateDiseaseService {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private DiseaseMapper diseaseMapper;

    private void reCreateIndex() {
        final Boolean exists = diseaseMapper.existsIndex(DISEASE_INDEX_NAME);
        if (Boolean.TRUE.equals(exists)) {
            diseaseMapper.deleteIndex(DISEASE_INDEX_NAME);
        }
        diseaseMapper.createIndex();
    }


    /**
     * 创建Disease索引
     */
    public void createDisease() {

        // 重建索引
        reCreateIndex();

        Query query = new Query();
        query.fields().include("lbl");
        long total = mongoTemplate.count(query, HumanDiseaseOntology.class);
        // 单次读取数量
        int size = 30000;
        // 读取次数
        long page = total / size;
        int skip;
        for (int i = 0; i <= page; i++) {
            skip = i * size;
            List<HumanDiseaseOntology> humanDiseaseOntologies = mongoTemplate.find(query.limit(size).skip(skip), HumanDiseaseOntology.class);

            if (CollUtil.isEmpty(humanDiseaseOntologies)) {
                break;
            }

            List<DiseaseEs> platformEsList = new ArrayList<>();
            for (HumanDiseaseOntology diseaseOntology : humanDiseaseOntologies) {
                if (StrUtil.isBlank(diseaseOntology.getLbl())) {
                    continue;
                }
                DiseaseEs diseaseEs = new DiseaseEs();
                BeanUtil.copyProperties(diseaseOntology, diseaseEs);
                platformEsList.add(diseaseEs);
            }
            diseaseMapper.insertBatch(platformEsList);
            log.warn("HumanDiseaseOntology index synchronized：{}", (i + 1) * size);
        }
        log.warn("HumanDiseaseOntology index synchronization completed");
    }

    public void createDisease(String noused) {
        createDisease();
    }
}

