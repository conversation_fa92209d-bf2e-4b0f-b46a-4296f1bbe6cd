package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/8/5
 */
@Data
public class UserOrgStatExcel {

    @Excel(name = "Month")
    private String month;

    @Excel(name = "Organization")
    private String type;

    @Excel(name = "Project", cellType = Excel.ColumnType.NUMERIC)
    private Long prjTotal = 0L;
    @Excel(name = "Experiment", cellType = Excel.ColumnType.NUMERIC)
    private Long expTotal = 0L;
    @Excel(name = "Sample", cellType = Excel.ColumnType.NUMERIC)
    private Long sapTotal = 0L;

    @Excel(name = "Data", cellType = Excel.ColumnType.NUMERIC)
    private Long dataTotal = 0L;

    @Excel(name = "Analysis", cellType = Excel.ColumnType.NUMERIC)
    private Long analysisTotal = 0L;

    @Excel(name = "Raw File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double rawFileSize = 0D;

    @Excel(name = "Analysis File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double analysisFileSize = 0D;

    @Excel(name = "UnAccessible Project", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessiblePrjTotal = 0L;

    @Excel(name = "UnAccessible Analysis", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessibleAnalysisTotal = 0L;

    @Excel(name = "Raw Private File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double rawPrivateFileSize = 0D;

    @Excel(name = "Analysis Private File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double analysisPrivateFileSize = 0D;

}
