package org.biosino.system.dto.dto;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/10/25
 */
@Data
public class ProjectEmailExportDTO {

    @Excel(name = "项目ID", cellType = Excel.ColumnType.STRING)
    private String projectNo;

    @Excel(name = "数据所有者邮箱(NODE)", width = 40, cellType = Excel.ColumnType.STRING)
    private String nodeEmail;

    @Excel(name = "数据提交人邮箱(Submitter)", width = 40, cellType = Excel.ColumnType.STRING)
    private String submitterEmail;

    @Excel(name = "邮箱是否一致", cellType = Excel.ColumnType.STRING)
    private String agreement;

    @Excel(name = "用户ID", width = 40, cellType = Excel.ColumnType.STRING)
    private String userId;
}
