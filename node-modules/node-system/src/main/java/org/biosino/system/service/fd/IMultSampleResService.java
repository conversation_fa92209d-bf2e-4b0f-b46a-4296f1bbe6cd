package org.biosino.system.service.fd;

import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.biosino.es.api.vo.fd.MultipleSampleQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.MultipleSampleVO;
import org.springframework.data.domain.PageImpl;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊数据集 多样本
 *
 * <AUTHOR>
 */
public interface IMultSampleResService {

    PageImpl<MultipleSampleResource> list(MultipleSampleQueryVO searchVO);

    MultipleSampleVO dialogList(MultipleSampleQueryVO searchVO);

    boolean save(List<String> prjIds, String username);

    void download(HttpServletRequest request, HttpServletResponse response);

    boolean batchUpdateStatus(FeatureDataDTO featureDataDTO);

    boolean batchImport(FeatureDataDTO featureDataDTO, String username);

    boolean batchDelete(FeatureDataDTO featureDataDTO);
}
