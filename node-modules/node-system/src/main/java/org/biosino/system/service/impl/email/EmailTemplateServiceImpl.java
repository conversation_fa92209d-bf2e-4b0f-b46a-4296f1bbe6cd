package org.biosino.system.service.impl.email;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.DirectoryEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.bean.BeanUtils;
import org.biosino.common.mongo.entity.email.EmailSendLog;
import org.biosino.common.mongo.entity.email.EmailTemplate;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.EmailWithContentDTO;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.domain.vo.SendEmailVO;
import org.biosino.system.enums.EmailSendStatus;
import org.biosino.system.repository.EmailSendLogRepository;
import org.biosino.system.repository.EmailTemplateRepository;
import org.biosino.system.service.email.IEmailTemplateService;
import org.biosino.system.vo.email.BatchEmailQueryVO;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * Tools - Batch Sending Email
 * 批量发送邮件 服务层
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class EmailTemplateServiceImpl implements IEmailTemplateService {
    private final EmailTemplateRepository emailTemplateRepository;
    private final EmailSendLogRepository emailSendLogRepository;
    private final RemoteMemberService remoteMemberService;
    private final RemoteNotificationService remoteNotificationService;

    /**
     * 邮件模版文件最大大小为5M
     */
    private final static long MAX_TEMPLATE_SIZE = 5L * 1024 * 1024;

    @Override
    public List<EmailTemplate> templateList() {
        /*final File saveDir = templateDir();
        for (EmailTemplate emailTemplate : list) {
            emailTemplate.setFilePreview(tempFilePreview(new File(saveDir, emailTemplate.getRandomFileName())));
        }*/
        return emailTemplateRepository.findAll(Sort.by(Sort.Direction.DESC, "createTime"));
    }

    @Override
    public PageImpl<EmailSendLog> sendLogList(BatchEmailQueryVO queryVO) {
        return emailSendLogRepository.sendLogList(queryVO);
    }

    @Override
    public Map<String, String> uploadEmailTemplate(final MultipartFile file, final String id) {
        if (file == null || file.isEmpty()) {
            throw new ServiceException("模板文件不能为空");
        }
        final String originalFilename = file.getOriginalFilename();
        final String contentType = file.getContentType();
        if (!isFtl(contentType, originalFilename)) {
            throw new ServiceException("仅支持ftl文件");
        }
        final long size = file.getSize();
        if (size > MAX_TEMPLATE_SIZE) {
            throw new ServiceException("模板文件大小不能超过 " + FileUtil.readableFileSize(MAX_TEMPLATE_SIZE));
        }
        /*EmailTemplate emailTemplate = null;
        if (id != null) {
            emailTemplate = emailTemplateRepository.findById(id).orElseThrow(() -> new ServiceException("Data not found"));
        }*/
        final File saveDir = templateDir();
        try {
            final String randomFileName = System.currentTimeMillis() + "_" + RandomUtil.randomString(8) + "." + FileUtil.extName(originalFilename);
            final File saveFile = new File(saveDir, randomFileName);
            saveFile.createNewFile();
            file.transferTo(saveFile);

            final Map<String, String> map = new HashMap<>();
            map.put("templateName", originalFilename);
            map.put("randomFileName", randomFileName);
//            map.put("filePreview", tempFilePreview(saveFile));
            return map;
        } catch (IOException e) {
            log.info("Upload error:", e);
            throw new ServiceException("上传错误：" + e.getMessage());
        }
    }

    private String tempFilePreview(final File file) {
        if (file == null || !file.exists() || !file.isFile()) {
            return null;
        }
        return FileUtil.readUtf8String(file);
    }

    private File templateDir() {
        final File dir = new File(DirConstants.DATA_HOME + File.separator + DirectoryEnum.email.name());
        if (!dir.exists()) {
            dir.mkdirs();
        }
        return dir;
    }

    private File deletExistFile(final String randomFileName) {
        final File saveDir = templateDir();
        if (randomFileName != null) {
            final File oldFile = new File(saveDir, randomFileName);
            if (oldFile.exists() && oldFile.isFile()) {
                FileUtil.del(oldFile);
            }
        }
        return saveDir;
    }

    @Override
    public boolean save(EmailTemplate emailTemplate, final String username) {
        // 删除空白字符
        emailTemplate = BeanUtils.trimStrToNullFields(emailTemplate);
        final String subject = emailTemplate.getSubject();
        final String id = emailTemplate.getId();
        final Date now = new Date();
        final EmailTemplate emailTemplateExist = emailTemplateRepository.findTopBySubject(subject);
        if (id == null) {
            // 新增
            if (emailTemplateExist != null) {
                throw new ServiceException("电子邮件主题已存在");
            }
            emailTemplate.setCreator(username);
            emailTemplate.setCreateTime(now);
        } else {
            // 编辑
            if (emailTemplateExist != null && !id.equals(emailTemplateExist.getId())) {
                throw new ServiceException("电子邮件主题已存在");
            }
            final EmailTemplate emailTemplateSaved = emailTemplateRepository.findById(id).orElseThrow(() -> new ServiceException("数据未找到"));
            final String savedRandomFileName = emailTemplateSaved.getRandomFileName();
            if (savedRandomFileName != null && !savedRandomFileName.equals(emailTemplate.getRandomFileName())) {
                deletExistFile(savedRandomFileName);
            }
            emailTemplate.setEmailNo(emailTemplateSaved.getEmailNo());
            emailTemplate.setCreator(emailTemplateSaved.getCreator());
            emailTemplate.setCreateTime(emailTemplateSaved.getCreateTime());
        }
        emailTemplate.setUpdateTime(now);
        emailTemplateRepository.save(emailTemplate);
        return true;
    }

    @Override
    public String preview(String name) {
        if (StrUtil.isBlank(name)) {
            throw new ServiceException("参数错误");
        }
        if (name.contains("./") || name.contains(".\\")) {
            throw new ServiceException("参数错误");
        }
        final File file = new File(templateDir(), name);
//        return DownloadUtils.downLoadWithResource(file, file.getName(), false, MediaType.TEXT_HTML);
        return tempFilePreview(file);
    }

    /**
     * 发送邮件
     */
    @Override
    public void sendBatchEmail(final SendEmailVO sendEmailVO, final String username) {
        if (sendEmailVO == null || username == null) {
            throw new ServiceException("参数错误");
        }
        final String sendType = sendEmailVO.getSendType();
        if (sendType == null) {
            throw new ServiceException("发送类型错误");
        }
        final boolean sendAll = Boolean.TRUE.equals(sendEmailVO.getAllMember()) && "Batch send".equals(sendType);
        final String recipient = sendEmailVO.getRecipient();
        switch (sendType) {
            case "Batch send":
                if (StrUtil.isBlank(recipient) && !sendAll) {
                    throw new ServiceException("收件人不能为空");
                }
                break;
            case "Single send":
                if (StrUtil.isBlank(recipient)) {
                    throw new ServiceException("收件人不能为空");
                }
                break;
            default:
                throw new ServiceException("发送类型错误");
        }

        final EmailTemplate emailTemplate = emailTemplateRepository.findById(sendEmailVO.getId()).orElseThrow(() -> new ServiceException("模板数据未找到"));
        if (emailTemplate.isSendingAll()) {
            final Date updateTime = emailTemplate.getUpdateTime();
            // 2天以内，不能重复执行全员发送
            if (updateTime.getTime() + (2L * 24 * 60 * 60 * 1000) > System.currentTimeMillis()) {
                // 全员发送中
                throw new ServiceException("当前模板正在向所有成员发送电子邮件");
            }
        }
        // 读取模版内容
        final String templateContent = preview(emailTemplate.getRandomFileName());
        if (StrUtil.isBlank(templateContent)) {
            throw new ServiceException("模板文件内容不能为空");
        }
        // 读取所有node用户email，判断邮箱是否有效
        final R<List<String>> r = remoteMemberService.getNodeMemberEmailList("FtpUser", SecurityConstants.INNER);
        if (R.isError(r) || CollUtil.isEmpty(r.getData())) {
            throw new ServiceException("未找到节点用户");
        }

        // 所有node用户邮箱列表
        final List<String> allNodeEmails = r.getData();
        final Set<String> sendToEmails = new LinkedHashSet<>();
        if (sendAll) {
            // 发给所有用户
//            sendToEmails.add("<EMAIL>");
            // TODO 上线开启
            sendToEmails.addAll(allNodeEmails);
        } else {
            final LinkedHashSet<String> emails = CollUtil.newLinkedHashSet(recipient.split(","));
            final List<String> unknownEmails = new ArrayList<>();
            for (String email : emails) {
                if (!allNodeEmails.contains(email)) {
                    unknownEmails.add(email);
                } else {
                    sendToEmails.add(email);
                }
            }
            if (CollUtil.isNotEmpty(unknownEmails)) {
                throw new ServiceException("非节点用户：" + CollUtil.join(unknownEmails, ", "));
            }
        }
        if (CollUtil.isEmpty(sendToEmails)) {
            throw new ServiceException("未找到收件人电子邮件");
        }
        final Date now = new Date();
        if (sendAll) {
            // 修改全员发送状态
            emailTemplate.setSendingAll(true);
            emailTemplate.setUpdateTime(now);
            emailTemplateRepository.save(emailTemplate);
        }
        // 记录发送日志
        final EmailSendLog emailSendLog = new EmailSendLog();
        emailSendLog.setEmailNo(emailTemplate.getEmailNo());
        emailSendLog.setRecipient(sendAll ? "All members" : recipient);
        final String subject = emailTemplate.getSubject();
        emailSendLog.setSubject(subject);
        emailSendLog.setRemark(emailTemplate.getRemark());
        emailSendLog.setContent(templateContent);
        emailSendLog.setCreator(username);
        emailSendLog.setCreateTime(now);
        emailSendLog.setUpdateTime(now);
        emailSendLog.setStatus(EmailSendStatus.sending.name());
        emailSendLogRepository.save(emailSendLog);
        ThreadUtil.execute(() -> doSendEmail(sendToEmails, subject, sendType, templateContent, emailSendLog, emailTemplate));
    }

    private void doSendEmail(final Set<String> sendToEmails, final String subject, final String sendType,
                             final String templateContent, final EmailSendLog emailSendLog, final EmailTemplate emailTemplate) {
        try {
            long sleepTime;
            switch (sendType) {
                case "Batch send":
                    // 拆分收信人，每次发送不能过多
                    sleepTime = sendToEmails.size() > 400 ? 2 * 60 * 1000L : 20 * 1000L;
                    final List<List<String>> emailGroups = splitColIntoSubList(sendToEmails, 60);
                    for (List<String> emails : emailGroups) {
                        final EmailWithContentDTO dto = new EmailWithContentDTO();
                        dto.setTitle(subject);
                        dto.setContent(templateContent);
                        dto.setToEmails(emails);
                        sendEmailWithContent(dto, sleepTime);
                    }
                    break;
                case "Single send":
                    sleepTime = 5 * 1000L;
                    for (String sendToEmail : sendToEmails) {
                        final R<MemberDTO> userResult = remoteMemberService.getMemberInfoByEmail(sendToEmail, "FtpUser", SecurityConstants.INNER);
                        if (R.isSuccess(userResult) && userResult.getData() != null) {
                            final MemberDTO memberDTO = userResult.getData();
                            final String content = templateContent.replace("${lastName}", memberDTO.getLastName());
                            final EmailWithContentDTO dto = new EmailWithContentDTO();
                            dto.setTitle(subject);
                            dto.setContent(content);
                            dto.setToEmails(CollUtil.toList(sendToEmail));
                            sendEmailWithContent(dto, sleepTime);
                        }
                    }
                    break;
            }

            if (emailTemplate.isSendingAll()) {
                emailTemplate.setSendingAll(false);
                emailTemplate.setUpdateTime(new Date());
                emailTemplateRepository.save(emailTemplate);
            }
            emailSendLog.setStatus(EmailSendStatus.success.name());
            emailSendLog.setUpdateTime(new Date());
            emailSendLogRepository.save(emailSendLog);
        } catch (Exception e) {
            emailSendLog.setStatus(EmailSendStatus.error.name());
            emailSendLog.setUpdateTime(new Date());
            emailSendLog.setErrorMsg(e.getMessage());
            emailSendLogRepository.save(emailSendLog);
            log.info("发送邮件失败", e);
        }
    }

    /**
     * 将给定的集合按每pageSize个元素分割成多个子List，并返回这些子List组成的二维List。
     *
     * @param sourceCol 要分割的原始集合
     * @param pageSize  分割后单个集合的最大大小
     * @return 由多个子List组成的二维List，每个子List包含最多pageSize个元素
     */
    public static <T> List<List<T>> splitColIntoSubList(final Collection<T> sourceCol, final int pageSize) {
        final List<List<T>> listOfSubList = new ArrayList<>();
        if (sourceCol == null || sourceCol.isEmpty() || pageSize < 1) {
            return listOfSubList;
        }
        ArrayList<T> list = new ArrayList<>(sourceCol);
        final int total = sourceCol.size();
        // 计算需要多少个子List来容纳全部元素
        int totalSublists = (int) Math.ceil((double) total / pageSize);
        for (int i = 0; i < totalSublists; i++) {
            // 计算当前子List的起始和结束索引
            int start = i * pageSize;
            int end = Math.min(start + pageSize, total);
            // 使用subList方法获取子List并添加到listOfSublists中
            listOfSubList.add(new ArrayList<>(list.subList(start, end)));
        }
        return listOfSubList;
    }

    private synchronized void sendEmailWithContent(final EmailWithContentDTO dto, final long sleepMils) {
        R r = remoteNotificationService.sendEmailWithContent(dto, SecurityConstants.INNER);
        if (R.isError(r)) {
            throw new ServiceException(r.getMsg());
        }
        // 防止频繁发送邮件导致封号
        ThreadUtil.sleep(sleepMils);
    }

    private boolean isFtl(final String contentType, final String originalFilename) {
        return (MediaType.APPLICATION_OCTET_STREAM_VALUE.equalsIgnoreCase(contentType) || MediaType.TEXT_PLAIN_VALUE.equalsIgnoreCase(contentType))
                && originalFilename != null && originalFilename.toLowerCase().endsWith(".ftl");
    }

}
