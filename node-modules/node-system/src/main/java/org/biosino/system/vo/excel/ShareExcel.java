package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

@Data
public class ShareExcel {
    // 月份
    @Excel(name = "Month")
    private String month;

    private String year;

    @Excel(name = "Share", cellType = Excel.ColumnType.NUMERIC)
    private Long share = 0L;
    @Excel(name = "Share File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double shareFileSize = 0D;
    @Excel(name = "Share File Number", cellType = Excel.ColumnType.NUMERIC)
    private Long shareFileNum = 0L;

    @Excel(name = "Review", cellType = Excel.ColumnType.NUMERIC)
    private Long review = 0L;
    @Excel(name = "Review File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double reviewFileSize = 0D;
    @Excel(name = "Review File Number", cellType = Excel.ColumnType.NUMERIC)
    private Long reviewFileNum = 0L;

    @Excel(name = "Request", cellType = Excel.ColumnType.NUMERIC)
    private Long request = 0L;
    @Excel(name = "Request File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double requestFileSize = 0D;
    @Excel(name = "Request File Number", cellType = Excel.ColumnType.NUMERIC)
    private Long requestFileNum = 0L;

    @Excel(name = "Request Authorized", cellType = Excel.ColumnType.NUMERIC)
    private Long requestAuthorized = 0L;
    @Excel(name = "Request Authorized File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double requestAuthorizedFileSize = 0D;
    @Excel(name = "Request Authorized File Number", cellType = Excel.ColumnType.NUMERIC)
    private Long requestAuthorizedFileNum = 0L;

    @Excel(name = "Gsa", cellType = Excel.ColumnType.NUMERIC)
    private Long gsa = 0L;
    @Excel(name = "Gsa File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double gsaFileSize = 0D;

}
