package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 */
@Data
public class SapMultiExpExcel {

    // 月份
    @Excel(name = "Month")
    private String month;

    // 样本类型（Human）
    @Excel(name = "Type")
    private String type;

    @Excel(name = "Multiple Omics Total", cellType = Excel.ColumnType.NUMERIC)
    private Long multi = 0L;

    @Excel(name = "Single Omics Total", cellType = Excel.ColumnType.NUMERIC)
    private Long single = 0L;

    @Excel(name = "Multiple Omics Quantity Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float multiPercentage = 0F;

    @Excel(name = "Single Omics Quantity Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float singlePercentage = 0F;

    @Excel(name = "Accessible Multiple Omics Total", cellType = Excel.ColumnType.NUMERIC)
    private Long accessibleMulti = 0L;

    @Excel(name = "Multiple Omics File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double multiFileSize = 0D;

    @Excel(name = "Accessible Multiple Omics File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double accessibleMultiFileSize = 0D;

    @Excel(name = "Multiple Omics File Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float multiSizePercentage = 0F;

    @Excel(name = "Accessible Multiple Omics File Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float accessibleMultiSizePercentage = 0F;

}
