package org.biosino.system.service.fd;

import org.biosino.es.api.dto.FdQueryDTO;
import org.biosino.system.dto.dto.MetadataQueryDTO;
import org.biosino.system.vo.fd.FdDetailVO;
import org.biosino.system.vo.fd.FdHomeStatVO;
import org.biosino.system.vo.fd.FdOmicsListVO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
 * 特殊数据集 node前端查询与统计接口
 *
 * <AUTHOR>
 */
public interface IFeatureDataWebService {

    FdHomeStatVO homeStat();

    List<FdHomeStatVO.FdStatItem> getBiomeCuratedStatData();

    FdDetailVO fdResDetail(FdQueryDTO search);

    FdOmicsListVO fdOmicsDetail(FdQueryDTO search);

    void refreshFdCache();

    Page<FdDetailVO.SapInfo> hmdsSapList(MetadataQueryDTO queryDTO);
}
