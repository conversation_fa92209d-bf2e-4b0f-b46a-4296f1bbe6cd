package org.biosino.system.vo.metadata;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@Data
public class PrivateDataExportVO {
    @Excel(name = "Data ID")
    private String datNo;

    @Excel(name = "File Name")
    private String name;

    @Excel(name = "Data Type")
    private String dataType;

    @Excel(name = "Data Security")
    private String security;

    @Excel(name = "Project ID")
    private String projNo;

    @Excel(name = "Experiment ID")
    private String expNo;

    @Excel(name = "Sample ID")
    private String sapNo;

    @Excel(name = "Analysis ID")
    private String analNo;

    @Excel(name = "File Size")
    private String readableFileSize;

    @Excel(name = "Creator Date")
    private String creatorEmail;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
}
