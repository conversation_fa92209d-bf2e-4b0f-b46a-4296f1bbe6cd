package org.biosino.system.dto.dto;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2025/6/30
 */
@Data
public class AnalysisDataExportDTO {

    @Excel(name = "analysis_no")
    private String analysisNo;

    @Excel(name = "analysis_name")
    private String analysisName;

    @Excel(name = "analysis_type")
    private String analysisType;

    @Excel(name = "data_no")
    private String dataNo;

    @Excel(name = "data_name")
    private String dataName;

    @Excel(name = "data_type")
    private String dataType;

    @Excel(name = "security")
    private String security;

    @Excel(name = "md5")
    private String md5;

    @Excel(name = "file_size")
    private String fileSize;

    @Excel(name = "data_path")
    private String dataPath;
}
