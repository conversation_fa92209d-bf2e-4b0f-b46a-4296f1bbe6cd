package org.biosino.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.HttpStatus;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.web.page.TableDataInfo;
import org.biosino.common.redis.service.RedisService;
import org.biosino.system.api.domain.sftp.FtpHomeFile;
import org.biosino.system.dto.dto.FtpFileLogQueryDTO;
import org.biosino.system.mapper.FtpHomeFileMapper;
import org.biosino.system.service.IFtpHomeFileService;
import org.biosino.system.vo.metadata.FtpHomeFileVO;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/5/9
 */
@Service
@RequiredArgsConstructor
public class FtpHomeFileServiceImpl extends ServiceImpl<FtpHomeFileMapper, FtpHomeFile> implements IFtpHomeFileService {


    private final RedisService redisService;

    private final static String SCAN_DISK_TASK_KEY = "scan_disk_task_key";

    private final static Long EXPIRE_TIME = 120L;

    /**
     * 扫描sftp目录文件
     */
    @Override
    public synchronized void scanFtpHomeFile() {

        if (redisService.getCacheObject(SCAN_DISK_TASK_KEY) != null) {
            throw new ServiceException("正在扫描目录，请稍后重试");
        }

        redisService.setCacheObject(SCAN_DISK_TASK_KEY, true, EXPIRE_TIME, TimeUnit.MINUTES);
        // 保存之前清空数据库
        this.remove(Wrappers.emptyWrapper());
        // 异步执行
        ThreadUtil.execute(() -> {
            try {
                File rootDir = FileUtil.file(DirConstants.SFTP_HOME);
                File[] dateDirs = rootDir.listFiles();
                List<FtpHomeFile> list = new ArrayList<>();
                int batchSize = 5000; // 设置批处理大小为5000

                for (File dateDir : dateDirs) {
                    if (!dateDir.isDirectory()) {
                        continue;
                    }
                    File[] userDirs = dateDir.listFiles();
                    for (File userDir : userDirs) {
                        if (!userDir.isDirectory()) {
                            continue;
                        }
                        String creatorEmail = userDir.getName();
                        List<File> userFiles = FileUtil.loopFiles(userDir);
                        for (File userFile : userFiles) {
                            if (userFile.isDirectory()) {
                                continue;
                            }
                            FtpHomeFile ftpHomeFile = new FtpHomeFile();
                            ftpHomeFile.setName(userFile.getName());
                            ftpHomeFile.setPath(FilenameUtils.normalizeNoEndSeparator(userFile.getAbsolutePath(), true));
                            ftpHomeFile.setSize(userFile.length());
                            ftpHomeFile.setCreatorEmail(creatorEmail);
                            ftpHomeFile.setCreateTime(new Date(userFile.lastModified()));
                            list.add(ftpHomeFile);

                            // 当列表达到5000条记录时，保存并清空列表
                            if (list.size() >= batchSize) {
                                this.saveOrUpdateBatch(list);
                                list.clear(); // 清空列表以便下一批处理
                            }
                        }
                    }
                }

                // 保存剩余的记录（如果有）
                if (!list.isEmpty()) {
                    this.saveOrUpdateBatch(list);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                // 删除键
                redisService.deleteObject(SCAN_DISK_TASK_KEY);
            }
        });
    }

    @Override
    public TableDataInfo selectListPage(FtpFileLogQueryDTO queryDTO) {
        Wrapper<FtpHomeFile> qw = Wrappers.<FtpHomeFile>lambdaQuery().eq(StrUtil.isNotBlank(queryDTO.getCreatorEmail()), FtpHomeFile::getCreatorEmail, queryDTO.getCreatorEmail())
                .like(StrUtil.isNotBlank(queryDTO.getPath()), FtpHomeFile::getPath, queryDTO.getPath())
                .ge(queryDTO.getBeginTime() != null, FtpHomeFile::getCreateTime, queryDTO.getBeginTime() != null ? DateUtil.beginOfDay(queryDTO.getBeginTime()) : null)
                .le(queryDTO.getEndTime() != null, FtpHomeFile::getCreateTime, queryDTO.getEndTime() != null ? DateUtil.endOfDay(queryDTO.getEndTime()) : null);
        List<FtpHomeFile> list = this.list(qw);

        List<FtpHomeFileVO> result = list.stream().map(x -> {
            FtpHomeFileVO vo = new FtpHomeFileVO();
            BeanUtil.copyProperties(x, vo);
            vo.setReadableFileSize(FileUtil.readableFileSize(x.getSize()));
            return vo;
        }).collect(Collectors.toList());

        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setRows(result);
        rspData.setMsg("查询成功");
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

}
