package org.biosino.system.vo.metadata;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@Data
public class UnarchivedDataExportVO {

    @Excel(name = "Data ID")
    private String datNo;

    @Excel(name = "File Name")
    private String name;

    @Excel(name = "Data Type")
    private String dataType;

    @Excel(name = "Upload Type")
    private String uploadType;

    @Excel(name = "File Size")
    private String readableFileSize;

    @Excel(name = "Creator Date")
    private String creatorEmail;

    @Excel(name = "Upload Date", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
}
