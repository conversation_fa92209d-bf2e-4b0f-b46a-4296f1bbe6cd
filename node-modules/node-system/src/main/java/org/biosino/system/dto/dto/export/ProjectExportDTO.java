package org.biosino.system.dto.dto.export;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.biosino.common.mongo.entity.other.OtherIds;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.system.dto.dto.ProjectDTO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/10
 */
@Data
public class ProjectExportDTO {

    @JSONField(name = "id", ordinal = 1)
    private String id;

    @JSONField(name = "proj_no", ordinal = 2)
    private String projectNo;

    @JSONField(name = "sub_no", ordinal = 3)
    private String subNo;

    @J<PERSON>NField(name = "name", ordinal = 4)
    private String name;

    @JSONField(name = "description", ordinal = 5)
    private String description;

    @JSONField(name = "related_links", ordinal = 6)
    private List<String> relatedLinks;

    @JSONField(name = "audited", ordinal = 7)
    private String audited;

    @JSONField(name = "creator", ordinal = 8)
    private String creator;

    @J<PERSON>NField(name = "submission_date", ordinal = 9)
    private Date createDate;

    @JSONField(name = "update_date", ordinal = 10)
    private Date updateDate;

    @JSONField(name = "public_date", ordinal = 11)
    private Date publicDate;

    @JSONField(name = "hit_num", ordinal = 12)
    private Long hitNum;

    @JSONField(name = "submitter", ordinal = 13)
    private Submitter submitter;

    @JSONField(name = "other_ids", ordinal = 14)
    private List<OtherIds> otherIds;

    @JSONField(name = "operator", ordinal = 15)
    private String operator;

    @JSONField(name = "operation_date", ordinal = 16)
    private Date operationDate;

    @JSONField(name = "export_num", ordinal = 17)
    private Long exportNum;

    @JSONField(name = "temp_data", ordinal = 18)
    private ProjectDTO tempData;

    @JSONField(name = "used_ids", ordinal = 19)
    private List<String> usedIds;

    @JSONField(name = "ownership", ordinal = 20)
    private String ownership;

    @JSONField(name = "source_project", ordinal = 21)
    private List<String> sourceProject;

    @JSONField(name = "visible_status", ordinal = 22)
    private String visibleStatus;

}
