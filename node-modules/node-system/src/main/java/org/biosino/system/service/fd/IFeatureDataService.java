package org.biosino.system.service.fd;

import org.biosino.common.mongo.entity.admin.HumanResource;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.SpecialHumanResourceVo;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊数据集 人类（微生物）资源
 *
 * <AUTHOR>
 */
public interface IFeatureDataService {

    SpecialHumanResourceVo list(HumanResource search, Pageable pageable);

    List<String> searchPrjId(final String keyword);

    List<String> initAllCat1(final boolean microbeFlag);

    boolean saveHumanResource(HumanResource humanResource, String operName, List<String> strings);

    boolean batchUpdateHrStatus(final FeatureDataDTO featureDataDTO);

    boolean batchDeleteHr(FeatureDataDTO featureDataDTO);

    void downloadHr(HttpServletResponse response, Boolean microbeFlag);

    String importData(MultipartFile file, String operName, Boolean microbeFlag, Boolean deleteOld);

}
