package org.biosino.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.DownloadLog;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.repository.DownloadLogRepository;
import org.biosino.system.service.meta.BaseService;
import org.biosino.system.vo.DownloadLogVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@Service
@RequiredArgsConstructor
public class DownloadLogService {

    private final DownloadLogRepository downloadLogRepository;

    private final BaseService baseService;

    public Page<DownloadLogVO> list(LogQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getUserEmail()) && queryDTO.getUserEmail().contains("@")) {
            MemberDTO memberDTO = baseService.getMemberInfoByEmail(queryDTO.getUserEmail());
            queryDTO.setUserId(memberDTO.getId());
        }
        Page<DownloadLog> page = downloadLogRepository.findLogPage(queryDTO);
        List<String> memberIds = page.getContent().stream().map(DownloadLog::getMemberId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, String> memberIdToEmailMap = baseService.getMemberIdToEmailMap(memberIds);
        Page<DownloadLogVO> result = page.map(x -> {
            DownloadLogVO vo = new DownloadLogVO();
            BeanUtil.copyProperties(x, vo);
            vo.setUserEmail(memberIdToEmailMap.getOrDefault(x.getMemberId(), x.getMemberId()));
            return vo;
        });
        return result;
    }
}
