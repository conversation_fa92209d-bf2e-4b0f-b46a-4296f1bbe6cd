package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
public class DataExcel {
    @Excel(name = "Month")
    private String month;

    @Excel(name = "Total Data", cellType = Excel.ColumnType.NUMERIC)
    private Long dataTotal = 0L;

    @Excel(name = "Public Data", cellType = Excel.ColumnType.NUMERIC)
    private Long dataPublic = 0L;
    @Excel(name = "Restricted Data", cellType = Excel.ColumnType.NUMERIC)
    private Long dataRestricted = 0L;
    @Excel(name = "Private Data", cellType = Excel.ColumnType.NUMERIC)
    private Long dataPrivate = 0L;

    @Excel(name = "Public Data Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float dataPublicPercentage = 0F;
    @Excel(name = "Restricted Data Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float dataRestrictedPercentage = 0F;
    @Excel(name = "Private Data Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float dataPrivatePercentage = 0F;

    @Excel(name = "Total Data Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double dataTotalSize = 0D;
    @Excel(name = "Public Data Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double dataPublicSize = 0D;
    @Excel(name = "Restricted Data Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double dataRestrictedSize = 0D;
    @Excel(name = "Private Data Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double dataPrivateSize = 0D;

    @Excel(name = "Public Data Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float dataPublicSizePercentage = 0F;
    @Excel(name = "Restricted Data Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float dataRestrictedSizePercentage = 0F;
    @Excel(name = "Private Data Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float dataPrivateSizePercentage = 0F;

    @Excel(name = "Data Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long dataIncrement = 0L;
    @Excel(name = "Public Data Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long dataPublicIncrement = 0L;
    @Excel(name = "Restricted Data Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long dataRestrictedIncrement = 0L;
    @Excel(name = "Private Data Increment", cellType = Excel.ColumnType.NUMERIC)
    private Long dataPrivateIncrement = 0L;

    @Excel(name = "Data Size Increment", cellType = Excel.ColumnType.NUMERIC)
    private Double dataSizeIncrement = 0D;
    @Excel(name = "Public Data Size Increment", cellType = Excel.ColumnType.NUMERIC)
    private Double dataPublicSizeIncrement = 0D;
    @Excel(name = "Restricted Data Size Increment", cellType = Excel.ColumnType.NUMERIC)
    private Double dataRestrictedSizeIncrement = 0D;
    @Excel(name = "Private Data Size Increment", cellType = Excel.ColumnType.NUMERIC)
    private Double dataPrivateSizeIncrement = 0D;

}
