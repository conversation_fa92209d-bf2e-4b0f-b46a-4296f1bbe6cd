package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsTempFtpData;
import org.biosino.system.vo.excel.TempFtpDataExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface StatisticsTempFtpDataMapper extends CommonMapper {
    StatisticsTempFtpDataMapper INSTANCE = Mappers.getMapper(StatisticsTempFtpDataMapper.class);

    @Mapping(source = "ftpHomeFileSize", target = "ftpHomeFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "ftpHomeHasMd5FileSize", target = "ftpHomeHasMd5FileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "ftpHomeExpired6MonthFileSize", target = "ftpHomeExpired6MonthFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "ftpHomeExpired1YearFileSize", target = "ftpHomeExpired1YearFileSize", qualifiedByName = "formatGBSize")
    TempFtpDataExcel dbToExcel(StatisticsTempFtpData data);


}
