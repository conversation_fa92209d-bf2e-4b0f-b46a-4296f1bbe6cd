package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsSample;
import org.biosino.common.mongo.entity.statistics.StatisticsSampleExp;
import org.biosino.system.vo.SampleExpStatVO;
import org.biosino.system.vo.SampleStatVO;
import org.biosino.system.vo.excel.SampleExcel;
import org.biosino.system.vo.excel.SampleExpExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
@Mapper
public interface StatisticsSampleMapper extends CommonMapper {
    StatisticsSampleMapper INSTANCE = Mappers.getMapper(StatisticsSampleMapper.class);

    @Mapping(source = "totalFileSize", target = "totalFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "accessibleFileSize", target = "accessibleFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "unAccessibleFileSize", target = "unAccessibleFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "totalFileSizeGrowth", target = "totalFileSizeGrowth", qualifiedByName = "formatSize")
    @Mapping(source = "accessibleFileSizeGrowth", target = "accessibleFileSizeGrowth", qualifiedByName = "formatSize")
    @Mapping(source = "unAccessibleFileSizeGrowth", target = "unAccessibleFileSizeGrowth", qualifiedByName = "formatSize")
    SampleExcel dbToExcel(StatisticsSample data);

    @Mapping(source = "totalFileSize", target = "totalFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "accessibleFileSize", target = "accessibleFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "unAccessibleFileSize", target = "unAccessibleFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "totalFileSizeGrowth", target = "totalFileSizeGrowth", qualifiedByName = "readableFileSize")
    @Mapping(source = "accessibleFileSizeGrowth", target = "accessibleFileSizeGrowth", qualifiedByName = "readableFileSize")
    @Mapping(source = "unAccessibleFileSizeGrowth", target = "unAccessibleFileSizeGrowth", qualifiedByName = "readableFileSize")
    SampleStatVO dbToVO(StatisticsSample data);

    List<SampleStatVO> dbToVO(List<StatisticsSample> data);


    @Mapping(source = "expTotalFileSize", target = "expTotalFileSize", qualifiedByName = "formatSize")
    @Mapping(source = "expPrivateFileSize", target = "expPrivateFileSize", qualifiedByName = "formatSize")
    SampleExpExcel sapExpDbToExcel(StatisticsSampleExp data);

    @Mapping(source = "expTotalFileSize", target = "expTotalFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "expPrivateFileSize", target = "expPrivateFileSize", qualifiedByName = "readableFileSize")
    SampleExpStatVO sapExpDbToVO(StatisticsSampleExp data);

    List<SampleExpStatVO> sapExpDbToVO(List<StatisticsSampleExp> data);


}
