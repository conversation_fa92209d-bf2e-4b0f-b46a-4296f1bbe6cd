package org.biosino.system.service.impl.fd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.EsTreeItemDTO;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.mongo.entity.admin.MultipleOmicsResource;
import org.biosino.common.security.utils.ExpSampleTokenUtils;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.es.api.dto.FdMultipleResDTO;
import org.biosino.es.api.dto.FeatureDataPrjDTO;
import org.biosino.es.api.vo.fd.MultipleOmicsQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.MultipleOmicsVO;
import org.biosino.system.repository.MultipleOmicsResourceRepository;
import org.biosino.system.service.fd.IMultOmicResService;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.system.service.impl.fd.SingleSapResServiceImpl.removeNoPermission;

/**
 * 特殊数据集 多组学
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MultOmicResServiceImpl implements IMultOmicResService {
    private final RemoteNodeESSearchService remoteNodeESSearchService;
    private final MultipleOmicsResourceRepository multipleOmicsResourceRepository;

    @Override
    public PageImpl<MultipleOmicsResource> list(MultipleOmicsQueryVO searchVO) {
        return multipleOmicsResourceRepository.multOmicList(searchVO);
    }

    @Override
    public MultipleOmicsVO dialogList(MultipleOmicsQueryVO searchVO) {
        final R<FdMultipleResDTO> resultR = remoteNodeESSearchService.searchMultipleOmics(searchVO, SecurityConstants.INNER);
        if (resultR == null || R.isError(resultR)) {
            return null;
        }
        final FdMultipleResDTO data = resultR.getData();
        final MultipleOmicsVO vo = new MultipleOmicsVO();

        // 删除没有权限的组学
        List<EsTreeItemDTO> esTreeItems = data.getEsTreeItems();
        removeNoPermission(esTreeItems, ExpSampleTokenUtils.getAllExperimentName());
        vo.setEsTreeItems(esTreeItems);

        vo.setDefaultCheckedKeys(data.getDefaultCheckedKeys());
        final EsPageInfo<FeatureDataPrjDTO> pageInfo = data.getPageInfo();
        if (pageInfo != null) {
            final List<FeatureDataPrjDTO> list = pageInfo.getList();
            final List<MultipleOmicsResource> listVo = new ArrayList<>();
            if (CollUtil.isNotEmpty(list)) {
                for (FeatureDataPrjDTO dto : list) {
                    final MultipleOmicsResource res = new MultipleOmicsResource();
                    res.setId(dto.getTypeId());
                    res.setProjID(dto.getTypeId());
                    res.setProjName(dto.getName());
                    res.setExpTypes(dto.getRelaExpType());
                    res.setSubmitter(dto.getSubName());
                    res.setModifiedDate(dto.getModifiedDate());
                    res.setDes(dto.getDescription());
                    listVo.add(res);
                }
            }
            final EsPageInfo<MultipleOmicsResource> page = EsPageInfo.of(listVo);
            page.setTotal(pageInfo.getTotal());
            vo.setPageInfo(page);
        }
        return vo;
    }

    /**
     * 保存
     */
    @Override
    public boolean save(List<String> prjIds, String username) {
        if (CollUtil.isEmpty(prjIds)) {
            throw new ServiceException("未找到项目");
        }
        final List<String> uploadIds = new ArrayList<>(prjIds);
        final R<List<FeatureDataPrjDTO>> prjResult = remoteNodeESSearchService.searchMultOmicByPrjNos(prjIds, SecurityConstants.INNER);
        if (prjResult == null || R.isError(prjResult)) {
            throw new ServiceException("ES Api错误");
        }
        final List<FeatureDataPrjDTO> data = prjResult.getData();
        if (CollUtil.isEmpty(data)) {
            throw new ServiceException("未找到项目");
        }

        prjIds = data.stream().map(FeatureDataPrjDTO::getTypeId).collect(Collectors.toList());
        uploadIds.removeAll(prjIds);
        if (CollUtil.isNotEmpty(uploadIds)) {
            throw new ServiceException("未找到项目id: " + JSON.toJSONString(uploadIds));
        }

        final Map<String, MultipleOmicsResource> resMap = multipleOmicsResourceRepository.findMapByProjIDIn(prjIds);
        final List<MultipleOmicsResource> newData = new ArrayList<>();
        final Date now = new Date();
        for (FeatureDataPrjDTO item : data) {
            final String projectNo = item.getTypeId();
            if (!resMap.containsKey(projectNo)) {
                final MultipleOmicsResource res = new MultipleOmicsResource();
                res.setProjID(projectNo);
                res.setProjName(item.getName());
                res.setSubmitter(item.getSubName());
                res.setDes(item.getDescription());
                res.setExpTypes(item.getRelaExpType());
                res.setStatus(DataStatusEnum.disable.name());
                res.setCreator(username);
                res.setCreateTime(now);
                res.setModifiedDate(now);
                newData.add(res);
            }
        }
        if (CollUtil.isNotEmpty(newData)) {
            multipleOmicsResourceRepository.saveAll(newData);
        }
        return true;
    }

    /**
     * 导出
     */
    @Override
    public void download(HttpServletRequest request, HttpServletResponse response) {
        final List<MultipleOmicsResource> all = multipleOmicsResourceRepository.findAll();
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("没有数据");
        }
        File multOmicFile = null;
        BigExcelWriter bigWriter = null;
        try {
            multOmicFile = Files.createTempFile("multOmic", ".xlsx").toFile();
            if (multOmicFile.exists()) {
                multOmicFile.delete();
            }
            bigWriter = cn.hutool.poi.excel.ExcelUtil.getBigWriter(multOmicFile);
            List<Map<String, Object>> rows = new ArrayList<>();
            for (MultipleOmicsResource item : all) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("Project ID", item.getProjID());
                row.put("Experiment Type", CollUtil.join(item.getExpTypes(), "、"));
                row.put("Project Name", item.getProjName());
                row.put("Description", item.getDes());
                row.put("Submitter", item.getSubmitter());
                row.put("Modified Date", DateUtils.formatDateTime(item.getModifiedDate()));
                rows.add(row);
            }
            bigWriter.write(rows, true);
            bigWriter.close();
            DownloadUtils.download(request, response, multOmicFile);
        } catch (Exception e) {
            log.error("多组学资源数据导出出错", e);
        } finally {
            IoUtil.close(bigWriter);
            if (multOmicFile != null && multOmicFile.isFile()) {
                FileUtil.del(multOmicFile);
            }
        }
    }

    @Override
    public boolean batchUpdateStatus(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        List<MultipleOmicsResource> list;
        if (CollUtil.isNotEmpty(ids)) {
            list = multipleOmicsResourceRepository.findAllByIdIn(ids);
        } else {
            list = multipleOmicsResourceRepository.findAll();
        }
        final boolean disable = featureDataDTO.isDisable();
        final Date now = new Date();
        for (MultipleOmicsResource item : list) {
            item.setStatus(disable ? DataStatusEnum.disable.name() : DataStatusEnum.enable.name());
            item.setModifiedDate(now);
        }
        multipleOmicsResourceRepository.saveAll(list);
        return true;
    }

    /**
     * 批量添加
     */
    @Override
    public boolean batchImport(FeatureDataDTO featureDataDTO, String username) {
        final String batchImportNosStr = StrUtil.trimToNull(featureDataDTO.getBatchImportNosStr());
        if (batchImportNosStr == null) {
            throw new ServiceException("项目ID不能为空");
        }
        final List<String> prjIds = Arrays.stream(batchImportNosStr.split("\n")).map(StrUtil::trimToNull).filter(Objects::nonNull).collect(Collectors.toList());
        save(prjIds, username);
        return true;
    }

    /**
     * 批量删除
     */
    @Override
    public boolean batchDelete(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            multipleOmicsResourceRepository.deleteByIdIn(ids);
        }
        return true;
    }

}
