package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 */
@Data
public class ExperimentExcel {
    // 月份
    @Excel(name = "Month")
    private String month;

    @Excel(name = "Type")
    private String type;

    // 存量 EXPERIMENT_TYPE_GENOMIC_001，experiment表中visible_status为Unaccessible或Accessible，且exp_type为Genomic的exp_no总数
    @Excel(name = "Total", cellType = Excel.ColumnType.NUMERIC)
    private Long total = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_002，experiment表中visible_status为Accessible，且exp_type为Genomic的exp_no总数
    @Excel(name = "Accessible", cellType = Excel.ColumnType.NUMERIC)
    private Long accessibleNum = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_003，EXPERIMENT_TYPE_GENOMIC_001 - EXPERIMENT_TYPE_GENOMIC_002
    @Excel(name = "UnAccessible", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessibleNum = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_004，EXPERIMENT_TYPE_GENOMIC_001（当前月份）-EXPERIMENT_TYPE_GENOMIC_001（上个月）
    @Excel(name = "Total Growth", cellType = Excel.ColumnType.NUMERIC)
    private Long totalGrowth = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_005，EXPERIMENT_TYPE_GENOMIC_002（当前月份）-EXPERIMENT_TYPE_GENOMIC_002（上个月）
    @Excel(name = "Accessible Growth", cellType = Excel.ColumnType.NUMERIC)
    private Long accessibleGrowth = 0L;

    // 增量 EXPERIMENT_TYPE_GENOMIC_006，EXPERIMENT_TYPE_GENOMIC_003（当前月份）-EXPERIMENT_TYPE_GENOMIC_003（上个月）
    @Excel(name = "UnAccessible Growth", cellType = Excel.ColumnType.NUMERIC)
    private Long unAccessibleGrowth = 0L;

    // 存量 EXPERIMENT_TYPE_GENOMIC_007，查询node_related_es索引库中，expNo在EXPERIMENT_TYPE_GENOMIC_001所属exp_no集合内的fileSize总和，单位TB
    @Excel(name = "Total File Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double totalFileSize = 0D;

    // 存量 EXPERIMENT_TYPE_GENOMIC_008，查询node_related_es索引库中，expNo在EXPERIMENT_TYPE_GENOMIC_002所属exp_no集合内的fileSize总和，单位TB
    @Excel(name = "Accessible File Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double accessibleFileSize = 0D;

    // 存量 EXPERIMENT_TYPE_GENOMIC_009，查询node_related_es索引库中，expNo在EXPERIMENT_TYPE_GENOMIC_003所属exp_no集合内的fileSize总和，单位TB
    @Excel(name = "UnAccessible File Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessibleFileSize = 0D;

    // 增量 EXPERIMENT_TYPE_GENOMIC_010，EXPERIMENT_TYPE_GENOMIC_007（当前月份）-EXPERIMENT_TYPE_GENOMIC_007（上个月）
    @Excel(name = "Total File Size Growth (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double totalFileSizeGrowth = 0D;

    // 增量 EXPERIMENT_TYPE_GENOMIC_011，EXPERIMENT_TYPE_GENOMIC_008（当前月份）-EXPERIMENT_TYPE_GENOMIC_008（上个月）
    @Excel(name = "Accessible File Size Growth (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double accessibleFileSizeGrowth = 0D;

    // 增量 EXPERIMENT_TYPE_GENOMIC_012，EXPERIMENT_TYPE_GENOMIC_009（当前月份）-EXPERIMENT_TYPE_GENOMIC_009（上个月）
    @Excel(name = "UnAccessible File Size Growth (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Double unAccessibleFileSizeGrowth = 0D;

    // 存量 EXPERIMENT_TYPE_GENOMIC_013，分子：EXPERIMENT_TYPE_GENOMIC_007，分母：DATA_VOLUMN_RAW_DATA_008
    @Excel(name = "Total File Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float totalFileSizeRate = 0F;

    // 存量 EXPERIMENT_TYPE_GENOMIC_014，分子：EXPERIMENT_TYPE_GENOMIC_001，分母：DATA_VOLUMN_EXPERIMENT_009
    @Excel(name = "Total Quantity Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float totalRate = 0F;

}
