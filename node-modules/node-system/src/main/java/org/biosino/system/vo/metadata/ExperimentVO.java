package org.biosino.system.vo.metadata;

import lombok.Data;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class ExperimentVO {

    private String expNo;
    private String projectNo;
    private String expType;
    private String name;
    private String description;
    private String protocol;
    private String[] usedIds;
    private String creator;

    private Map<String, Object> attributes = new LinkedHashMap<>();

    private List<String> relatedLinks;

    private List<PublishVO> publish;

    private Submitter submitter;
}
