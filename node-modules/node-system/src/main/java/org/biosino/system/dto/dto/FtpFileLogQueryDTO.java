package org.biosino.system.dto.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FtpFileLogQueryDTO extends BaseQuery {

    private String creator;

    private String creatorEmail;

    private String path;

    private String status;

    private Date beginTime;

    private Date endTime;

}
