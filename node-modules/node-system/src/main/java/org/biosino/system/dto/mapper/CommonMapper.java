package org.biosino.system.dto.mapper;

import cn.hutool.core.io.FileUtil;
import org.biosino.common.core.utils.NodeUtils;
import org.mapstruct.Named;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
public interface CommonMapper {
    @Named("formatSize")
    default double formatSize(Long value) {
        return value == null ? 0 : NodeUtils.convertToTB(value);
    }

    @Named("formatGBSize")
    default double formatGBSize(Long value) {
        return value == null ? 0 : NodeUtils.convertToGB(value);
    }

    @Named("readableFileSize")
    default String readableFileSize(Long value) {
        return value == null ? "0" : FileUtil.readableFileSize(value);
    }
}
