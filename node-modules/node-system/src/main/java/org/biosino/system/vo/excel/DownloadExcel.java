package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/7/12
 */
@Data
public class DownloadExcel {

    @Excel(name = "Month")
    private String month;

    @Excel(name = "Project", cellType = Excel.ColumnType.NUMERIC)
    private Long project;

    @Excel(name = "Experiment", cellType = Excel.ColumnType.NUMERIC)
    private Long experiment;

    @Excel(name = "Sample", cellType = Excel.ColumnType.NUMERIC)
    private Long sample;

    @Excel(name = "Analysis", cellType = Excel.ColumnType.NUMERIC)
    private Long analysis;

    @Excel(name = "FTP Total Data", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpDataTotal = 0L;
    @Excel(name = "FTP Total Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpTotalDataSize = 0D;

    @Excel(name = "FTP Public Data", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpPublicData = 0L;
    @Excel(name = "FTP Public Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpPublicDataSize = 0D;
    @Excel(name = "FTP Restricted Data", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpRestrictedData = 0L;
    @Excel(name = "FTP Restricted Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpRestrictedDataSize = 0D;
    @Excel(name = "FTP Private Data", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpPrivateData = 0L;
    @Excel(name = "FTP Private Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpPrivateDataSize = 0D;

    @Excel(name = "HTTP Total Data", cellType = Excel.ColumnType.NUMERIC)
    private Long httpDataTotal = 0L;
    @Excel(name = "HTTP Total Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double httpTotalDataSize = 0D;

    @Excel(name = "HTTP Public Data", cellType = Excel.ColumnType.NUMERIC)
    private Long httpPublicData = 0L;
    @Excel(name = "HTTP Public Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double httpPublicDataSize = 0D;
    @Excel(name = "HTTP Restricted Data", cellType = Excel.ColumnType.NUMERIC)
    private Long httpRestrictedData = 0L;
    @Excel(name = "HTTP Restricted Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double httpRestrictedDataSize = 0D;
    @Excel(name = "HTTP Private Data", cellType = Excel.ColumnType.NUMERIC)
    private Long httpPrivateData = 0L;
    @Excel(name = "HTTP Private Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double httpPrivateDataSize = 0D;


}
