package org.biosino.system.dto.dto;

import lombok.Data;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/4/28
 */
@Data
public class ExperimentDTO {
    private String expNo;
    private String projectNo;
    private String expType;
    private String name;
    private String description;
    private String protocol;
    private Map<String, Object> attributes = new LinkedHashMap<>();
    private List<String> relatedLinks;
    private List<PublishDTO> publish;
    private Submitter submitter;
}
