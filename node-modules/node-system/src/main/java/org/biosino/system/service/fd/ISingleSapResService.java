package org.biosino.system.service.fd;

import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.SingleSapVO;
import org.springframework.data.domain.PageImpl;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊数据集 单样本多组学
 *
 * <AUTHOR>
 */
public interface ISingleSapResService {

    PageImpl<SingleSampleResource> list(SingleSapQueryVO searchVO);

    SingleSapVO dialogList(SingleSapQueryVO searchVO);

    boolean save(List<String> prjIds, String username);

    void download(HttpServletRequest request, HttpServletResponse response);

    boolean batchUpdateStatus(FeatureDataDTO featureDataDTO);

    boolean batchImport(FeatureDataDTO featureDataDTO, String username);

    boolean batchDelete(FeatureDataDTO featureDataDTO);
}
