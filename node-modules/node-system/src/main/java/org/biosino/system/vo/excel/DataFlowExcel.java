package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/7/10
 */
@Data
public class DataFlowExcel {

    @Excel(name = "Month")
    private String month;

    @Excel(name = "Download", cellType = Excel.ColumnType.NUMERIC)
    private Long download = 0L;
    @Excel(name = "Download Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double downloadDataSize = 0D;

    @Excel(name = "Upload Data", cellType = Excel.ColumnType.NUMERIC)
    private Long uploadData = 0L;
    @Excel(name = "Upload Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double uploadDataSize = 0D;

    @Excel(name = "Submission", cellType = Excel.ColumnType.NUMERIC)
    private Long submission = 0L;
    @Excel(name = "Submission Data Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double submissionDataSize = 0D;

}
