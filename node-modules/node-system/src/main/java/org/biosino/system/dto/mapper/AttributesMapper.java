package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.ExpSampleType;
import org.biosino.system.dto.dto.standmg.StandMgAttrDTO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AttributesMapper {
    AttributesMapper INSTANCE = Mappers.getMapper(AttributesMapper.class);

    void copyToDb(StandMgAttrDTO source, @MappingTarget ExpSampleType.Attributes result);

    List<StandMgAttrDTO> copyListToDTO(List<ExpSampleType.Attributes> list);

}
