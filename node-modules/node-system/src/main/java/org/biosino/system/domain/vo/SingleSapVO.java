package org.biosino.system.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.dto.es.EsTreeItemDTO;
import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.dromara.easyes.core.biz.EsPageInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 特殊数据集，单样本多组学数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class SingleSapVO implements Serializable {
    // 列表数据
    private EsPageInfo<SingleSampleResource> pageInfo;
    // 树数据
    private List<EsTreeItemDTO> esTreeItems;

    private List<String> defaultCheckedKeys;
}
