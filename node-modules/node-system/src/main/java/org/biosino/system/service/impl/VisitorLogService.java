package org.biosino.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.common.mongo.entity.VisitorLog;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.dto.dto.LogQueryDTO;
import org.biosino.system.repository.VisitorLogRepository;
import org.biosino.system.service.meta.BaseService;
import org.biosino.system.vo.VisitorLogVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@Service
@RequiredArgsConstructor
public class VisitorLogService {

    private final VisitorLogRepository visitorLogRepository;

    private final BaseService baseService;

    public Page<VisitorLogVO> list(LogQueryDTO queryDTO) {
        if (StrUtil.isNotBlank(queryDTO.getUserEmail())) {
            MemberDTO memberDTO = baseService.getMemberInfoByEmail(queryDTO.getUserEmail());
            queryDTO.setUserId(memberDTO.getId());
        }
        if (StrUtil.isNotBlank(queryDTO.getOwnerEmail())) {
            MemberDTO memberDTO = baseService.getMemberInfoByEmail(queryDTO.getOwnerEmail());
            queryDTO.setOwnerId(memberDTO.getId());
        }
        Page<VisitorLog> page = visitorLogRepository.findLogPage(queryDTO);

        Set<String> allMemberIds = new HashSet<>();
        List<String> memberIds = page.getContent().stream().map(VisitorLog::getMemberId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());
        List<String> ownerIds = page.getContent().stream().map(VisitorLog::getOwnerId).filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 去重
        allMemberIds.addAll(memberIds);
        allMemberIds.addAll(ownerIds);

        // 一起将Email查出来
        Map<String, String> memberIdToEmailMap = baseService.getMemberIdToEmailMap(allMemberIds);
        Page<VisitorLogVO> result = page.map(x -> {
            VisitorLogVO vo = new VisitorLogVO();
            BeanUtil.copyProperties(x, vo);
            vo.setUserEmail(memberIdToEmailMap.getOrDefault(x.getMemberId(), null));
            vo.setOwnerEmail(memberIdToEmailMap.getOrDefault(x.getOwnerId(), null));
            return vo;
        });
        return result;
    }
}
