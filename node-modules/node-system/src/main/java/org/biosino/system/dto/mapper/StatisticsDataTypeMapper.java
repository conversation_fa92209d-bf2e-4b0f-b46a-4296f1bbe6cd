package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsDataType;
import org.biosino.system.vo.DataTypeStatVO;
import org.biosino.system.vo.excel.DataTypeExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/31
 */
@Mapper
public interface StatisticsDataTypeMapper extends CommonMapper {
    StatisticsDataTypeMapper INSTANCE = Mappers.getMapper(StatisticsDataTypeMapper.class);

    @Mapping(source = "totalFileSize", target = "totalFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "totalPublicFileSize", target = "totalPublicFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "totalRestrictedFileSize", target = "totalRestrictedFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "totalPrivateFileSize", target = "totalPrivateFileSize", qualifiedByName = "formatGBSize")
    DataTypeExcel dbToExcel(StatisticsDataType data);

    List<DataTypeExcel> dbToExcel(List<StatisticsDataType> data);


    @Mapping(source = "totalFileSize", target = "totalFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "totalPublicFileSize", target = "totalPublicFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "totalRestrictedFileSize", target = "totalRestrictedFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "totalPrivateFileSize", target = "totalPrivateFileSize", qualifiedByName = "readableFileSize")
    DataTypeStatVO dbToVO(StatisticsDataType data);

    List<DataTypeStatVO> dbToVO(List<StatisticsDataType> data);

}
