package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

@Data
public class TempFtpDataExcel {
    // 月份
    @Excel(name = "Month")
    private String month;

    // 存量 DATA_FTP_HOME_001，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5的记录数
    @Excel(name = "Ftp Home File", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpHomeFile = 0L;

    // 存量 DATA_FTP_HOME_002，DATA_FTP_HOME_001记录的file size之和
    @Excel(name = "Ftp Home File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpHomeFileSize = 0D;

    // 存量 DATA_FTP_HOME_003，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5 且 拥有相关的md5文件的记录数
    @Excel(name = "Ftp Home Has Md5 File", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpHomeHasMd5File = 0L;

    // 存量 DATA_FTP_HOME_004，DATA_FTP_HOME_003记录的file size之和
    @Excel(name = "Ftp Home Has Md5 File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpHomeHasMd5FileSize = 0D;

    // 存量 DATA_FTP_HOME_005，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5 且 create_time超过6个月 的记录数
    @Excel(name = "Ftp Home Expired 6 Month File", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpHomeExpired6MonthFile = 0L;

    // 存量 DATA_FTP_HOME_006，DATA_FTP_HOME_005记录的file size之和
    @Excel(name = "Ftp Home Expired 6 Month File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpHomeExpired6MonthFileSize = 0D;

    // 存量 DATA_FTP_HOME_007，MySQL中ftp_file_log表中status为“待校验” 且 文件名结尾不是.md5 且 create_time超过12个月 的记录数
    @Excel(name = "Ftp Home Expired 1 Year File", cellType = Excel.ColumnType.NUMERIC)
    private Long ftpHomeExpired1YearFile = 0L;

    // 存量 DATA_FTP_HOME_008，DATA_FTP_HOME_007记录的file size之和
    @Excel(name = "Ftp Home Expired 1 Year File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double ftpHomeExpired1YearFileSize = 0D;

}
