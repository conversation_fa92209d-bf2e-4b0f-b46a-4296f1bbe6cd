package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 * @date 2024/8/1
 */
@Data
public class PrjMultiExpExcel {

    // 月份
    @Excel(name = "Month")
    private String month;

    // 存量 MULTI_OMICS_PROJECT_BASE_001，查询node_related_es索引库中，expType类别超过1种的projNo的个数
    @Excel(name = "Multiple Omics Total", cellType = Excel.ColumnType.NUMERIC)
    private Long multi = 0L;

    // 存量 MULTI_OMICS_PROJECT_BASE_002，查询node_related_es索引库中，expType类别超过1种、且projVisible为Accessible的projNo的个数
    @Excel(name = "Accessible Multiple Omics Total", cellType = Excel.ColumnType.NUMERIC)
    private Long accessibleMulti = 0L;

    // 存量 MULTI_OMICS_PROJECT_BASE_003，查询node_related_es索引库中，expType类别只有1种的projNo的个数
    @Excel(name = "Single Omics Total", cellType = Excel.ColumnType.NUMERIC)
    private Long single = 0L;

    @Excel(name = "Multiple Omics Quantity Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float multiPercentage = 0F;

    @Excel(name = "Single Omics Quantity Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float singlePercentage = 0F;

    // 存量 MULTI_OMICS_PROJECT_BASE_013，查询node_related_es索引库中，MULTI_OMICS_PROJECT_BASE_001对应projNo所属data文件大小总和，单位：GB
    @Excel(name = "Multiple Omics File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double multiFileSize = 0D;

    // 存量 MULTI_OMICS_PROJECT_BASE_014，查询node_related_es索引库中，MULTI_OMICS_PROJECT_BASE_002对应projNo所属data文件大小总和，单位：GB
    @Excel(name = "Accessible Multiple Omics File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double accessibleMultiFileSize = 0D;

    @Excel(name = "Multiple Omics File Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float multiSizePercentage = 0F;

    @Excel(name = "Accessible Multiple Omics File Size Percentage (%)", cellType = Excel.ColumnType.NUMERIC, dateFormat = "0.00")
    private Float accessibleMultiSizePercentage = 0F;

}
