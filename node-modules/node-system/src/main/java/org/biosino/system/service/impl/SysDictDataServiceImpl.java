package org.biosino.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.security.utils.DictUtils;
import org.biosino.system.api.domain.SysDictData;
import org.biosino.system.dto.dto.OrganizationDTO;
import org.biosino.system.mapper.SysDictDataMapper;
import org.biosino.system.service.ISysDictDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl implements ISysDictDataService {
    @Autowired
    private SysDictDataMapper dictDataMapper;

    private static final String ORGANIZATION = "node_organization";


    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        return dictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return dictDataMapper.selectDictLabel(dictType, dictValue);
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = selectDictDataById(dictCode);
            dictDataMapper.deleteDictDataById(dictCode);
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
    }

    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int insertDictData(SysDictData data) {
        int row = dictDataMapper.insertDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }

    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    public int updateDictData(SysDictData data) {
        int row = dictDataMapper.updateDictData(data);
        if (row > 0) {
            List<SysDictData> dictDatas = dictDataMapper.selectDictDataByType(data.getDictType());
            DictUtils.setDictCache(data.getDictType(), dictDatas);
        }
        return row;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importOrganizationData(List<OrganizationDTO> dictDatas, String operName) {
        if (CollUtil.isEmpty(dictDatas)) {
            throw new ServiceException("导入数据不能为空");
        }

        List<SysDictData> organizationDatas = new ArrayList<>();

        long i = 1;
        for (OrganizationDTO line : dictDatas) {
            String type = line.getType().trim();
            String chineseName = line.getChineseName().trim();
            String englishFullName = line.getEnglishFullName().trim();
            String englishShortName = line.getEnglishShortName().trim();
            if (StrUtil.isBlank(englishFullName)) {
                continue;
            }

            SysDictData dictData = new SysDictData();

            String label = englishFullName;
            if (StrUtil.isNotBlank(englishShortName) && !"null".equalsIgnoreCase(englishShortName.trim())) {
                label += " (" + englishShortName + ")";
            }

            String remark = chineseName;
            if (StrUtil.isNotBlank(type) && !"null".equalsIgnoreCase(type.trim())) {
                remark += " [" + type + "]";
            }

            if (StrUtil.isNotBlank(remark)) {
                dictData.setRemark(remark);
            }

            dictData.setDictSort(i++);
            dictData.setDictLabel(label);
            dictData.setDictValue(englishFullName);
            dictData.setDictType(ORGANIZATION);
            dictData.setCreateBy(operName);
            dictData.setUpdateBy(operName);
            dictData.setCreateTime(new Date());
            dictData.setUpdateTime(dictData.getCreateTime());

            organizationDatas.add(dictData);
        }

        if (CollUtil.isEmpty(organizationDatas)) {
            throw new ServiceException("导入的数据不能为空，请检查Excel内容");
        }
        // 删除历史数据
        LambdaQueryWrapper<SysDictData> deleteWrapper = Wrappers.<SysDictData>lambdaQuery().eq(SysDictData::getDictType, ORGANIZATION);
        dictDataMapper.delete(deleteWrapper);
        for (SysDictData organizationData : organizationDatas) {
            dictDataMapper.insertDictData(organizationData);
        }
    }
}
