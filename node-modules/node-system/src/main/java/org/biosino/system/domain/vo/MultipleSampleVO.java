package org.biosino.system.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.biosino.common.core.domain.dto.es.EsTreeItemDTO;
import org.biosino.common.mongo.entity.admin.MultipleSampleResource;
import org.dromara.easyes.core.biz.EsPageInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 特殊数据集，多样本数据
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class MultipleSampleVO implements Serializable {
    // 列表数据
    private EsPageInfo<MultipleSampleResource> pageInfo;
    // 树数据
    private List<EsTreeItemDTO> esTreeItems;

    private List<String> defaultCheckedKeys;
}
