package org.biosino.system.domain.vo;

import lombok.Data;
import org.biosino.common.mongo.entity.admin.HumanResource;
import org.springframework.data.domain.PageImpl;

import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * 特殊数据集 人类（微生物）资源
 *
 * <AUTHOR>
 */
@Data
public class SpecialHumanResourceVo {

    private PageImpl<HumanResource> pageInfo;

    // 新增弹窗中Category 1下拉框数据
    private List<SelectVo> selectVoList;

    private Set<String> existCategory1 = new TreeSet<>();
    private Set<String> existCategory2 = new TreeSet<>();
    private Set<String> existCategory3 = new TreeSet<>();
    private Set<String> existProjectNo = new TreeSet<>();

}
