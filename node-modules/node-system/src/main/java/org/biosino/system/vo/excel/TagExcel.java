package org.biosino.system.vo.excel;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.core.annotation.Excel;
import org.biosino.common.mongo.entity.statistics.StatisticsTag;

/**
 * <AUTHOR>
 * @date 2024/7/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TagExcel extends StatisticsTag {

    @Excel(name = "Data File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double dataFileSizeGB;

    @Excel(name = "Data Public File Size (GB)", cellType = Excel.ColumnType.NUMERIC)
    private Double dataPublicFileSizeGB;
    
}
