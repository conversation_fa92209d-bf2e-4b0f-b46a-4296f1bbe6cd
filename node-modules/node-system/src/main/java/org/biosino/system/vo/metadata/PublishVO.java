package org.biosino.system.vo.metadata;

import lombok.Data;

import java.util.Date;

@Data
public class PublishVO {

    private String subNo;
    private String type;
    private String typeId;
    private String typeName;
    private String typeDesc;

    private String id;

    // journal（需注意：一期页面的字段和数据库不对应）
    private String publication;

    private String doi;

    private String pmid;

    // title
    private String articleName;

    private String reference;

    private String status;

    private Integer sort;

    private String creator;
    private String createEmail;

    private Date createDate;

    private Boolean deleted;

}
