package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsPrjMultiExp;
import org.biosino.common.mongo.entity.statistics.StatisticsSapMultiExp;
import org.biosino.system.vo.SapMultiExpStatVO;
import org.biosino.system.vo.excel.PrjMultiExpExcel;
import org.biosino.system.vo.excel.SapMultiExpExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
@Mapper
public interface MultipleOmicsMapper extends CommonMapper {
    MultipleOmicsMapper INSTANCE = Mappers.getMapper(MultipleOmicsMapper.class);

    @Mapping(source = "multiFileSize", target = "multiFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "accessibleMultiFileSize", target = "accessibleMultiFileSize", qualifiedByName = "formatGBSize")
    PrjMultiExpExcel dbToExcel(StatisticsPrjMultiExp data);


    @Mapping(source = "multiFileSize", target = "multiFileSize", qualifiedByName = "readableFileSize")
    @Mapping(source = "accessibleMultiFileSize", target = "accessibleMultiFileSize", qualifiedByName = "readableFileSize")
    SapMultiExpStatVO dbToSapVo(StatisticsSapMultiExp data);

    List<SapMultiExpStatVO> dbToSapVo(List<StatisticsSapMultiExp> data);

    @Mapping(source = "multiFileSize", target = "multiFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "accessibleMultiFileSize", target = "accessibleMultiFileSize", qualifiedByName = "formatGBSize")
    SapMultiExpExcel dbToSapExcel(StatisticsSapMultiExp data);

    List<SapMultiExpExcel> dbToSapExcel(List<StatisticsSapMultiExp> data);


}
