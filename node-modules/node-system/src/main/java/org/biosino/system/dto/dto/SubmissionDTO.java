package org.biosino.system.dto.dto;

import lombok.Data;
import org.biosino.common.mongo.dto.BaseQuery;

/**
 * submission list查询
 *
 * <AUTHOR>
 */
@Data
public class SubmissionDTO extends BaseQuery {

    // Submission no
    private String subNo;

    private String submitter;

    // 审核员ID
    private Long auditor;

    // 数据提交人
    private String creator;
    private String creatorEmail;

    // 审核状态
    private String status;
}
