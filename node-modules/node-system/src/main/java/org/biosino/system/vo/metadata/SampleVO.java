package org.biosino.system.vo.metadata;

import lombok.Data;
import org.biosino.common.mongo.entity.other.Submitter;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class SampleVO {

    private String sapNo;
    // sample type
    private String subjectType;
    private String name;
    private String description;
    private String protocol;

    private String organism;
    private String tissue;

    private Map<String, String> attributes = new LinkedHashMap<>();

    private List<Map<String, String>> customAttributes;

    private List<String> relatedLinks;

    private List<PublishVO> publish;

    private Submitter submitter;
}
