package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.statistics.StatisticsTempData;
import org.biosino.system.vo.excel.TempDataExcel;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface StatisticsTempDataMapper extends CommonMapper {
    StatisticsTempDataMapper INSTANCE = Mappers.getMapper(StatisticsTempDataMapper.class);

    @Mapping(source = "unarchivedFileSize", target = "unarchivedFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "unarchived6MonthFileSize", target = "unarchived6MonthFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "unarchived1YearFileSize", target = "unarchived1YearFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "unAccessibleFileSize", target = "unAccessibleFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "unAccessible6MonthFileSize", target = "unAccessible6MonthFileSize", qualifiedByName = "formatGBSize")
    @Mapping(source = "unAccessible1YearFileSize", target = "unAccessible1YearFileSize", qualifiedByName = "formatGBSize")
    TempDataExcel dbToExcel(StatisticsTempData data);

}
