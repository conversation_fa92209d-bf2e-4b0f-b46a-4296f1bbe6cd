package org.biosino.system.dto.dto;

import lombok.Data;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.other.Submitter;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/11
 */
@Data
public class AnalysisDTO {
    private String analysisNo;

    @NotBlank
    private String name;

    private String description;

    @NotBlank
    private String analysisType;

    private String customAnalysisType;

    private List<Pipeline> pipeline;

    private List<AnalysisTarget> target;

    private List<CustomTarget> customTarget;

    private List<PublishDTO> publish;

    private Submitter submitter;
}
