package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.Publish;
import org.biosino.system.dto.dto.PublishDTO;
import org.biosino.system.vo.metadata.PublishVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PublishDTOMapper {
    PublishDTOMapper INSTANCE = Mappers.getMapper(PublishDTOMapper.class);

    @Mapping(target = "tempData", ignore = true)
    Publish copy(Publish publish);

    void copyToDb(PublishDTO sourceDto, @MappingTarget Publish publish);

    void copyToVo(Publish publish, @MappingTarget PublishVO result);
}
