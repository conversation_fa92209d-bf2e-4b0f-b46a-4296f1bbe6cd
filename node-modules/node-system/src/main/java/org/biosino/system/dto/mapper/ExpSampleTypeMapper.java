package org.biosino.system.dto.mapper;

import org.biosino.common.mongo.entity.ExpSampleType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface ExpSampleTypeMapper {
    ExpSampleTypeMapper INSTANCE = Mappers.getMapper(ExpSampleTypeMapper.class);

    ExpSampleType copy(ExpSampleType publish);

    void copyTo(ExpSampleType source, @MappingTarget ExpSampleType result);
}
