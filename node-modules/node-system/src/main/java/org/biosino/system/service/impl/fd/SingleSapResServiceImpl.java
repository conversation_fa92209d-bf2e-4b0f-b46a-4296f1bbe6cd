package org.biosino.system.service.impl.fd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import com.alibaba.fastjson2.JSON;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.domain.dto.es.EsTreeItemDTO;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.mongo.entity.admin.SingleSampleResource;
import org.biosino.common.security.utils.ExpSampleTokenUtils;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.es.api.dto.FdMultipleResDTO;
import org.biosino.es.api.dto.FeatureDataPrjDTO;
import org.biosino.es.api.vo.fd.SingleSapQueryVO;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.SingleSapVO;
import org.biosino.system.repository.SingleSampleResourceRepository;
import org.biosino.system.service.fd.ISingleSapResService;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 特殊数据集 单样本多组学
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SingleSapResServiceImpl implements ISingleSapResService {
    private final RemoteNodeESSearchService remoteNodeESSearchService;
    private final SingleSampleResourceRepository singleSampleResourceRepository;

    @Override
    public PageImpl<SingleSampleResource> list(SingleSapQueryVO searchVO) {
        return singleSampleResourceRepository.list(searchVO);
    }

    @Override
    public SingleSapVO dialogList(SingleSapQueryVO searchVO) {
        final R<FdMultipleResDTO> resultR = remoteNodeESSearchService.searchSingleSap(searchVO, SecurityConstants.INNER);
        if (resultR == null || R.isError(resultR)) {
            return null;
        }
        final FdMultipleResDTO data = resultR.getData();
        final SingleSapVO vo = new SingleSapVO();

        // 删除没有权限的组学
        List<EsTreeItemDTO> esTreeItems = data.getEsTreeItems();
        removeNoPermission(esTreeItems, ExpSampleTokenUtils.getAllExperimentName());
        vo.setEsTreeItems(esTreeItems);

        vo.setDefaultCheckedKeys(data.getDefaultCheckedKeys());
        final EsPageInfo<FeatureDataPrjDTO> pageInfo = data.getPageInfo();
        if (pageInfo != null) {
            final List<FeatureDataPrjDTO> list = pageInfo.getList();
            final List<SingleSampleResource> listVo = new ArrayList<>();
            if (CollUtil.isNotEmpty(list)) {
                for (FeatureDataPrjDTO dto : list) {
                    final SingleSampleResource res = new SingleSampleResource();
                    res.setId(dto.getTypeId());
                    res.setSapID(dto.getTypeId());
                    res.setSapType(dto.getSampleType());
                    res.setSapName(dto.getName());
                    res.setExpTypes(dto.getRelaExpType());
                    res.setSubmitter(dto.getSubName());
                    res.setModifiedDate(dto.getModifiedDate());
                    res.setDes(dto.getDescription());
                    listVo.add(res);
                }
            }
            final EsPageInfo<SingleSampleResource> page = EsPageInfo.of(listVo);
            page.setTotal(pageInfo.getTotal());
            vo.setPageInfo(page);
        }
        return vo;
    }

    /**
     * 删除没有权限的组学或样本类型
     *
     * @param esTreeItems 组学或样本类型树数据
     * @param permList    当前存在的权限列表
     * @see ExpSampleTokenUtils#getAllExperimentName() 获取组学权限列表
     * @see ExpSampleTokenUtils#getAllSampleName() 获取样本权限列表
     */
    public static void removeNoPermission(final List<EsTreeItemDTO> esTreeItems, final List<String> permList) {
        // 针对已入库数据，此处不考虑权限token配置
        /*if (CollUtil.isEmpty(esTreeItems)) {
            return;
        }
        if (CollUtil.isEmpty(permList)) {
            esTreeItems.clear();
        } else {
            final Iterator<EsTreeItemDTO> iterator = esTreeItems.iterator();
            while (iterator.hasNext()) {
                final EsTreeItemDTO next = iterator.next();
                final List<EsTreeItemDTO> data = next.getData();
                final boolean noSub = CollUtil.isEmpty(data);
                if (!permList.contains(next.getFieldName()) && noSub) {
                    iterator.remove();
                } else {
                    if (!noSub) {
                        removeNoPermission(data, permList);
                        if (CollUtil.isEmpty(data)) {
                            iterator.remove();
                        }
                    }
                }
            }
        }*/
    }

    /**
     * 保存
     */
    @Override
    public boolean save(List<String> nos, String username) {
        if (CollUtil.isEmpty(nos)) {
            throw new ServiceException("未找到项目");
        }
        final List<String> uploadIds = new ArrayList<>(nos);
        final R<List<FeatureDataPrjDTO>> prjResult = remoteNodeESSearchService.searchSingleSapcByNos(nos, SecurityConstants.INNER);
        if (prjResult == null || R.isError(prjResult)) {
            throw new ServiceException("es api错误");
        }
        final List<FeatureDataPrjDTO> data = prjResult.getData();
        if (CollUtil.isEmpty(data)) {
            throw new ServiceException("未找到项目");
        }

        nos = data.stream().map(FeatureDataPrjDTO::getTypeId).collect(Collectors.toList());
        uploadIds.removeAll(nos);
        if (CollUtil.isNotEmpty(uploadIds)) {
            throw new ServiceException("未找到样本id: " + JSON.toJSONString(uploadIds));
        }

        final Map<String, SingleSampleResource> resMap = singleSampleResourceRepository.findMapBySapIDIn(nos);
        final List<SingleSampleResource> newData = new ArrayList<>();
        final Date now = new Date();
        for (FeatureDataPrjDTO item : data) {
            final String projectNo = item.getTypeId();
            if (!resMap.containsKey(projectNo)) {
                final SingleSampleResource res = new SingleSampleResource();
                res.setSapID(projectNo);
                res.setSapName(item.getName());
                res.setSapType(item.getSampleType());
                res.setSubmitter(item.getSubName());
                res.setDes(item.getDescription());
                res.setExpTypes(item.getRelaExpType());
                res.setStatus(DataStatusEnum.disable.name());
                res.setCreator(username);
                res.setCreateTime(now);
                res.setModifiedDate(now);
                newData.add(res);
            }
        }
        if (CollUtil.isNotEmpty(newData)) {
            singleSampleResourceRepository.saveAll(newData);
        }
        return true;
    }

    /**
     * 导出
     */
    @Override
    public void download(HttpServletRequest request, HttpServletResponse response) {
        final List<SingleSampleResource> all = singleSampleResourceRepository.findAll();
        if (CollUtil.isEmpty(all)) {
            throw new ServiceException("没有数据");
        }
        File singleSapFile = null;
        BigExcelWriter bigWriter = null;
        try {
            singleSapFile = Files.createTempFile("singleSap", ".xlsx").toFile();
            if (singleSapFile.exists()) {
                singleSapFile.delete();
            }
            bigWriter = cn.hutool.poi.excel.ExcelUtil.getBigWriter(singleSapFile);
            List<Map<String, Object>> rows = new ArrayList<>();
            for (SingleSampleResource item : all) {
                Map<String, Object> row = new LinkedHashMap<>();
                row.put("Sample ID", item.getSapID());
                row.put("Sample Type", item.getSapType());
                row.put("Sample Name", item.getSapName());
                row.put("Experiment Type", CollUtil.join(item.getExpTypes(), "、"));
                row.put("Description", item.getDes());
                row.put("Submitter", item.getSubmitter());
                row.put("Modified Date", DateUtils.formatDateTime(item.getModifiedDate()));
                rows.add(row);
            }
            bigWriter.write(rows, true);
            bigWriter.close();
            DownloadUtils.download(request, response, singleSapFile);
        } catch (Exception e) {
            log.error("多组学资源数据导出出错", e);
        } finally {
            IoUtil.close(bigWriter);
            if (singleSapFile != null && singleSapFile.isFile()) {
                FileUtil.del(singleSapFile);
            }
        }
    }

    @Override
    public boolean batchUpdateStatus(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        List<SingleSampleResource> list;
        if (CollUtil.isNotEmpty(ids)) {
            list = singleSampleResourceRepository.findAllByIdIn(ids);
        } else {
            list = singleSampleResourceRepository.findAll();
        }
        final boolean disable = featureDataDTO.isDisable();
        final Date now = new Date();
        for (SingleSampleResource item : list) {
            item.setStatus(disable ? DataStatusEnum.disable.name() : DataStatusEnum.enable.name());
            item.setModifiedDate(now);
        }
        singleSampleResourceRepository.saveAll(list);
        return true;
    }

    /**
     * 批量添加
     */
    @Override
    public boolean batchImport(FeatureDataDTO featureDataDTO, String username) {
        final String batchImportNosStr = StrUtil.trimToNull(featureDataDTO.getBatchImportNosStr());
        if (batchImportNosStr == null) {
            throw new ServiceException("样本ID不能为空");
        }
        final List<String> nos = Arrays.stream(batchImportNosStr.split("\n")).map(StrUtil::trimToNull).filter(Objects::nonNull).collect(Collectors.toList());
        save(nos, username);
        return true;
    }

    /**
     * 批量删除
     */
    @Override
    public boolean batchDelete(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            singleSampleResourceRepository.deleteByIdIn(ids);
        }
        return true;
    }

}
