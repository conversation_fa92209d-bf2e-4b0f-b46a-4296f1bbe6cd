package org.biosino.system.dto.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.dto.BaseQuery;

import java.util.List;

/**
 * <AUTHOR> @date 2024/4/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MetadataQueryDTO extends BaseQuery {

    private String name;

    private List<String> nos;

    private String creatorEmail;

    private String creator;

    private List<String> tags;

    private String submitterEmail;

    private String submitterOrgName;

    private List<String> expTypes;

    private List<String> organisms;

    private List<String> sapTypes;

    private List<String> targetNos;

    private List<String> analTypes;

    private List<AdvQueryDTO> advQueryList;

    public void setName(String name) {
        this.name = StrUtil.trimToNull(name);
    }

    @Data
    public static class AdvQueryDTO {
        private String relation;
        private String queryField;
        private String inputValue;
    }

    public void checkNonQuery() {
        if (StrUtil.isBlank(this.name)
                && CollUtil.isEmpty(this.nos)
                && StrUtil.isBlank(this.creatorEmail)
                && StrUtil.isBlank(this.creator)
                && CollUtil.isEmpty(this.tags)
                && StrUtil.isBlank(this.submitterEmail)
                && StrUtil.isBlank(this.submitterOrgName)
                && CollUtil.isEmpty(this.expTypes)
                && CollUtil.isEmpty(this.organisms)
                && CollUtil.isEmpty(this.sapTypes)
                && CollUtil.isEmpty(this.analTypes)
                && CollUtil.isEmpty(this.targetNos)
                && advQueryListCheckNon()
                && this.getBeginTime() == null
                && this.getEndTime() == null) {
            throw new ServiceException("Please enter at least one query condition");
        }
    }

    private boolean advQueryListCheckNon() {
        if (CollUtil.isEmpty(this.advQueryList)) {
            return true;
        }
        return this.advQueryList.stream().allMatch(x -> StrUtil.isBlank(x.getQueryField()) && StrUtil.isBlank(x.getInputValue()));
    }
}
