package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * 样本-实验组合统计
 *
 * <AUTHOR>
 */
@Data
public class SampleExpExcel {
    // 月份
    @Excel(name = "Month")
    private String month;

    @Excel(name = "Sample Type")
    private String sapType;

    @Excel(name = "Experiment Type")
    private String expType;

    @Excel(name = "Experiment Total", cellType = Excel.ColumnType.NUMERIC)
    private Long expTotal = 0L;

    @Excel(name = "Experiment Total Size (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Long expTotalFileSize = 0L;

    @Excel(name = "Experiment Private Total (TB)", cellType = Excel.ColumnType.NUMERIC)
    private Long expPrivateFileSize = 0L;
}
