package org.biosino.system.dto.dto.standmg;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * <AUTHOR>
 */
@Data
public class StandSetExportDTO {
    @Excel(name = "数据集")
    private String dataSet;

    @Excel(name = "子模块")
    private String submodule;

    @Excel(name = "字段名称")
    private String fieldName;

    @Excel(name = "字段含义")
    private String fieldMeaning;

    @Excel(name = "数据类型")
    private String dataType;

    @Excel(name = "是否唯一")
    private String unique;

    @Excel(name = "是否必填")
    private String required;

//    @Excel(name = "是否为空")
//    private String empty;

//    @Excel(name = "约束表")
//    private String constraintTable;

//    @Excel(name = "约束字段")
//    private String constraintField;

    @Excel(name = "引入版本")
    private String version;

    @Excel(name = "引入日期", dateFormat = "yyyy/MM/dd")
    private String introductionDate;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "父模板")
    private String parentRecord;

}
