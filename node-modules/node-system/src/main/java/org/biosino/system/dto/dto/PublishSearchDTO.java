package org.biosino.system.dto.dto;

import lombok.Data;
import org.biosino.common.mongo.dto.BaseQuery;

/**
 * 文献管理 查询条件
 *
 * <AUTHOR>
 */
@Data
public class PublishSearchDTO extends BaseQuery {

    private String id;

    private String typeId;

    // journal
    private String publication;

    private String doi;

    private String pmid;

    private Integer sort;

    private String status;

    // title
    private String articleName;

    private String reference;

    private String createEmail;
    private String creator;

    private Boolean deleted;

}
