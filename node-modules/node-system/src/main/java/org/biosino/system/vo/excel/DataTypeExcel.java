package org.biosino.system.vo.excel;

import lombok.Data;
import org.biosino.common.core.annotation.Excel;

/**
 * 数据类型统计导出Excel
 *
 * <AUTHOR>
 * @date 2024/7/31
 */
@Data
public class DataTypeExcel {

    @Excel(name = "Month")
    private String month;

    @Excel(name = "Total Types", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long allTypeNum = 0L;

    @Excel(name = "Type")
    private String type;

    @Excel(name = "Total", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long total = 0L;

    @Excel(name = "Total Public", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalPublic = 0L;

    @Excel(name = "Total Restricted", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalRestricted = 0L;

    @Excel(name = "Total Private", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalPrivate = 0L;

    @Excel(name = "Total File Size (GB)", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalFileSize = 0L;

    @Excel(name = "Total Public File Size (GB)", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalPublicFileSize = 0L;

    @Excel(name = "Total Restricted File Size (GB)", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalRestrictedFileSize = 0L;

    @Excel(name = "Total Private File Size (GB)", cellType = Excel.ColumnType.NUMERIC, needMerge = true)
    private Long totalPrivateFileSize = 0L;

}
