package org.biosino.system.service.impl.fd;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.sys.DataStatusEnum;
import org.biosino.common.core.enums.sys.ExpSampleTypeEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.utils.bean.BeanUtils;
import org.biosino.common.core.utils.poi.ExcelUtil;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.entity.admin.HumanResource;
import org.biosino.common.redis.service.RedisService;
import org.biosino.es.api.RemoteNodeESSearchService;
import org.biosino.system.api.domain.SysDictData;
import org.biosino.system.domain.dto.FeatureDataDTO;
import org.biosino.system.domain.vo.SelectVo;
import org.biosino.system.domain.vo.SpecialHumanResourceVo;
import org.biosino.system.repository.ExpSampleTypeRepository;
import org.biosino.system.repository.HumanResourceRepository;
import org.biosino.system.repository.ProjectRepository;
import org.biosino.system.service.ISysDictTypeService;
import org.biosino.system.service.fd.IFeatureDataService;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

import static org.biosino.system.service.impl.fd.FeatureDataWebServiceImpl.getFdCacheKey;

/**
 * 特殊数据集 人类（微生物）资源
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FeatureDataServiceImpl implements IFeatureDataService {
    private final RemoteNodeESSearchService remoteNodeESSearchService;
    private final ProjectRepository projectRepository;
    private final HumanResourceRepository humanResourceRepository;
    private final ExpSampleTypeRepository expSampleTypeRepository;
    private final ISysDictTypeService sysDictTypeService;

    private final RedisService redisService;

    @Override
    public SpecialHumanResourceVo list(final HumanResource search, final Pageable pageable) {
        final PageImpl<HumanResource> pageInfo = humanResourceRepository.search(search, pageable);

        final boolean microbeFlag = search.isMicrobeFlag();
        final SpecialHumanResourceVo vo = new SpecialHumanResourceVo();
        vo.setSelectVoList(initSelectVoList(microbeFlag));
        vo.setPageInfo(pageInfo);

        // 添加下拉框数据
        final List<HumanResource> all = all(microbeFlag);
        final Set<String> existCategory1 = vo.getExistCategory1();
        final Set<String> existCategory2 = vo.getExistCategory2();
        final Set<String> existCategory3 = vo.getExistCategory3();
        final Set<String> existProjectNo = vo.getExistProjectNo();
        for (HumanResource humanResource : all) {
            final String category1 = humanResource.getCategory1();
            if (StrUtil.isNotBlank(category1)) {
                existCategory1.add(category1);
            }
            final String category2 = humanResource.getCategory2();
            if (StrUtil.isNotBlank(category2)) {
                existCategory2.add(category2);
            }
            final String category3 = humanResource.getCategory3();
            if (StrUtil.isNotBlank(category3)) {
                existCategory3.add(category3);
            }
            final String projectNo = humanResource.getProjectNo();
            if (StrUtil.isNotBlank(projectNo)) {
                existProjectNo.add(projectNo);
            }
        }
        return vo;
    }

    private List<HumanResource> all(final boolean microbeFlag) {
        return humanResourceRepository.findAllByMicrobeFlag(microbeFlag);
    }

    /**
     * 模糊查询项目编号列表
     */
    @Override
    public List<String> searchPrjId(final String keyword) {
        final R<List<String>> result = remoteNodeESSearchService.searchPrjId(keyword, SecurityConstants.INNER);
        if (result == null || R.isError(result)) {
            return new ArrayList<>();
        }
        return result.getData();
    }

    public List<String> initAllCat1(final boolean microbeFlag) {
        if (microbeFlag) {
            return expSampleTypeRepository.findLvl1NameByType(ExpSampleTypeEnum.sample);
        } else {
            return allNames();
        }
    }

    private HumanResource checkHumanResource(final HumanResource humanResource, final String operName, List<String> allCat1) {
        if (StrUtil.isBlank(operName)) {
            throw new ServiceException("没有权限");
        }
        final String category1 = humanResource.getCategory1();
        if (StrUtil.isBlank(category1) || !allCat1.contains(category1)) {
            throw new ServiceException("类别1无效");
        }
        final String projectNo = humanResource.getProjectNo();
        final Project project = projectRepository.findPublicProjectByNo(projectNo);
        if (project == null) {
            throw new ServiceException("项目ID无效:" + projectNo);
        }

        // 当存在分类3时，分类2必须存在
        if (StrUtil.isNotBlank(humanResource.getCategory3()) && StrUtil.isBlank(humanResource.getCategory2())) {
            throw new ServiceException("当类别3存在时，类别2不能为空，错误: " + projectNo);
        }

        /*final R<StatDTO> statDTOResult = remoteNodeESSearchService.statByPrj(projectNo, SecurityConstants.INNER);
        if (statDTOResult != null && R.isSuccess(statDTOResult)) {
            humanResource.setStatInfo(statDTOResult.getData());
        }*/

        final String id = humanResource.getId();
        final Date now = new Date();
        if (StrUtil.isBlank(id)) {
            // 新增
            humanResource.setStatus(DataStatusEnum.disable.name());
            humanResource.setCreateTime(now);
            humanResource.setCreator(operName);
        }
        humanResource.setUpdateTime(now);
        //空白字符字段设置为null
        return BeanUtils.trimStrToNullFields(humanResource);
    }

    @Override
    public boolean saveHumanResource(final HumanResource humanResource, final String operName, List<String> allCat1) {
        redisService.deleteObject(getFdCacheKey(humanResource.isMicrobeFlag()));
        // 保存入库
        humanResourceRepository.save(checkHumanResource(humanResource, operName, allCat1));
        return true;
    }

    /**
     * 批量更新数据状态
     */
    @Override
    public boolean batchUpdateHrStatus(final FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        List<HumanResource> list;
        if (CollUtil.isNotEmpty(ids)) {
            list = humanResourceRepository.findAllByIdIn(ids);
        } else {
            list = all(featureDataDTO.isMicrobeFlag());
        }
        final boolean disable = featureDataDTO.isDisable();
        final Date now = new Date();
        for (HumanResource humanResource : list) {
            humanResource.setStatus(disable ? DataStatusEnum.disable.name() : DataStatusEnum.enable.name());
            humanResource.setUpdateTime(now);
        }
        humanResourceRepository.saveAll(list);
        return true;
    }

    /**
     * 批量删除
     */
    @Override
    public boolean batchDeleteHr(FeatureDataDTO featureDataDTO) {
        final List<String> ids = featureDataDTO.getIds();
        if (CollUtil.isNotEmpty(ids)) {
            humanResourceRepository.deleteByIdIn(ids);
        }
        return true;
    }

    @Override
    public void downloadHr(HttpServletResponse response, Boolean microbeFlag) {
        microbeFlag = Boolean.TRUE.equals(microbeFlag);
        final List<HumanResource> list = all(microbeFlag);
        if (CollUtil.isNotEmpty(list)) {
            final ExcelUtil<HumanResource> util = new ExcelUtil<>(HumanResource.class);
            util.exportExcel(response, handleCat1(list, true, microbeFlag, expSampleTypeRepository, sysDictTypeService), sheetName(microbeFlag));
        }
    }

    public static String sheetName(final Boolean microbeFlag) {
        return Boolean.TRUE.equals(microbeFlag) ? "Microbe Resource" : "Human Resource";
    }

    public static List<HumanResource> handleCat1(final List<HumanResource> list, final boolean updateCat1, boolean microbeFlag,
                                                 final ExpSampleTypeRepository expSampleTypeRepository, final ISysDictTypeService sysDictTypeService) {
        if (CollUtil.isNotEmpty(list)) {
            final Map<String, String> nameDesMap = microbeFlag ? initSapTypeMap(expSampleTypeRepository) : nameDesMap(sysDictTypeService);
            for (HumanResource humanResource : list) {
                final String des = nameDesMap.get(humanResource.getCategory1());
                humanResource.setCategory1Des(des);
                if (updateCat1) {
                    humanResource.setCategory1(des);
                }
            }
        }
        return list;
    }

    /**
     * 数据批量导入
     */
    @Override
    public String importData(final MultipartFile file, final String operName, Boolean microbeFlag, final Boolean deleteOld) {
        if (StrUtil.isBlank(operName)) {
            throw new ServiceException("没有权限");
        }
        InputStream inputStream = null;
        ExcelReader reader = null;
        try {
            microbeFlag = Boolean.TRUE.equals(microbeFlag);

            inputStream = file.getInputStream();
            reader = cn.hutool.poi.excel.ExcelUtil.getReader(inputStream);
            final List<Map<String, Object>> maps = reader.readAll();
            if (CollUtil.isNotEmpty(maps)) {
                final Map<String, String> cat1Map = initCat1Map(microbeFlag);
                final List<String> allCat1 = new ArrayList<>(cat1Map.values());
                final List<HumanResource> data = new ArrayList<>();
                for (Map<String, Object> map : maps) {
                    HumanResource humanResource = new HumanResource();
                    humanResource.setCategory1(cat1Map.get(strVal(map.get("Category 1"))));
                    humanResource.setCategory2(strVal(map.get("Category 2")));
                    humanResource.setCategory3(strVal(map.get("Category 3")));
                    humanResource.setComment(strVal(map.get("Comment")));
                    humanResource.setProjectNo(strVal(map.get("Project ID")));
                    humanResource.setMicrobeFlag(microbeFlag);
                    humanResource = checkHumanResource(humanResource, operName, allCat1);
                    data.add(humanResource);
                }

                redisService.deleteObject(getFdCacheKey(microbeFlag));

                if (Boolean.TRUE.equals(deleteOld)) {
                    humanResourceRepository.deleteByMicrobeFlag(microbeFlag);
                }
                humanResourceRepository.saveAll(data);
            }
            return "success";
        } catch (Exception e) {
            log.warn("导入失败", e);
            return e.getMessage();
        } finally {
            IoUtil.close(inputStream);
            IoUtil.close(reader);
        }
    }

    private Map<String, String> initCat1Map(final Boolean microbeFlag) {
        Map<String, String> map;
        if (Boolean.TRUE.equals(microbeFlag)) {
            map = initSapTypeMap(expSampleTypeRepository);
        } else {
            map = desNameMap();
        }
        return map;
    }

    private static Map<String, String> initSapTypeMap(ExpSampleTypeRepository expSampleTypeRepository) {
        final List<String> allNameOfSap = expSampleTypeRepository.findLvl1NameByType(ExpSampleTypeEnum.sample);
        Map<String, String> map = new HashMap<>();
        for (String name : allNameOfSap) {
            map.put(name, name);
        }
        return map;
    }

    private String strVal(Object obj) {
        return obj == null ? null : StrUtil.trimToNull(obj.toString());
    }

    private List<SelectVo> initSelectVoList(boolean microbeFlag) {
        final List<SelectVo> selectVoList = new ArrayList<>();
        if (microbeFlag) {
            final List<String> allNameOfSap = expSampleTypeRepository.findLvl1NameByType(ExpSampleTypeEnum.sample);
            for (String name : allNameOfSap) {
                final SelectVo selectVo = new SelectVo();
                selectVo.setLabel(name);
                selectVo.setValue(name);
                selectVoList.add(selectVo);
            }
        } else {
            final List<SysDictData> sysDictData = humanResCat1List(sysDictTypeService);
            if (CollUtil.isNotEmpty(sysDictData)) {
                for (SysDictData item : sysDictData) {
                    final SelectVo selectVo = new SelectVo();
                    selectVo.setLabel(item.getDictLabel());
                    selectVo.setValue(item.getDictValue());
                    selectVoList.add(selectVo);
                }
            }
        }
        return selectVoList;
    }

    public static List<SysDictData> humanResCat1List(final ISysDictTypeService sysDictTypeService) {
        return sysDictTypeService.selectDictDataByType("fd_human_res_cat1");
    }

    private List<String> allNames() {
        final List<SysDictData> list = humanResCat1List(sysDictTypeService);
        List<String> data = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            data = list.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        }
        return data;
    }

    private Map<String, String> desNameMap() {
        final List<SysDictData> list = humanResCat1List(sysDictTypeService);
        Map<String, String> data = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            data = list.stream().collect(Collectors.toMap(SysDictData::getDictLabel, SysDictData::getDictValue));
        }
        return data;
    }

    private static Map<String, String> nameDesMap(final ISysDictTypeService sysDictTypeService) {
        final List<SysDictData> list = humanResCat1List(sysDictTypeService);
        Map<String, String> data = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            data = list.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        }
        return data;
    }

}
