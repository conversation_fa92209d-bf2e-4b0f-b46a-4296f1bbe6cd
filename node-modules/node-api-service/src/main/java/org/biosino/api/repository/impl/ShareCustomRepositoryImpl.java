package org.biosino.api.repository.impl;

import lombok.RequiredArgsConstructor;
import org.biosino.api.repository.ShareCustomRepository;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.Share;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@RequiredArgsConstructor
public class ShareCustomRepositoryImpl implements ShareCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public List<Share> findAllByShareTo(String... email) {
        Criteria criteria = Criteria.where("share_to").in(email)
                .and("status").is(ShareStatusEnum.sharing.name());
        return mongoTemplate.find(Query.query(criteria), Share.class);
    }

    @Override
    public boolean existShareByTypeNoAndEmail(String type, String no, String email) {
        TypeInformation typeInfo = TypeInformation.typeInfoMap.get(type);
        Criteria criteria = Criteria.where("share_to").is(email)
                .and("status").is(ShareStatusEnum.sharing.name())
                .and(typeInfo.getShareMongoQueryField()).is(no);
        return mongoTemplate.exists(Query.query(criteria), Share.class);
    }
}
