package org.biosino.sftp.ftp.download.path;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.sshd.common.file.util.BasePath;
import org.biosino.common.core.constant.ConfigConstants;
import org.biosino.sftp.ftp.download.DownloadFileSystem;
import org.biosino.sftp.ftp.download.iterator.*;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.FileSystem;
import java.nio.file.LinkOption;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.spi.FileSystemProvider;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * 路径转换类，将ftp的uri转换
 */
@Slf4j
public class CommonPath extends BasePath<CommonPath, DownloadFileSystem> {

    private static final Map<Pattern, Class<? extends Iterator<Path>>> iteratorClazzMap = new LinkedHashMap<>();

    /**
     * todo ID_LENGTH
     * 用于匹配路径的正则表达式
     * ConfigConstants.ID_LENGTH 是网页里面ID的数字部分长度
     * (ConfigConstants.ID_LENGTH + 2)是因为向右偏移了两位
     */
    static {
        // 原有的代码
        // iteratorClazzMap.put(Pattern.compile("^/+\\s*$"), RootIterator.class);
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)(\\s|/)*$"), SecurityIterator.class);
        //
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byRun)(\\s|/)*$"), ByRunIterator.class);
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byRun)(/((NODER|OER)[0-9]{2,8}))+(\\s|/)*$"), RunIterator.class);
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byRun)(/((NODER|OER)[0-9]{2,8}))+/+([^/]+)$"), null);
        //
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byAnalysis)(\\s|/)*$"), ByAnalysisIterator.class);
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byAnalysis)(/((NODEZ|OEZ)[0-9]{2,8}))+(\\s|/)*$"), AnalysisIterator.class);
        // iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byAnalysis)(/((NODEZ|OEZ)[0-9]{2,8}))+/+([^/]+)$"), null);

        // 修改后的代码
        iteratorClazzMap.put(Pattern.compile("^/+\\s*$"), RootIterator.class);
        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)(\\s|/)*$"), SecurityIterator.class);

        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byRun)(\\s|/)*$"), ByRunIterator.class);
        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byRun)(/((NODER|OER)[0-9]{2," + (ConfigConstants.ID_LENGTH + 2) + "}))+(\\s|/)*$"), RunIterator.class);
        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byRun)(/((NODER|OER)[0-9]{2," + (ConfigConstants.ID_LENGTH + 2) + "}))+/+([^/]+)$"), null);

        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byAnalysis)(\\s|/)*$"), ByAnalysisIterator.class);
        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byAnalysis)(/((NODEZ|OEZ)[0-9]{2," + (ConfigConstants.ID_LENGTH + 2) + "}))+(\\s|/)*$"), AnalysisIterator.class);
        iteratorClazzMap.put(Pattern.compile("^/+(Private|Restricted|Public)/+(byAnalysis)(/((NODEZ|OEZ)[0-9]{2," + (ConfigConstants.ID_LENGTH + 2) + "}))+/+([^/]+)$"), null);
    }

    private AttributeView attributeView;

    // 如果是目录，则指定迭代器
    private Class<? extends Iterator<Path>> iteratorClazz;

    // 安全等级
    private String security;

    // byRun 或 byAnalysis
    private String type;

    // Run 或者 Analysis 的编号
    private String no;

    public CommonPath(DownloadFileSystem fileSystem, String root, List<String> names) {
        super(fileSystem, root, names);
        // 初始化路径信息
        initPath();
    }

    private void initPath() {
        boolean matched = false;
        for (Pattern key : iteratorClazzMap.keySet()) {
            Matcher matcher = key.matcher(this.toString());
            if (matcher.matches()) {
                int groupCount = matcher.groupCount();
                if (groupCount > 1) {
                    this.security = matcher.group(1);
                }
                if (groupCount > 2) {
                    this.type = matcher.group(2);
                }
                if (groupCount > 4) {
                    this.no = matcher.group(4);
                }
                this.iteratorClazz = iteratorClazzMap.get(key);

                matched = true;
                break;
            }
        }
        if (matched) {
            DownloadFileSystem fileSystem = this.getFileSystem();
            this.attributeView = new AttributeView(this, fileSystem.getFtpDbService(), fileSystem.getMemberHolder());
        }
    }

    public Class<? extends Iterator<Path>> iteratorClazz() {
        return this.iteratorClazz;
    }

    @SneakyThrows
    @Override
    public File toFile() {
        if (this.getAttributeView() == null) {
            throw new FileNotFoundException("Path error");
        }

        BasicFileAttributes attributes = this.getAttributeView().readAttributes();
        if (attributes instanceof FileAttributes) {
            return ((FileAttributes) attributes).realFile();
        }

        CommonPath absolute = toAbsolutePath();
        DownloadFileSystem fs = getFileSystem();
        Path path = fs.getRoot();
        for (String n : absolute.names) {
            path = path.resolve(n);
        }
        return path.toFile();
    }

    @Override
    public CommonPath toRealPath(LinkOption... options) throws IOException {
        CommonPath absolute = toAbsolutePath();
        FileSystem fs = getFileSystem();
        FileSystemProvider provider = fs.provider();
        provider.checkAccess(absolute);
        return absolute;
    }

    public String getSecurity() {
        return security;
    }

    public String getType() {
        return type;
    }

    public String getNo() {
        return no;
    }

    public AttributeView getAttributeView() {
        return attributeView;
    }
}
