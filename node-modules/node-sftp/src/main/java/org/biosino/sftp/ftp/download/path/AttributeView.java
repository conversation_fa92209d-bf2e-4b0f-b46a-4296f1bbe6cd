package org.biosino.sftp.ftp.download.path;

import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.entity.Data;
import org.biosino.common.mongo.entity.Run;
import org.biosino.sftp.authentication.MemberHolder;
import org.biosino.sftp.db.FtpDbService;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.AccessDeniedException;
import java.nio.file.attribute.BasicFileAttributeView;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;

@Slf4j
public class AttributeView implements BasicFileAttributeView {

    private CommonPath commonPath;
    private FtpDbService ftpDbService;
    private MemberHolder memberHolder;
    private BasicFileAttributes attributes;

    public AttributeView(CommonPath commonPath, FtpDbService ftpDbService, MemberHolder memberHolder) {
        this.commonPath = commonPath;
        this.ftpDbService = ftpDbService;
        this.memberHolder = memberHolder;
    }

    @Override
    public String name() {
        return "node";
    }

    @Override
    public BasicFileAttributes readAttributes() throws IOException {
        if (this.attributes == null) {
            this.attributes = createAttributes();
        }
        return this.attributes;
    }

    private BasicFileAttributes createAttributes() throws FileNotFoundException {
        // 文件夹
        if (commonPath.iteratorClazz() != null) {
            return new DirectoryAttributes();
        }

        Data data = null;
        if ("byRun".equalsIgnoreCase(commonPath.getType())) {
            data = runFileData(commonPath);
        }
        if ("byAnalysis".equalsIgnoreCase(commonPath.getType())) {
            data = analysisFileData(commonPath);
        }

        if (data == null) {
            throw new FileNotFoundException("file not found");
        }

        return new FileAttributes(data);
    }

    private Data runFileData(CommonPath commonPath) throws FileNotFoundException {
        Run run = ftpDbService.getRun(commonPath.getNo());
        if (run == null) {
            throw new FileNotFoundException("File not found.");
        }
        // 2024/12/17注释，因为文件名前面加了DataNo
        // Data data = ftpDbService.getData(commonPath.getNo(), commonPath.getFileName().toString());

        // 2024/12/17增加，因为文件名前面加了DataNo
        String dataNo = ReUtil.getGroup0("^OED[^_]+", commonPath.getFileName().toString());
        Data data;
        if (StrUtil.isNotBlank(dataNo)) {
            data = ftpDbService.getDataByDataNo(dataNo);
        } else {
            data = ftpDbService.getData(commonPath.getNo(), commonPath.getFileName().toString());
        }
        return data;
        // if (data == null || StringUtils.isBlank(data.getSecurity())) {
        //     throw new FileNotFoundException("file not found");
        // }
        //
        // if (memberHolder.getId().equals(run.getCreator())) {
        //     return data;
        // }
        //
        // Collection<Data> ownDatas = ftpDbService.searchUserSelfDataNo(memberHolder.getId(), data.getDatNo());
        // if (ownDatas.size() != 0) {
        //     return data;
        // }
        // Collection<ResourceAuthorize> resourceAuthorizes = ftpDbService.searchAuthorizeToUserDataNo(memberHolder.getId(), data.getDatNo());
        // if (resourceAuthorizes.size() != 0) {
        //     return data;
        // }
        // Collection<String> shareDataNos = ftpDbService.searchShareToUserDataNo(memberHolder.getUsername(), data.getRunNo());
        // if (shareDataNos.contains(data.getDatNo())) {
        //     return data;
        // }
        //
        // return null;
    }

    private Data analysisFileData(CommonPath commonPath) throws FileNotFoundException {

        Analysis analysis = ftpDbService.getAnalysis(commonPath.getNo());
        if (analysis == null) {
            throw new FileNotFoundException("File not found.");
        }
        // 2024/12/17注释，因为文件名前面加了DataNo
        // Data data = ftpDbService.getDataByAnalysisNo(commonPath.getNo(), commonPath.getFileName().toString());

        // 2024/12/17增加，因为文件名前面加了DataNo
        String dataNo = ReUtil.getGroup0("^OED[^_]+", commonPath.getFileName().toString());
        Data data;
        if (StrUtil.isNotBlank(dataNo)) {
            data = ftpDbService.getDataByDataNo(dataNo);
        } else {
            data = ftpDbService.getDataByAnalysisNo(commonPath.getNo(), commonPath.getFileName().toString());
        }
        return data;
        // if (data == null || StringUtils.isBlank(data.getSecurity())) {
        //     throw new FileNotFoundException("file not found");
        // }
        //
        // // 如果当前analysis的创建者是当前用户，直接返回
        // if (memberHolder.getId().equals(analysis.getCreator())) {
        //     return data;
        // }
        //
        // // 如果当前data的创建者是当前用户,则直接返回
        // Collection<Data> ownDatas = ftpDbService.searchUserSelfDataNo(memberHolder.getId(), data.getDatNo());
        // if (ownDatas.size() != 0) {
        //     return data;
        // }
        //
        // // 如果当前用户有data的授权,则直接返回
        // Collection<ResourceAuthorize> resourceAuthorizes = ftpDbService.searchAuthorizeToUserDataNo(memberHolder.getId(), data.getDatNo());
        // if (resourceAuthorizes.size() != 0) {
        //     return data;
        // }
        //
        // // 如果当前用户被分享了当前data,则直接返回
        // Collection<String> shareDataNos = ftpDbService.searchShareToUserDataNoByAnalysisNo(memberHolder.getUsername(), data.getRunNo());
        // if (shareDataNos.contains(data.getDatNo())) {
        //     return data;
        // }
        //
        // // 否则返回空
        // return null;
    }

    @Override
    public void setTimes(FileTime lastModifiedTime, FileTime lastAccessTime, FileTime createTime) throws IOException {
        throw new AccessDeniedException("Access denied");
    }
}
