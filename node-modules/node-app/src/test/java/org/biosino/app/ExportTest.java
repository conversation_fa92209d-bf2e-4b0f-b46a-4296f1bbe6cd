package org.biosino.app;

import org.biosino.app.dto.GsaExportQueryDTO;
import org.biosino.app.service.GsaService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/6/4
 */
@SpringBootTest
public class ExportTest {
    @Autowired
    private GsaService gsaService;

    @Test
    public void exportGsaHumanTest() throws IOException {
        GsaExportQueryDTO queryDTO = new GsaExportQueryDTO();
        queryDTO.setProjNo("OEP004787");
        queryDTO.setSubjectType("Human");
        gsaService.exportGsaData(queryDTO, null, null);
    }

    @Test
    public void getProjectExpTypes() {
        gsaService.getProjectExpTypes("OEP000970");
    }
}
