package org.biosino.app;

import org.biosino.app.service.UserStatisticsService;
import org.biosino.common.core.utils.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @date 2024/2/27
 */
@SpringBootTest
public class NodeApplicationTest {
/*    @Autowired
    private MongoTemplate mongoTemplate;*/

    @Autowired
    private UserStatisticsService userStatisticsService;


//    @Autowired
//    private GenerateStatisticsService generateStatisticsService;

  /*  @Autowired
    private PublishService publishService;*/

/*    @Test
    public void genShareData() {
        Share share = new Share();
        share.setShareTo(CollUtil.newArrayList("<EMAIL>"));
        share.setStatus(ShareStatusEnum.sharing.name());
        List<ShareData> shareDataList = new ArrayList<>();
        ShareData shareData = new ShareData();
        shareData.setDatNo("OED00874726");
        shareDataList.add(shareData);
        share.setDatas(shareDataList);
        mongoTemplate.save(share);
    }*/

    @Test
    void TestGetDataStatistics() {
//        userStatisticsService.getDataStatistics("NPZGG2YCXFEMZPFE3CTSZNDZOA");
//        userStatisticsService.getDataStatistics("VRUMETYXU5FAZEYTLY6U5WBQBQ");
    }

    @Test
    void TestGetMyDataActivity() {
//        generateStatisticsService.calculateStatisticsForYears();
    }

    @Test
    void name() {
        System.out.println(StringUtils.isDOIFormat("10.1038/s41422-018-0052-4"));
    }
}
