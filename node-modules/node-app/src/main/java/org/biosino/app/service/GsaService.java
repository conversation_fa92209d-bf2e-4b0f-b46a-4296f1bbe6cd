package org.biosino.app.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.biosino.app.dto.GsaExportQueryDTO;
import org.biosino.app.repository.*;
import org.biosino.app.vo.ExperimentInfoVO;
import org.biosino.common.core.constant.DirConstants;
import org.biosino.common.core.constant.WebConstants;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.core.utils.DownloadUtils;
import org.biosino.common.core.utils.file.FileTypeUtils;
import org.biosino.common.core.utils.file.MyFileUtils;
import org.biosino.common.es.entity.NodeRelatedEs;
import org.biosino.common.es.mapper.NodeRelatedEsMapper;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.GsaExportParam;
import org.biosino.common.security.utils.SecurityUtils;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.dromara.easyes.core.kernel.EsWrappers;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/6/4
 */
@Service
@RequiredArgsConstructor
public class GsaService {

    private final NodeRelatedEsMapper nodeRelatedEsMapper;

    private final ProjectRepository projectRepository;

    private final ExperimentRepository experimentRepository;

    private final SampleRepository sampleRepository;

    private final RunRepository runRepository;

    private final DataRepository dataRepository;

    private final ExpSampleTypeRepository expSampleTypeRepository;

    private final GsaExportLogRepository gsaExportLogRepository;

    private final static String[] EXP_TYPES = {
            "Genomic",
            "Transcriptomic",
            "Metagenomic",
            "Metatranscriptomic",
            "Genomic single cell",
            "Transcriptomic single cell",
            "Synthetic",
            "Viral RNA"
    };

    private final static String[] SAMPLE_TYPES = {
            "Human",
            "Animalia",
            "Cell line",
            "Pathogen affecting public health",
            "Environment host",
            "Environment non-host",
            "Microbe"
    };

    private final static String[] ILLUMINA = {
            "HiSeq X Five",
            "HiSeq X Ten",
            "Illumina Genome Analyzer",
            "Illumina Genome Analyzer II",
            "Illumina Genome Analyzer IIx",
            "Illumina HiScanSQ",
            "Illumina HiSeq 1000",
            "Illumina HiSeq 1500",
            "Illumina HiSeq 2000",
            "Illumina HiSeq 2500",
            "Illumina HiSeq 3000",
            "Illumina HiSeq 4000",
            "Illumina iSeq 100",
            "Illumina NovaSeq 6000",
            "Illumina MiniSeq",
            "Illumina MiSeq",
            "NextSeq 500",
            "NextSeq 550"
    };

    private final static String[] BGISEQ = {
            "BGISEQ-500",
            "BGISEQ-50",
            "MGISEQ-T7",
            "MGISEQ-2000",
            "MGISEQ-200",
            "MGISP-960",
            "MGISP-100",
            "MGIFLP"
    };

    public Collection<String> getProjectExpTypes(String projNo) {
        LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        queryWrapper.eq(NodeRelatedEs::getProjNo, projNo);
        queryWrapper.select(NodeRelatedEs::getExpType).distinct(NodeRelatedEs::getExpType);
        List<NodeRelatedEs> relatedEs = nodeRelatedEsMapper.selectList(queryWrapper);

        List<String> expTypes = relatedEs.stream().map(NodeRelatedEs::getExpType).collect(Collectors.toList());

        Collection<String> result = CollUtil.intersection(expTypes, CollUtil.newArrayList(EXP_TYPES));

        return result;
    }

    public Collection<String> getProjectSubjectTypes(String projNo) {
        LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        queryWrapper.eq(NodeRelatedEs::getProjNo, projNo);
        queryWrapper.select(NodeRelatedEs::getSapType).distinct(NodeRelatedEs::getSapType);
        List<NodeRelatedEs> relatedEs = nodeRelatedEsMapper.selectList(queryWrapper);

        List<String> sapTypes = relatedEs.stream().map(NodeRelatedEs::getSapType).collect(Collectors.toList());

        Collection<String> result = CollUtil.intersection(sapTypes, CollUtil.newArrayList(SAMPLE_TYPES));

        return result;
    }

    public List<String> getProjectDataSecurity(String projNo) {
        LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        queryWrapper.eq(NodeRelatedEs::getProjNo, projNo);
        queryWrapper.select(NodeRelatedEs::getSecurity).distinct(NodeRelatedEs::getSecurity);
        List<NodeRelatedEs> relatedEs = nodeRelatedEsMapper.selectList(queryWrapper);

        List<String> result = relatedEs.stream().map(NodeRelatedEs::getSecurity).collect(Collectors.toList());

        return result;
    }


    public List<ExperimentInfoVO> getProjectExpInfo(GsaExportQueryDTO queryDTO) {
        LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class);
        queryWrapper.eq(NodeRelatedEs::getProjNo, queryDTO.getProjNo());
        queryWrapper.in(NodeRelatedEs::getExpType, queryDTO.getExpTypes());
        queryWrapper.select(NodeRelatedEs::getExpNo, NodeRelatedEs::getExpName).distinct(NodeRelatedEs::getExpNo);
        List<NodeRelatedEs> relatedEs = nodeRelatedEsMapper.selectList(queryWrapper);
        List<ExperimentInfoVO> result = new ArrayList<>();
        for (NodeRelatedEs item : relatedEs) {
            ExperimentInfoVO vo = new ExperimentInfoVO();
            vo.setExpNo(item.getExpNo());
            vo.setName(item.getExpName());
            result.add(vo);
        }
        return result;
    }

    public void exportGsaData(GsaExportQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {

        Project project = projectRepository.findTopByProjectNo(queryDTO.getProjNo()).orElseThrow(() -> new ServiceException("No Project Found"));
        if (!StrUtil.equals(project.getCreator(), SecurityUtils.getMemberId())) {
            throw new NotPermissionException("Not permission");
        }

        LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .in(CollUtil.isNotEmpty(queryDTO.getSecurity()), NodeRelatedEs::getSecurity, queryDTO.getSecurity())
                .in(NodeRelatedEs::getExpNo, queryDTO.getExpNos())
                .eq(StrUtil.isNotBlank(queryDTO.getProjNo()), NodeRelatedEs::getProjNo, queryDTO.getProjNo())
                .in(CollUtil.isNotEmpty(queryDTO.getExpTypes()), NodeRelatedEs::getExpType, queryDTO.getExpTypes())
                .eq(StrUtil.isNotBlank(queryDTO.getSubjectType()), NodeRelatedEs::getSapType, queryDTO.getSubjectType());
        List<NodeRelatedEs> nodeRelatedEs = nodeRelatedEsMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(nodeRelatedEs)) {
            throw new ServiceException("No Data Export");
        }

        File resultDir;

        switch (queryDTO.getTemplateName()) {
            case "Solid_Tumor":
                resultDir = exportGsaSolidTumorData(nodeRelatedEs);
                break;
            case "Hematologic_Tumor":
                resultDir = exportGsaHematologicTumorData(nodeRelatedEs);
                break;
            case "Autoimmune":
                resultDir = exportGsaAutoimmuneData(nodeRelatedEs);
                break;
            case "Human_General":
                resultDir = exportGsaHumanGeneralData(nodeRelatedEs);
                break;
            case "Human_Cellline":
                resultDir = exportGsaHumanCellLineData(nodeRelatedEs);
                break;
            case "Human_Clinical_pathogen":
                resultDir = exportGsaHumanClinicalPathogenData(nodeRelatedEs);
                break;
            case "Human_associated_metagenome":
                resultDir = exportGsaHumanAssociatedMetagenomeData(nodeRelatedEs);
                break;
            case "Model_animal":
                resultDir = exportGsaModelAnimalData(nodeRelatedEs);
                break;
            case "Microbe":
                resultDir = exportGsaMicrobeData(nodeRelatedEs);
                break;
            case "Plant":
                resultDir = exportGsaPlantData(nodeRelatedEs);
                break;
            case "Virus":
                resultDir = exportGsaVirusData(nodeRelatedEs);
                break;
            case "Pathogen_Clinicla or host-associated":
                resultDir = exportGsaPathogenClinicalOrHostAssociatedData(nodeRelatedEs);
                break;
            case "Pathogen_Environmental, food orther":
                resultDir = exportGsaPathogenEnvironmentalFoodOrtherData(nodeRelatedEs);
                break;
            case "Metagenome or environmental":
                resultDir = exportGsaMetagenomeOrEnvironmentalData(nodeRelatedEs);
                break;
            case "MetagenomeMISMS.me_soil":
                resultDir = exportGsaMetagenomeMimsMeSoilData(nodeRelatedEs);
                break;
            case "MetagenomeMISMS.me_water":
                resultDir = exportGsaMetagenomeMimsMeWaterData(nodeRelatedEs);
                break;
            default:
                throw new ServiceException("当前模板不支持,开发中!!!");
        }

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());
        // total data file size
        long totalSize = 0;
        for (NodeRelatedEs item : nodeRelatedEs) {
            totalSize += item.getFileSize();
        }
        // 新增gsa_export_log
        GsaExportLog gsaExportLog = GsaExportLog.builder()
                .creator(SecurityUtils.getMemberId())
                .owner(project.getCreator())
                .params(BeanUtil.copyProperties(queryDTO, GsaExportParam.class))
                .dataNos(dataNos)
                .totalFileSize(totalSize)
                .createTime(new Date()).build();
        gsaExportLogRepository.save(gsaExportLog);

        File zipFile = ZipUtil.zip(resultDir);
        DownloadUtils.download(request, response, zipFile, queryDTO.getProjNo() + "_gsa_" + queryDTO.getTemplateName() + ".zip");
    }


    private File exportGsaSolidTumorData(List<NodeRelatedEs> nodeRelatedEs) {
        if (CollUtil.isEmpty(nodeRelatedEs)) {
            throw new ServiceException("No Data Export");
        }

        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // individual sheet的数据
        List<LinkedHashMap<String, String>> individualSheetData = new ArrayList<>();

        Map<String, String> sapIdToIndividualIdMap = new LinkedHashMap<>();
        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", StrUtil.isBlank(sample.getAttributes().get("subject_id")) ? sample.getName() : sample.getAttributes().get("subject_id"));
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("sex"), "male", "female")) {
                line.put("3", sample.getAttributes().get("sex"));
            } else {
                line.put("3", "not collected");
            }
            line.put("5", "not applicable");
            line.put("6", sample.getAttributes().get("disease_name"));
            line.put("21", sample.getAttributes().get("disease_name"));

            sapIdToIndividualIdMap.put(sapNo, id);

            individualSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("8", sample.getName());
            line.put("7", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("10", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("9", sample.getAttributes().get("sample_collection_date"));
            line.put("3", sample.getTissue());
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("4", sapIdToIndividualIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }
            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Solid_Tumor.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Solid_Tumor_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, individualSheetData, 1, 9, 11);
            writeSheet(workbook, sampleSheetData, 3, 8, 10);
            writeSheet(workbook, experimentSheetData, 4, 8, 10);
            writeSheet(workbook, runSheetData, 5, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaHematologicTumorData(List<NodeRelatedEs> nodeRelatedEs) {
        if (CollUtil.isEmpty(nodeRelatedEs)) {
            throw new ServiceException("No Data Export");
        }

        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // individual sheet的数据
        List<LinkedHashMap<String, String>> individualSheetData = new ArrayList<>();

        Map<String, String> sapIdToIndividualIdMap = new LinkedHashMap<>();
        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", StrUtil.isBlank(sample.getAttributes().get("subject_id")) ? sample.getName() : sample.getAttributes().get("subject_id"));
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("sex"), "male", "female")) {
                line.put("3", sample.getAttributes().get("sex"));
            } else {
                line.put("3", "not collected");
            }
            line.put("5", "not applicable");
            line.put("6", sample.getAttributes().get("disease_name"));
            line.put("21", sample.getAttributes().get("disease_name"));

            sapIdToIndividualIdMap.put(sapNo, id);

            individualSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("8", sample.getName());
            line.put("7", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("10", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("9", sample.getAttributes().get("sample_collection_date"));
            line.put("3", sample.getTissue());
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("4", sapIdToIndividualIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }
            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Hematologic_Tumor.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Hematologic_Tumor_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, individualSheetData, 1, 9, 11);
            writeSheet(workbook, sampleSheetData, 3, 8, 10);
            writeSheet(workbook, experimentSheetData, 4, 8, 10);
            writeSheet(workbook, runSheetData, 5, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaAutoimmuneData(List<NodeRelatedEs> nodeRelatedEs) {
        if (CollUtil.isEmpty(nodeRelatedEs)) {
            throw new ServiceException("No Data Export");
        }
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // individual sheet的数据
        List<LinkedHashMap<String, String>> individualSheetData = new ArrayList<>();

        Map<String, String> sapIdToIndividualIdMap = new LinkedHashMap<>();
        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", StrUtil.isBlank(sample.getAttributes().get("subject_id")) ? sample.getName() : sample.getAttributes().get("subject_id"));
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("sex"), "male", "female")) {
                line.put("3", sample.getAttributes().get("sex"));
            } else {
                line.put("3", "not collected");
            }
            line.put("5", "not applicable");
            line.put("6", sample.getAttributes().get("disease_name"));
            line.put("21", sample.getAttributes().get("disease_name"));

            sapIdToIndividualIdMap.put(sapNo, id);

            individualSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("8", sample.getName());
            line.put("7", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("10", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("9", sample.getAttributes().get("sample_collection_date"));
            line.put("3", sample.getTissue());
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("4", sapIdToIndividualIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }
            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Autoimmune.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Autoimmune_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, individualSheetData, 1, 9, 11);
            writeSheet(workbook, sampleSheetData, 3, 8, 10);
            writeSheet(workbook, experimentSheetData, 4, 8, 10);
            writeSheet(workbook, runSheetData, 5, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }


    /**
     * 对应的规则和模板请参考文档
     */
    public File exportGsaHumanGeneralData(List<NodeRelatedEs> nodeRelatedEs) {

        if (CollUtil.isEmpty(nodeRelatedEs)) {
            throw new ServiceException("No Data Export");
        }

        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // individual sheet的数据
        List<LinkedHashMap<String, String>> individualSheetData = new ArrayList<>();

        Map<String, String> sapIdToIndividualIdMap = new LinkedHashMap<>();
        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", StrUtil.isBlank(sample.getAttributes().get("subject_id")) ? sample.getName() : sample.getAttributes().get("subject_id"));
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("sex"), "male", "female")) {
                line.put("3", sample.getAttributes().get("sex"));
            } else {
                line.put("3", "not collected");
            }
            line.put("5", "not applicable");

            sapIdToIndividualIdMap.put(sapNo, id);

            individualSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("8", sample.getName());
            line.put("7", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());
            line.put("10", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("9", sample.getAttributes().get("sample_collection_date"));
            line.put("3", sample.getTissue());
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("4", sapIdToIndividualIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        HashMap<String, String> validateExpType = new HashMap<>();
        // 校验相同experiment,type下实验条件一样的情况
        // for (Experiment exp : expNoToExpMap.values()) {
        //     String expType = exp.getExpType();
        //     String expCondition = (String) exp.getAttributes().get("platform") +
        //             (String) exp.getAttributes().get("library_layout") +
        //             (String) exp.getAttributes().get("library_strategy") +
        //             (String) exp.getAttributes().get("library_selection");
        //     if (validateExpType.containsKey(expCondition) && validateExpType.get(expCondition).equals(expType)) {
        //         throw new ServiceException("不同的experiment下，有相同的Experiment type下实验条件一样的情况，不支持导出");
        //     }
        //     validateExpType.put(expCondition, expType);
        // }

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }

            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Human_General.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "General_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, individualSheetData, 1, 8, 10);
            writeSheet(workbook, sampleSheetData, 3, 8, 10);
            writeSheet(workbook, experimentSheetData, 4, 8, 10);
            writeSheet(workbook, runSheetData, 5, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaHumanCellLineData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // cell line information sheet的数据
        List<LinkedHashMap<String, String>> cellLineSheetData = new ArrayList<>();

        Map<String, String> sapIdToCellLineIdMap = new LinkedHashMap<>();

        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", sample.getAttributes().get("cell_line_name"));
            line.put("3", sample.getTissue());
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("sex"), "male", "female")) {
                line.put("4", sample.getAttributes().get("sex"));
            } else {
                line.put("4", "not collected");
            }

            sapIdToCellLineIdMap.put(sapNo, id);
            cellLineSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("6", sample.getName());
            line.put("5", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("8", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("7", sample.getAttributes().get("sample_collection_date"));
            line.put("3", sample.getTissue());
            line.put("4", sapIdToCellLineIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }
            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Human_Cellline.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Cellline_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, cellLineSheetData, 1, 8, 10);
            writeSheet(workbook, sampleSheetData, 2, 8, 10);
            writeSheet(workbook, experimentSheetData, 3, 8, 10);
            writeSheet(workbook, runSheetData, 4, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;

    }

    private File exportGsaHumanClinicalPathogenData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // cell line information sheet的数据
        List<LinkedHashMap<String, String>> _1stSheetData = new ArrayList<>();

        Map<String, String> sapIdToCellLineIdMap = new LinkedHashMap<>();

        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", sample.getName());
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("host_sex"), "male", "female")) {
                line.put("3", sample.getAttributes().get("host_sex"));
            } else {
                line.put("3", "not collected");
            }

            sapIdToCellLineIdMap.put(sapNo, id);
            _1stSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("5", sample.getName());
            line.put("4", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("6", sample.getOrganism());
            line.put("9", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("10", sample.getAttributes().get("sample_collection_date"));
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("12", StrUtil.isNotBlank(sample.getAttributes().get("isolate_from")) ? sample.getAttributes().get("isolate_from") : "missing");
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("13", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("13", sample.getAttributes().get("lat_lon"));
            }
            line.put("3", sapIdToCellLineIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }
            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Human_Clinical_pathogen.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Clinical_pathogen_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheetData, 1, 8, 10);
            writeSheet(workbook, sampleSheetData, 2, 8, 10);
            writeSheet(workbook, experimentSheetData, 3, 8, 10);
            writeSheet(workbook, runSheetData, 4, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaHumanAssociatedMetagenomeData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // cell line information sheet的数据
        List<LinkedHashMap<String, String>> _1stSheetData = new ArrayList<>();

        Map<String, String> sapIdToCellLineIdMap = new LinkedHashMap<>();

        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String id = "D" + (i + 1);
            line.put("1", id);
            line.put("2", sample.getName());
            if (StrUtil.equalsAnyIgnoreCase(sample.getAttributes().get("host_sex"), "male", "female")) {
                line.put("3", sample.getAttributes().get("host_sex"));
            } else {
                line.put("3", "not collected");
            }

            sapIdToCellLineIdMap.put(sapNo, id);
            _1stSheetData.add(line);
        }

        // sample sheet的数据
        List<LinkedHashMap<String, String>> sampleSheetData = new ArrayList<>();

        for (String sapNo : sapNos) {

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String sapId = sample.getSapNo().replace("OES", "S");
            line.put("1", sapId);
            line.put("2", sample.getName());
            line.put("6", sample.getName());
            line.put("4", sample.getAttributes().get("env_package"));
            line.put("5", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("7", sample.getOrganism());
            line.put("9", sample.getAttributes().get("env_biome"));
            line.put("10", sample.getAttributes().get("env_feature"));
            line.put("11", sample.getAttributes().get("env_material"));
            line.put("8", sample.getAttributes().get("sample_collection_date"));
            line.put("13", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("12", sample.getAttributes().get("host"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("14", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("14", sample.getAttributes().get("lat_lon"));
            }
            line.put("3", sapIdToCellLineIdMap.get(sapNo));

            sampleSheetData.add(line);
        }

        // experiment sheet的数据
        List<LinkedHashMap<String, String>> experimentSheetData = new ArrayList<>();

        List<String> expNos = nodeRelatedEs.stream().map(NodeRelatedEs::getExpNo).distinct().collect(Collectors.toList());

        Map<String, Experiment> expNoToExpMap = experimentRepository.findAllByExpNoIn(expNos).stream().collect(Collectors.toMap(Experiment::getExpNo, Function.identity(), (existingValue, newValue) -> existingValue));

        List<String> distinctPaired = nodeRelatedEs.stream().map(x -> x.getExpNo() + "+" + x.getSapNo()).distinct().collect(Collectors.toList());

        Map<String, String> pairNoToExpId = new HashMap<>();

        for (int i = 0; i < distinctPaired.size(); i++) {
            String pair = distinctPaired.get(i);
            String[] split = pair.split("\\+");

            String expNo = split[0];
            String sapNo = split[1];

            Experiment exp = expNoToExpMap.get(expNo);
            String sapId = sapNo.replace("OES", "S");

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            String platform = (String) exp.getAttributes().get("platform");

            String layout = (String) exp.getAttributes().get("library_layout");
            String expId = "E" + (i + 1);

            pairNoToExpId.put(pair, expId);

            line.put("1", expId);
            line.put("2", exp.getName() + "_" + sapId);
            line.put("3", sapId);
            line.put("4", platform);
            line.put("5", (String) exp.getAttributes().get("library_strategy"));
            line.put("7", (String) exp.getAttributes().get("library_strategy"));
            line.put("8", exp.getExpType().toUpperCase());
            line.put("9", (String) exp.getAttributes().get("library_selection"));
            line.put("10", layout);
            if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                line.put("10", "PAIRED");
            } else {
                line.put("10", "FRAGMENT");
            }
            String readLength1 = (String) exp.getAttributes().get("read_length_for_mate1(bp)");
            String readLength2 = (String) exp.getAttributes().get("read_length_for_mate2(bp)");

            if (ArrayUtil.containsIgnoreCase(ILLUMINA, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "150");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "150");
                }
            } else if (ArrayUtil.containsIgnoreCase(BGISEQ, platform)) {
                line.put("11", StrUtil.isNotBlank(readLength1) ? readLength1 : "100");
                if (StrUtil.equalsIgnoreCase(layout, "paired")) {
                    line.put("12", StrUtil.isNotBlank(readLength2) ? readLength2 : "100");
                }
            }
            experimentSheetData.add(line);
        }

        // run sheet的数据
        List<LinkedHashMap<String, String>> runSheetData = new ArrayList<>();

        List<String> runNos = nodeRelatedEs.stream().map(NodeRelatedEs::getRunNo).distinct().collect(Collectors.toList());

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());

        Map<String, List<Data>> runNoToDataListMap = dataRepository.findByDataNos(dataNos).stream().collect(Collectors.groupingBy(Data::getRunNo));

        Map<String, Run> runNoToRunMap = runRepository.findAllByNos(runNos).stream().collect(Collectors.toMap(Run::getRunNo, Function.identity(), (existingValue, newValue) -> existingValue));

        for (Map.Entry<String, Run> entry : runNoToRunMap.entrySet()) {
            String runNo = entry.getKey();
            Run run = entry.getValue();
            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", run.getRunNo().replace("OER", "R"));
            line.put("2", run.getName());
            line.put("3", pairNoToExpId.get(run.getExpNo() + "+" + run.getSapNo()));

            List<Data> dataList = runNoToDataListMap.get(runNo);
            if (CollUtil.isNotEmpty(dataList)) {
                if (dataList.size() > 2) {
                    throw new ServiceException(StrUtil.format("Run {} has more than 2 data, please check.", runNo));
                }
                line.put("4", FileTypeUtils.getDataTypeByName(dataList.get(0).getName()));
                if (dataList.size() == 1) {
                    Data data = dataList.get(0);
                    line.put("5", data.getName());
                    line.put("6", data.getMd5());
                }
                if (dataList.size() == 2) {
                    Data data1 = dataList.get(0);
                    Data data2 = dataList.get(1);
                    if (StrUtil.containsIgnoreCase(data1.getName(), "R1")) {
                        line.put("5", data1.getName());
                        line.put("6", data1.getMd5());
                        line.put("7", data2.getName());
                        line.put("8", data2.getMd5());
                    } else {
                        line.put("5", data2.getName());
                        line.put("6", data2.getMd5());
                        line.put("7", data1.getName());
                        line.put("8", data1.getMd5());
                    }
                }
            }
            runSheetData.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Human_associated_metagenome.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Human_associated_metagenome_1.0_us.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheetData, 1, 8, 10);
            writeSheet(workbook, sampleSheetData, 2, 8, 10);
            writeSheet(workbook, experimentSheetData, 3, 8, 10);
            writeSheet(workbook, runSheetData, 4, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaModelAnimalData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            String age = sample.getAttributes().get("age");
            if (StrUtil.isNotBlank(age)) {
                // 获取age的数字部分
                String ageNum = age.replaceAll("[^0-9]", "");
                // 获取age的单位部分
                String ageUnit = age.replaceAll("[^a-zA-Z]", "");
                line.put("8", ageNum);
                line.put("9", ageUnit);
            }
            line.put("10", sample.getAttributes().get("development_stage"));
            line.put("11", sample.getAttributes().get("sex"));
            line.put("12", sample.getTissue());
            line.put("13", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Model_animal.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Model_animal.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaMicrobeData(List<NodeRelatedEs> nodeRelatedEs) {

        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("6", StrUtil.isNotBlank(sample.getAttributes().get("isolate_from")) ? sample.getAttributes().get("isolate_from") : "not collected");
            line.put("7", StrUtil.isNotBlank(sample.getAttributes().get("strain_name")) ? sample.getAttributes().get("strain_name") : "not collected");
            line.put("8", sample.getAttributes().get("sample_collection_date"));
            line.put("9", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ? sample.getAttributes().get("geographic_location") : "not collected");

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Microbe.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Microbe.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaPlantData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("6", StrUtil.isNotBlank(sample.getAttributes().get("cultivar")) ? sample.getAttributes().get("cultivar") : "not collected");
            line.put("7", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("8", sample.getTissue());
            String age = sample.getAttributes().get("age");
            if (StrUtil.isNotBlank(age)) {
                // 获取age的数字部分
                String ageNum = age.replaceAll("[^0-9]", "");
                // 获取age的单位部分
                String ageUnit = age.replaceAll("[^a-zA-Z]", "");
                line.put("9", ageNum);
                line.put("10", ageUnit);
            }
            line.put("11", sample.getAttributes().get("dev_stage"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Plant.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Plant.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaVirusData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("6", "not collected");
            line.put("12", StrUtil.isNotBlank(sample.getAttributes().get("isolate_from")) ? sample.getAttributes().get("isolate_from") : "missing");
            line.put("10", sample.getAttributes().get("sample_collection_date"));
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Virus.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Virus.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaPathogenClinicalOrHostAssociatedData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("13", StrUtil.isNotBlank(sample.getAttributes().get("isolate_from")) ? sample.getAttributes().get("isolate_from") : "missing");
            line.put("8", sample.getSubmitter() != null ? sample.getSubmitter().getOrgName() : "");
            line.put("9", sample.getAttributes().get("sample_collection_date"));
            line.put("10", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("11", sample.getAttributes().get("natural_host"));
            line.put("12", sample.getAttributes().get("disease"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("14", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("14", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Pathogen_Clinicla or host-associated.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Pathogen_cl.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaPathogenEnvironmentalFoodOrtherData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("11", StrUtil.isNotBlank(sample.getAttributes().get("isolate_from")) ? sample.getAttributes().get("isolate_from") : "missing");
            line.put("8", sample.getSubmitter() != null ? sample.getSubmitter().getOrgName() : "");
            line.put("9", sample.getAttributes().get("sample_collection_date"));
            line.put("10", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("12", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("12", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Pathogen_Environmental, food orther.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Pathogen_env.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaMetagenomeOrEnvironmentalData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("8", sample.getAttributes().get("sample_collection_date"));
            line.put("9", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            line.put("6", sample.getAttributes().get("natural_host"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("10", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("10", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "Metagenome or environmental.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Metagenome_or_environmental.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaMetagenomeMimsMeSoilData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("6", sample.getAttributes().get("sample_collection_date"));
            line.put("7", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("8", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("8", sample.getAttributes().get("lat_lon"));
            }
            line.put("9", sample.getAttributes().get("env_biome"));
            line.put("10", sample.getAttributes().get("env_feature"));
            line.put("11", sample.getAttributes().get("env_material"));
            line.put("12", sample.getAttributes().get("depth"));
            line.put("13", sample.getAttributes().get("elevation"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "MetagenomeMISMS.me_soil.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "MetagenomeMIMS.me_soil.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportGsaMetagenomeMimsMeWaterData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("1", sample.getName());
            line.put("2", StrUtil.isBlank(sample.getDescription())
                    ? StrUtil.format("data source from {}/project/detail/{}", WebConstants.WEB_DOMAIN, nodeRelatedEs.get(0).getProjNo())
                    : sample.getDescription());

            line.put("4", sample.getName());
            line.put("5", sample.getOrganism());
            line.put("6", sample.getAttributes().get("sample_collection_date"));
            line.put("7", StrUtil.isNotBlank(sample.getAttributes().get("geographic_location")) ?
                    sample.getAttributes().get("geographic_location") : "missing");
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("8", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("8", sample.getAttributes().get("lat_lon"));
            }
            line.put("9", sample.getAttributes().get("env_biome"));
            line.put("10", sample.getAttributes().get("env_feature"));
            line.put("11", sample.getAttributes().get("env_material"));
            line.put("12", sample.getAttributes().get("depth"));
            line.put("13", sample.getAttributes().get("alkalinity"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "GSA_template", "MetagenomeMISMS.me_water.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "MetagenomeMIMS.me_water.cn.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 8, 10);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private static void writeSheet(XSSFWorkbook workbook, List<LinkedHashMap<String, String>> dataList, Integer sheetIndex, Integer headRowIndex, Integer writeFirstRowIndex) {
        XSSFSheet sheet0 = workbook.getSheetAt(sheetIndex);
        Row headerRow = sheet0.getRow(headRowIndex);
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet0.createRow(writeFirstRowIndex + i); // 从第11行开始写入
            Map<String, String> dataMap = dataList.get(i);

            for (int j = 0; j < headerRow.getLastCellNum(); j++) {
                Cell headerCell = headerRow.getCell(j);
                if (headerCell != null) {
                    headerCell.setCellType(CellType.STRING);
                    String header = headerCell.getStringCellValue();
                    String value = dataMap.get(header);
                    Cell cell = row.createCell(j);
                    cell.setCellValue(value != null ? value : "");
                }
            }
        }
    }

    public void exportSraData(GsaExportQueryDTO queryDTO, HttpServletRequest request, HttpServletResponse response) throws IOException {

        Project project = projectRepository.findTopByProjectNo(queryDTO.getProjNo()).orElseThrow(() -> new ServiceException("No Project Found"));
        if (!StrUtil.equals(project.getCreator(), SecurityUtils.getMemberId())) {
            throw new NotPermissionException("Not permission");
        }

        LambdaEsQueryWrapper<NodeRelatedEs> queryWrapper = EsWrappers.lambdaQuery(NodeRelatedEs.class)
                .in(CollUtil.isNotEmpty(queryDTO.getSecurity()), NodeRelatedEs::getSecurity, queryDTO.getSecurity())
                .eq(StrUtil.isNotBlank(queryDTO.getProjNo()), NodeRelatedEs::getProjNo, queryDTO.getProjNo())
                .in(NodeRelatedEs::getExpNo, queryDTO.getExpNos())
                .in(CollUtil.isNotEmpty(queryDTO.getExpTypes()), NodeRelatedEs::getExpType, queryDTO.getExpTypes())
                .eq(StrUtil.isNotBlank(queryDTO.getSubjectType()), NodeRelatedEs::getSapType, queryDTO.getSubjectType());
        List<NodeRelatedEs> nodeRelatedEs = nodeRelatedEsMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(nodeRelatedEs)) {
            throw new ServiceException("No Data Export");
        }

        File resultDir;

        switch (queryDTO.getTemplateName()) {
            case "Human":
                resultDir = exportSraHumanData(nodeRelatedEs);
                break;
            case "Invertebrate":
                resultDir = exportSraInvertebrateData(nodeRelatedEs);
                break;
            case "Metagenome.environmental":
                resultDir = exportSraMetagenomeData(nodeRelatedEs);
                break;
            case "Microbe":
                resultDir = exportSraMicrobeData(nodeRelatedEs);
                break;
            case "Model.organism.animal":
                resultDir = exportSraModelOrganismAnimalData(nodeRelatedEs);
                break;
            case "Pathogen.cl":
                resultDir = exportSraPathogenClData(nodeRelatedEs);
                break;
            case "Pathogen.combined":
                resultDir = exportSraPathogenCombinedData(nodeRelatedEs);
                break;
            case "Pathogen.env":
                resultDir = exportSraPathogenEnvData(nodeRelatedEs);
                break;
            case "Plant":
                resultDir = exportSraPlantData(nodeRelatedEs);
                break;
            case "Virus":
                resultDir = exportSraVirusData(nodeRelatedEs);
                break;
            default:
                throw new ServiceException("当前模板不支持,开发中!!!");
        }

        List<String> dataNos = nodeRelatedEs.stream().map(NodeRelatedEs::getDatNo).distinct().collect(Collectors.toList());
        // total data file size
        long totalSize = 0;
        for (NodeRelatedEs item : nodeRelatedEs) {
            totalSize += item.getFileSize();
        }
        // 新增gsa_export_log
        GsaExportLog gsaExportLog = GsaExportLog.builder()
                .creator(SecurityUtils.getMemberId())
                .owner(project.getCreator())
                .params(BeanUtil.copyProperties(queryDTO, GsaExportParam.class))
                .dataNos(dataNos)
                .totalFileSize(totalSize)
                .createTime(new Date()).build();
        gsaExportLogRepository.save(gsaExportLog);

        File zipFile = ZipUtil.zip(resultDir);
        DownloadUtils.download(request, response, zipFile, queryDTO.getProjNo() + "_sra_" + queryDTO.getTemplateName() + ".zip");
    }


    private File exportSraHumanData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*biomaterial_provider", StrUtil.isNotBlank(sample.getAttributes().get("biomaterial_provider")) ?
                    sample.getAttributes().get("biomaterial_provider") : sample.getSubmitter().getOrgName());
            line.put("*age", sample.getAttributes().get("age"));
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*tissue", sample.getTissue());
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("*sex", sample.getAttributes().get("sex"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Human.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Human.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 11, 12);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }


    private File exportSraInvertebrateData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*tissue", sample.getTissue());
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("breed", sample.getAttributes().get("breed"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Invertebrate.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Invertebrate.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraMetagenomeData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("*lat_lon", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("*lat_lon", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Metagenome.environmental.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Metagenome.environmental.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraMicrobeData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Microbe.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Microbe.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraModelOrganismAnimalData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*tissue", sample.getTissue());
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("*sex", sample.getAttributes().get("sex"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Model.organism.animal.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Model.organism.animal.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraPathogenClData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("isolate", sample.getAttributes().get("isolated_by"));
            line.put("*collected_by", sample.getSubmitter() != null ? sample.getSubmitter().getOrgName() : "");
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("*host", sample.getAttributes().get("natural_host"));
            line.put("*host_disease", sample.getAttributes().get("disease_name"));
            line.put("*isolation_source", sample.getAttributes().get("isolate_from"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("*lat_lon", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("*lat_lon", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Pathogen.cl.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Pathogen.cl.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraPathogenCombinedData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("isolate", sample.getAttributes().get("isolated_by"));
            line.put("*collected_by", sample.getSubmitter() != null ? sample.getSubmitter().getOrgName() : "");
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("*isolation_source", sample.getAttributes().get("isolate_from"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("*lat_lon", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("*lat_lon", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Pathogen.combined.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Pathogen.combined.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 13, 14);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraPathogenEnvData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("isolate", sample.getAttributes().get("isolated_by"));
            line.put("*collected_by", sample.getSubmitter() != null ? sample.getSubmitter().getOrgName() : "");
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("*isolation_source", sample.getAttributes().get("isolate_from"));
            if (StrUtil.isBlank(sample.getAttributes().get("lat_lon"))) {
                if (StrUtil.isNotBlank(sample.getAttributes().get("latitude")) && StrUtil.isNotBlank(sample.getAttributes().get("longitude"))) {
                    line.put("*lat_lon", sample.getAttributes().get("latitude") + " " + sample.getAttributes().get("longitude"));
                }
            } else {
                line.put("*lat_lon", sample.getAttributes().get("lat_lon"));
            }

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Pathogen.env.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Pathogen.env.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }

    private File exportSraPlantData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*tissue", sample.getTissue());
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Plant.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Plant.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }


    private File exportSraVirusData(List<NodeRelatedEs> nodeRelatedEs) {
        List<String> sapNos = nodeRelatedEs.stream().map(NodeRelatedEs::getSapNo).distinct().collect(Collectors.toList());

        Map<String, Sample> sapNoToSapMap = sampleRepository.findAllBySapNoIn(sapNos).stream().collect(Collectors.toMap(Sample::getSapNo, Function.identity(), (existingValue, newValue) -> existingValue));

        // 第一个sheet
        List<LinkedHashMap<String, String>> _1stSheet = new ArrayList<>();


        for (int i = 0; i < sapNos.size(); i++) {
            String sapNo = sapNos.get(i);

            Sample sample = sapNoToSapMap.get(sapNo);

            LinkedHashMap<String, String> line = new LinkedHashMap<>();

            line.put("*sample_name", sample.getName());
            line.put("*organism", sample.getOrganism());
            line.put("*isolate", sample.getAttributes().get("isolate"));
            line.put("*collection_date", sample.getAttributes().get("sample_collection_date"));
            line.put("*geo_loc_name", sample.getAttributes().get("geographic_location"));
            line.put("*isolation_source", sample.getAttributes().get("isolate_from"));

            _1stSheet.add(line);
        }

        // 找到模板文件
        File file = FileUtil.file(DirConstants.DATA_HOME, "SRA_template", "Virus.xlsx");

        // 创建临时目录
        File tempDir = MyFileUtils.getTempDir();
        File outputFile = FileUtil.file(tempDir, "Virus.xlsx");
        FileUtil.touch(outputFile);

        // 依次写入数据到不同的sheet
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, _1stSheet, 0, 12, 13);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
        return tempDir;
    }
}
