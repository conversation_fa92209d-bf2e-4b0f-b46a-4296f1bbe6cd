package org.biosino.app.repository;

import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.vo.PrjExpSapListSearchVO;
import org.biosino.common.mongo.entity.Experiment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


@Repository
public interface ExperimentCustomRepository {


    List<Experiment> findDetailByProjNo(String projNo);

    Experiment findByExpNoWithPermission(String expNo);

    List<Experiment> findDetailByProjNos(Collection<String> projNos);

    BrowseStatDTO findBrowseStatInfo(String expNo);

    List<Experiment> findByProNo(String projectNo);

    Experiment findByNo(String expNo);

    boolean existVisibleByNo(String no);

    Page<Experiment> findExperimentPage(UserCenterListSearchDTO queryDTO);

    List<String> getUserAuditedExpType(String memberId);

    void incHitNum(String expNo);

    Optional<Experiment> findTopByExpNo(String expNo);

    List<Experiment> findAllByExpNoIn(Collection<String> expNos);

    List<Experiment> findDetailByProjNoIn(Collection<String> projNos);

    PageImpl<Experiment> search(PrjExpSapListSearchVO searchVO);

    List<Experiment> findHasTempDataByExpNoIn(Collection<String> expNos);
}
