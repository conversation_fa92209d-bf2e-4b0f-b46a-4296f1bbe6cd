package org.biosino.app.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.repository.ExperimentCustomRepository;
import org.biosino.app.service.ProjectService;
import org.biosino.app.vo.PrjExpSapListSearchVO;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.mongo.entity.Experiment;
import org.biosino.common.mongo.entity.Share;
import org.biosino.common.security.utils.SecurityUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ExperimentCustomRepositoryImpl implements ExperimentCustomRepository {

    private final MongoTemplate mongoTemplate;


    /**
     * 根据ProjectNo下所有的实验编号
     */
    @Override
    public List<Experiment> findDetailByProjNo(String projNo) {
        Query query = Query.query(Criteria.where("proj_no").is(projNo).and("ownership")
                .is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name()));
        query.fields().include("exp_no")
                .include("proj_no")
                .include("name")
                .include("description")
                .include("exp_type")
                .include("visible_status");
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public List<Experiment> findDetailByProjNos(Collection<String> projNos) {
        Query query = Query.query(Criteria.where("proj_no").in(projNos).and("ownership")
                .is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name()));
        query.fields().include("exp_no")
                .include("proj_no")
                .include("name")
                .include("description")
                .include("exp_type")
                .include("visible_status");
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public BrowseStatDTO findBrowseStatInfo(String expNo) {
        final BrowseStatDTO result = new BrowseStatDTO();
        findRelationalStatInfo(mongoTemplate, CollUtil.toList(expNo), result);
        return result;
    }

    /**
     * 查询exp的关联data和样本信息
     */
    public static void findRelationalStatInfo(final MongoTemplate mongoTemplate, final Collection<String> expNos, final BrowseStatDTO result) {
        if (CollUtil.isEmpty(expNos)) {
            return;
        }
        // 查询关联的data信息和样本编号
        RunCustomRepositoryImpl.findBrowseStatInfoByExpOrSap(mongoTemplate, expNos, null, result);
        // 查询关联的sample
        SampleCustomRepositoryImpl.findStatInfoFromExp(mongoTemplate, result.getSapNos(), result);
    }

    /**
     * 查询exp信息
     */
    public static void findStatInfoFromSample(final MongoTemplate mongoTemplate, final Collection<String> expNos, final BrowseStatDTO result) {
        if (CollUtil.isEmpty(expNos)) {
            return;
        }
        // 查询Sample信息
        Query query = new Query();
        query.addCriteria(Criteria.where("exp_no").in(expNos).and("visible_status").is(VisibleStatusEnum.Accessible.name()));
        query.fields().include("exp_no", "exp_type");
        List<Experiment> sampleList = mongoTemplate.find(query, Experiment.class);
        final Set<String> expTypes = result.getExpTypes();
        final Set<String> expNosSet = result.getExpNos();
        for (Experiment experiment : sampleList) {
            expTypes.add(experiment.getExpType());
            expNosSet.add(experiment.getExpNo());
        }
    }

    @Override
    public Experiment findByExpNoWithPermission(String expNo) {
        Criteria criteria = new Criteria().orOperator(Criteria.where("exp_no").is(expNo),
                Criteria.where("used_ids").in(expNo));
        Query query = Query.query(criteria);
        query.addCriteria(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        Experiment experiment = mongoTemplate.findOne(query, Experiment.class);
        if (experiment == null) {
            throw new ServiceException("Not found Experiment");
        }
        if (experiment.getVisibleStatus().equals(VisibleStatusEnum.Unaccessible.name())
                && !experiment.getCreator().equals(SecurityUtils.getMemberId())
                && !shareToMember(experiment, SecurityUtils.getMemberEmail())) {
            throw new NotPermissionException("Not permission");
        }
        return experiment;
    }

    public boolean shareToMember(Experiment item, String... shareTo) {
        List<String> nos = new ArrayList<>();
        nos.add(item.getExpNo());
        if (CollUtil.isNotEmpty(item.getUsedIds())) {
            nos.addAll(item.getUsedIds());
        }
        return mongoTemplate.exists(new Query(
                Criteria.where("share_to").in(shareTo)
                        .and("experiments.exp_no").in(nos)
                        .and("status").is(ShareStatusEnum.sharing.name())), Share.class);
    }

    @Override
    public List<Experiment> findByProNo(String projectNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("source").exists(false).and("ownership").is(OwnershipEnum.self_support.getDesc());

        if (StringUtils.isNotBlank(projectNo)) {
            criteria.andOperator(Criteria.where("proj_no").is(projectNo), Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
            query.addCriteria(criteria);
        }
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public Experiment findByNo(String expNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("exp_no").is(expNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Experiment.class);
    }

    @Override
    public boolean existVisibleByNo(String expNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("exp_no").is(expNo);
        criteria.and("visible_status").is(VisibleStatusEnum.Accessible.name());
        query.addCriteria(criteria);
        return mongoTemplate.exists(query, Experiment.class);
    }

    @Override
    public Page<Experiment> findExperimentPage(UserCenterListSearchDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where("exp_no").regex(pattern),
                    Criteria.where("name").regex(pattern)));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        if (CollUtil.isNotEmpty(queryDTO.getExpTypes())) {
            criteriaList.add(Criteria.where("exp_type").in(queryDTO.getExpTypes()));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));

        // 查询数据量
        long total = mongoTemplate.count(query, Experiment.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Experiment> content = mongoTemplate.find(query, Experiment.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public List<String> getUserAuditedExpType(String memberId) {
        Query query = new Query(Criteria.where("creator").is(memberId)
                .and("audited").is(AuditEnum.audited.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query.fields().include("exp_type");
        return mongoTemplate.findDistinct(query, "exp_type", Experiment.class, String.class);
    }

    @Override
    public void incHitNum(String id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        Update update = new Update();
        update.inc("hit_num", 1);

        mongoTemplate.updateFirst(query, update, Experiment.class);
    }

    @Override
    public Optional<Experiment> findTopByExpNo(String expNo) {
        if (StrUtil.isBlank(expNo)) {
            return Optional.empty();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("exp_no").is(expNo),
                Criteria.where("used_ids").in(expNo)));
        Query query = new Query(new Criteria().andOperator(condition));
        Experiment exp = mongoTemplate.findOne(query, Experiment.class);
        return Optional.ofNullable(exp);
    }

    @Override
    public List<Experiment> findAllByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return Collections.emptyList();
        }

        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("exp_no").in(expNos),
                Criteria.where("used_ids").in(expNos)));

        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public List<Experiment> findHasTempDataByExpNoIn(Collection<String> expNos) {
        if (CollUtil.isEmpty(expNos)) {
            return Collections.emptyList();
        }

        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("exp_no").in(expNos),
                Criteria.where("used_ids").in(expNos)));

        condition.add(Criteria.where("temp_data").exists(true).ne(null));
        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public List<Experiment> findDetailByProjNoIn(Collection<String> projNos) {
        Query query = Query.query(Criteria.where("proj_no").in(projNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("audited").is(AuditEnum.audited.name()));
        query.fields().include("exp_no")
                .include("proj_no")
                .include("name")
                .include("description")
                .include("exp_type")
                .include("visible_status");
        return mongoTemplate.find(query, Experiment.class);
    }

    @Override
    public PageImpl<Experiment> search(PrjExpSapListSearchVO searchVO) {
        final List<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where("exp_type").is(searchVO.getDataType()));

        final Boolean ownerFlag = searchVO.getOwnerFlag();
        if (ownerFlag == null) {
            // 未登录
            conditions.add(Criteria.where("visibleStatus").is(VisibleStatusEnum.Accessible.name()));
        } else {
            // 已登录
            if (ownerFlag) {
                // 拥有者
                conditions.add(Criteria.where("visibleStatus").in(VisibleStatusEnum.includeExistsVisibleStatus()));
            } else {
                // 非拥有者，被共享
                final Set<String> expNos = ShareCustomRepositoryImpl.sharedExoNo(mongoTemplate, searchVO.getEmail(), searchVO.getProjectNo());
                conditions.add(new Criteria().orOperator(
                        Criteria.where("visibleStatus").is(VisibleStatusEnum.Accessible.name()),
                        Criteria.where("expNo").in(expNos)
                ));
            }
        }

        conditions.add(Criteria.where("projectNo").is(searchVO.getProjectNo()));
        conditions.add(Criteria.where("audited").ne(AuditEnum.init.name()));

        final String searchId = searchVO.getSearchId();
        if (StrUtil.isNotBlank(searchId)) {
            conditions.add(Criteria.where("expNo").regex(".*" + ReUtil.escape(searchId) + ".*", "i"));
        }

        final Query query = new Query(new Criteria().andOperator(conditions));
        final long total = mongoTemplate.count(query, Experiment.class);
        List<Experiment> list = new ArrayList<>();
        final Pageable pageable = searchVO.initPageInfo();
        if (total > 0) {
            query.with(pageable);
            list = mongoTemplate.find(query, Experiment.class);

            // 将Other类型属性数据补充到自定义数据中
            for (Experiment item : list) {
                if (ProjectService.OTHER_TYPE.equalsIgnoreCase(item.getExpType())) {
                    setCustomAttr(item);
                }
            }
        }
        return new PageImpl<>(list, pageable, total);
    }

    private void setCustomAttr(final Experiment dbData) {
        final Map<String, Object> attributes = dbData.getAttributes();
        if (CollUtil.isNotEmpty(attributes)) {
            final Map<String, String> customAttrMap = new LinkedHashMap<>();
            for (Map.Entry<String, Object> entry : attributes.entrySet()) {
                final String attr = StrUtil.trimToNull(entry.getKey());
                final Object v = entry.getValue();
                final String val = v == null ? null : StrUtil.trimToNull(v.toString());
                // 排除空属性
                if (attr != null && val != null) {
                    customAttrMap.put(attr, val);
                }
            }

            if (!customAttrMap.isEmpty()) {
                dbData.setCustomAttrOther(customAttrMap);
            }
        }
    }

}
