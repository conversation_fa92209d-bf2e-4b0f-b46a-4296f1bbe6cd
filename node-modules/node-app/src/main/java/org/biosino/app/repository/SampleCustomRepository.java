package org.biosino.app.repository;

import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.vo.PrjExpSapListSearchVO;
import org.biosino.common.mongo.entity.Sample;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Repository
public interface SampleCustomRepository {

    Class<Sample> clz();

    Sample findByNo(String sapNo);

    boolean existVisibleByNo(String sapNo);

    List<Sample> findDetailBySapNoIn(Collection<String> sapNos);

    BrowseStatDTO getBrowseStatInfo(String sapNo);

    Sample findBySapNoWithPermission(String sapNo);

    List<Sample> findSampleBySearch(String field, String value, List<String> sapNos);

    List<Sample> findSampleBySapNoIn(List<String> sapNos);

    Sample findSampleBySampleNo(String sapNo);

    List<String> getSampleNosByRunNos(Collection<String> runNos);

    Page<Sample> findSamplePage(UserCenterListSearchDTO queryDTO);

    List<String> getUserAuditedSapType(String memberId);

    List<String> getUserAuditedOrganism(String memberId);

    void incHitNum(String sapId);

    Optional<Sample> findTopBySapNo(String sapNo);

    List<Sample> findAllBySapNoIn(Collection<String> sapNos);

    PageImpl<Sample> search(PrjExpSapListSearchVO searchVO);

    List<Sample> findAllBySubjectTypeAndSapNoIn(String subjectType, Collection<String> sapNos);

    List<Sample> findHasTempDataBySapNoIn(Collection<String> sapNos);
}
