package org.biosino.app.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.biosino.app.dto.RequestDTO;
import org.biosino.app.repository.*;
import org.biosino.app.vo.ReviewDetailVO;
import org.biosino.app.vo.ReviewListVO;
import org.biosino.app.vo.ReviewVO;
import org.biosino.common.core.constant.SecurityConstants;
import org.biosino.common.core.constant.WebConstants;
import org.biosino.common.core.domain.R;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.MailTemplate;
import org.biosino.common.core.enums.ReviewStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.ip.IpUtils;
import org.biosino.common.core.utils.uuid.UUID;
import org.biosino.common.core.web.domain.AjaxResult;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.*;
import org.biosino.common.security.utils.ConfigUtils;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.RemoteDataService;
import org.biosino.es.api.dto.RelatedDataDTO;
import org.biosino.es.api.vo.detail.DataListSearchVO;
import org.biosino.es.api.vo.detail.DataShareSearchVO;
import org.biosino.system.api.RemoteMemberService;
import org.biosino.system.api.RemoteNotificationService;
import org.biosino.system.api.dto.MemberDTO;
import org.biosino.system.api.dto.SendEmailDTO;
import org.biosino.system.api.model.Member;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReviewService extends BaseService {

    private final ProjectRepository projectRepository;
    private final SampleRepository sampleRepository;
    private final ExperimentRepository experimentRepository;
    private final RunRepository runRepository;
    private final DataRepository dataRepository;
    private final AnalysisRepository analysisRepository;
    private final ReviewRepository reviewRepository;
    private final LogService logService;
    private final RemoteDataService remoteDataService;
    private final RemoteMemberService remoteMemberService;
    private final RemoteNotificationService remoteNotificationService;

    public ReviewVO getReviewList(DataShareSearchVO dto) {

        final DataListSearchVO searchVO = new DataListSearchVO();
        searchVO.setType(dto.getType());
        searchVO.setTypeNo(dto.getTypeNo());
        R<List<RelatedDataDTO>> all = remoteDataService.findAllByTypeAndNo(searchVO, SecurityConstants.INNER);
        List<RelatedDataDTO> dataList = all.getData();

        if (CollUtil.isEmpty(dataList)) {
            return null;
        }

        Set<String> projNos = new TreeSet<>();
        Set<String> analNos = new TreeSet<>();
        Set<String> expNos = new TreeSet<>();
        Set<String> sapNos = new TreeSet<>();

        for (RelatedDataDTO data : dataList) {
            if (StrUtil.isNotBlank(data.getProjNo())) {
                projNos.add(data.getProjNo());
            }
            if (StrUtil.isNotBlank(data.getExpNo())) {
                expNos.add(data.getExpNo());
            }
            if (StrUtil.isNotBlank(data.getSapNo())) {
                sapNos.add(data.getSapNo());
            }
            if (StrUtil.isNotBlank(data.getAnalNo())) {
                analNos.add(data.getAnalNo());
            }
        }

        ReviewVO reviewVO = new ReviewVO();

        reviewVO.setProjNos(CollUtil.newArrayList(projNos));
        reviewVO.setExpNos(CollUtil.newArrayList(expNos));
        reviewVO.setSapNos(CollUtil.newArrayList(sapNos));
        reviewVO.setAnalNos(CollUtil.newArrayList(analNos));

        return reviewVO;
    }

    public String saveReviewData(ReviewVO dto) {
        String memberId = SecurityUtils.getMemberId();
        Member member = SecurityUtils.getMember();

        String reviewToEmail = dto.getReviewToEmail();
        String toName = dto.getReviewToName();

        if (StrUtil.isNotBlank(reviewToEmail) && StrUtil.isBlank(toName)) {
            throw new ServiceException("Please fill in the recipient's name");
        }

        Review review = saveReviews(dto, memberId);

        String url = WebConstants.WEB_DOMAIN + "/review/detail/" + review.getReviewId() + "?code=" + review.getCode();

        if (StrUtil.isBlank(reviewToEmail)) {
            return url;
        }

        // 收件人
        if (member != null && StrUtil.isNotBlank(reviewToEmail)) {

            Map<String, Object> params = new HashMap<>();
            params.put("lastName", toName);
            params.put("reviewUserName", member.getName());
            params.put("reviewLink", url);

            Set<String> ids = new LinkedHashSet<>();

            List<String> projNos = dto.getProjNos();
            if (CollUtil.isNotEmpty(projNos)) {
                ids.addAll(projNos);
            }
            List<String> analNos = dto.getAnalNos();
            if (CollUtil.isNotEmpty(analNos)) {
                ids.addAll(analNos);
            }
            List<String> expNos = dto.getExpNos();
            if (CollUtil.isNotEmpty(expNos)) {
                ids.addAll(expNos);
            }
            List<String> sapNos = dto.getSapNos();
            if (CollUtil.isNotEmpty(sapNos)) {
                ids.addAll(sapNos);
            }

            params.put("data", StringUtils.join(ids, ","));

            SendEmailDTO sendEmailDTO = new SendEmailDTO();
            sendEmailDTO.setToEmail(reviewToEmail);
            sendEmailDTO.setMailTemplate(MailTemplate.Review_To);
            sendEmailDTO.setParams(params);

            remoteNotificationService.sendEmail(sendEmailDTO, SecurityConstants.INNER);
        }
        return url;
    }

    private Review saveReviews(ReviewVO reviewVo, String memberId) {
        if (reviewVo == null) {
            throw new ServiceException("The shared review data object cannot be empty");
        }

        if (memberId == null) {
            throw new NotPermissionException("Not permission");
        }

        Review reviewItem = new Review();

        reviewItem.setReviewToName(reviewVo.getReviewToName());
        reviewItem.setReviewToEmail(reviewVo.getReviewToEmail());

        Date date = DateUtils.parseDate(reviewVo.getValidDate());
        reviewItem.setExpireDate(date);

        reviewItem.setCreator(memberId);

        List<String> projNos = reviewVo.getProjNos();
        if (CollUtil.isNotEmpty(projNos)) {
            List<ShareProject> shareProjectList = new ArrayList<>();

            for (String projNo : projNos) {
                if (StrUtil.isBlank(projNo)) {
                    continue;
                }
                ShareProject shareProjectItem = new ShareProject();
                shareProjectItem.setProjectNo(projNo);
                Project project = projectRepository.findByNo(projNo);
                if (project != null) {
                    shareProjectItem.setName(project.getName());
                    shareProjectItem.setDescription(project.getDescription());
                    shareProjectItem.setRelatedLinks(project.getRelatedLinks());
                }
                shareProjectList.add(shareProjectItem);
            }
            reviewItem.setProjects(shareProjectList);
        }

        List<String> expNos = reviewVo.getExpNos();
        if (CollUtil.isNotEmpty(expNos)) {
            List<ShareExperiment> shareExperimentList = new ArrayList<>();

            for (String expNo : expNos) {
                if (StrUtil.isBlank(expNo)) {
                    continue;
                }
                ShareExperiment shareExperimentItem = new ShareExperiment();
                shareExperimentItem.setExpNo(expNo);

                Experiment experiment = experimentRepository.findByNo(expNo);
                if (experiment != null) {
                    shareExperimentItem.setName(experiment.getName());
                    shareExperimentItem.setProjectNo(experiment.getProjectNo());
                    shareExperimentItem.setDescription(experiment.getDescription());
                    shareExperimentItem.setRelatedLinks(experiment.getRelatedLinks());
                }
                shareExperimentList.add(shareExperimentItem);
            }
            reviewItem.setExperiments(shareExperimentList);
        }

        List<String> reviewRunNos = new ArrayList<>();
        List<ShareRun> shareRunList = new ArrayList<>();

        List<String> sapNos = reviewVo.getSapNos();
        if (CollUtil.isNotEmpty(sapNos)) {

            List<ShareSample> shareSampleList = new ArrayList<>();

            for (String sapNo : sapNos) {
                ShareSample shareSampleItem = new ShareSample();
                shareSampleItem.setSapNo(sapNo);

                Sample sample = sampleRepository.findSampleBySampleNo(sapNo);
                if (sample != null) {
                    shareSampleItem.setName(sample.getName());
                    shareSampleItem.setDescription(sample.getDescription());
                    shareSampleItem.setRelatedLinks(sample.getRelatedLinks());

                }
                shareSampleList.add(shareSampleItem);
            }
            reviewItem.setSamples(shareSampleList);
        }

        if (CollUtil.isNotEmpty(reviewVo.getExpNos()) || CollUtil.isNotEmpty(reviewVo.getSapNos())) {
            List<Run> runLists = runRepository.findAllByExpNosAndSapNos(reviewVo.getExpNos(), reviewVo.getSapNos());

            if (CollUtil.isNotEmpty(runLists)) {

                for (Run runList : runLists) {
                    ShareRun shareRunItem = new ShareRun();
                    shareRunItem.setRunNo(runList.getRunNo());
                    shareRunItem.setName(runList.getName());
                    shareRunItem.setExpNo(runList.getExpNo());
                    shareRunItem.setSapNo(runList.getSapNo());
                    shareRunItem.setDescription(runList.getDescription());
                    shareRunList.add(shareRunItem);
                    reviewRunNos.add(runList.getRunNo());
                }
            }
        }

        reviewItem.setRuns(shareRunList);

        List<String> analNos = reviewVo.getAnalNos();
        if (CollUtil.isNotEmpty(analNos)) {

            List<ShareAnalysis> shareAnalysisList = new ArrayList<>();

            for (String analNo : analNos) {
                if (StrUtil.isBlank(analNo)) {
                    continue;
                }
                ShareAnalysis shareAnalysisItem = new ShareAnalysis();
                shareAnalysisItem.setAnalNo(analNo);
                Analysis analysis = analysisRepository.findByNo(analNo);
                if (analysis != null) {
                    shareAnalysisItem.setName(analysis.getName());
                    shareAnalysisItem.setDescription(analysis.getDescription());
                }
                shareAnalysisList.add(shareAnalysisItem);
            }
            reviewItem.setAnalysis(shareAnalysisList);
        }

        List<ReviewData> shareDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(reviewRunNos)) {
            List<Data> dataLists = dataRepository.findDetailByRunNos(reviewRunNos);
            if (CollUtil.isNotEmpty(dataLists)) {
                for (Data dataList : dataLists) {
                    ReviewData reviewDataItem = new ReviewData();
                    reviewDataItem.setDataNo(dataList.getDatNo());
                    reviewDataItem.setName(dataList.getName());
                    reviewDataItem.setFileName(dataList.getFileName());
                    reviewDataItem.setRunNo(dataList.getRunNo());
                    reviewDataItem.setSize(dataList.getFileSize().toString());
                    reviewDataItem.setType(dataList.getDataType());
                    shareDataList.add(reviewDataItem);
                }
            }
        }

        List<ShareAnalysis> analysisList = reviewItem.getAnalysis();
        if (CollUtil.isNotEmpty(analysisList)) {
            List<Data> dataLists = dataRepository.findDetailByAnalNoIn(reviewVo.getAnalNos());
            if (CollUtil.isNotEmpty(dataLists)) {
                for (Data dataList : dataLists) {
                    ReviewData reviewDataItem = new ReviewData();
                    reviewDataItem.setDataNo(dataList.getDatNo());
                    reviewDataItem.setName(dataList.getName());
                    reviewDataItem.setAnlysisNo(dataList.getAnalNo());
                    reviewDataItem.setFileName(dataList.getFileName());
                    reviewDataItem.setSize(dataList.getFileSize().toString());
                    reviewDataItem.setType(dataList.getDataType());
                    shareDataList.add(reviewDataItem);
                }
            }
        }
        reviewItem.setDatas(shareDataList);

        reviewItem.setReviewDate(new Date());
        reviewItem.setStatus(ReviewStatusEnum.reviewing.name());
        reviewItem.setSee("No");
        reviewItem.setCode(UUID.getShortUUID().substring(0, 8));

        return reviewRepository.save(reviewItem);
    }

    public Long getNewReviewNum() {
        String memberId = SecurityUtils.getMemberId();
        return reviewRepository.countMyReviewNum(memberId);
    }

    public Page<ReviewListVO> getMyReviewList(RequestDTO requestDTO) {
        if (StrUtil.isNotBlank(requestDTO.getSourceEmail()) && StrUtil.isBlank(requestDTO.getSourceMember())) {
            final R<MemberDTO> nodeUser = remoteMemberService.getMemberInfoByEmail(requestDTO.getSourceEmail(), "FtpUser", SecurityConstants.INNER);
            requestDTO.setSourceMember(nodeUser.getData().getId());
        }

        PageImpl<Review> reviewList = reviewRepository.findMyReviewList(requestDTO);

        return reviewList.map(review -> {
            ReviewListVO reviewListVO = new ReviewListVO();
            reviewListVO.setId(review.getId());
            reviewListVO.setReviewId(review.getReviewId());
            reviewListVO.setReviewDate(review.getReviewDate());
            reviewListVO.setReviewToEmail(review.getReviewToEmail());
            if (requestDTO.getMyRequest() == null) {
                R<MemberDTO> nodeUser = remoteMemberService.getOneMemberByMemberId(review.getCreator(), "FtpUser", SecurityConstants.INNER);
                if (R.isError(nodeUser)) {
                    log.warn("getMyReviewList出错,用户：{}数据未查询到", review.getCreator());
                    throw new ServiceException("用户服务异常，请联系管理员");
                }
                MemberDTO memberDTO = nodeUser.getData();
                if (memberDTO != null) {
                    reviewListVO.setReviewFromEmail(memberDTO.getEmail());
                }
            }
            reviewListVO.setExpiredDate(review.getExpireDate());
            reviewListVO.setStatus(review.getStatus());
            reviewListVO.setSee(review.getSee());
            reviewListVO.setCreator(review.getCreator());
            String url = WebConstants.WEB_DOMAIN + "/review/detail/" + review.getReviewId() + "?code=" + review.getCode();
            reviewListVO.setUrl(url);

            return reviewListVO;
        });
    }

    public ReviewVO getReviewDataList(String reviewId) {
        Review review = reviewRepository.findById(reviewId).orElseThrow(() -> new ServiceException("The review data was not found"));
        List<ShareProject> projects = review.getProjects();

        ReviewVO reviewVO = new ReviewVO();
        if (CollUtil.isNotEmpty(projects)) {
            reviewVO.setProjNos(projects.stream().map(ShareProject::getProjectNo).distinct().collect(Collectors.toList()));
        }

        List<ShareAnalysis> analysisList = review.getAnalysis();
        if (CollUtil.isNotEmpty(analysisList)) {
            reviewVO.setAnalNos(analysisList.stream().map(ShareAnalysis::getAnalNo).distinct().collect(Collectors.toList()));
        }

        List<ShareExperiment> experiments = review.getExperiments();
        if (CollUtil.isNotEmpty(experiments)) {
            reviewVO.setExpNos(experiments.stream().map(ShareExperiment::getExpNo).distinct().collect(Collectors.toList()));
        }

        List<ShareSample> samples = review.getSamples();
        if (CollUtil.isNotEmpty(samples)) {
            reviewVO.setSapNos(samples.stream().map(ShareSample::getSapNo).distinct().collect(Collectors.toList()));
        }

        List<ShareRun> runs = review.getRuns();
        if (CollUtil.isNotEmpty(runs)) {
            reviewVO.setRunNos(runs.stream().map(ShareRun::getRunNo).distinct().collect(Collectors.toList()));
        }

        List<ReviewData> datas = review.getDatas();
        if (CollUtil.isNotEmpty(datas)) {
            reviewVO.setDataNos(datas.stream().map(ReviewData::getDataNo).distinct().collect(Collectors.toList()));
        }
        return reviewVO;
    }

    public void cancel(String reviewId) {
        Review review = reviewRepository.findById(reviewId).orElseThrow(() -> new ServiceException("The review data was not found"));
        review.setStatus(ReviewStatusEnum.cancled.name());
        reviewRepository.save(review);
    }

    public void extendDate(String reviewId, String date) {
        if (StrUtil.isBlank(reviewId) || StrUtil.isBlank(date)) {
            throw new ServiceException("Parameter error");
        }
        Review review = reviewRepository.findById(reviewId).orElseThrow(() -> new ServiceException("The review data was not found"));
        review.setExpireDate(DateUtils.parseDate(date));
        reviewRepository.save(review);
    }

    public AjaxResult getReviewDetail(String reviewNo, String code) {
        Review review = reviewRepository.findByNo(reviewNo, code);
        if (review == null) {
            // 查询历史用过的ID
            review = reviewRepository.findByUsedIds(reviewNo, code);
            if (review == null) {
                return AjaxResult.custom("The data does not exist! Please contact " + WebConstants.SUPPORT_EMAIL + " for support!");
            }
        }

        if (ReviewStatusEnum.cancled.name().equals(review.getStatus()) || ReviewStatusEnum.expired.name().equals(review.getStatus())
                || (review.getExpireDate() != null && review.getExpireDate().before(new Date()))) {
            return AjaxResult.custom("This review link has expired or cancelled! Please try to visit through the following link:", review);
        }

        List<ReviewDetailVO> reviewDetailList = new ArrayList<>();

        List<ShareProject> projects = review.getProjects();
        List<ShareExperiment> experiments = review.getExperiments();
        List<ShareSample> samples = review.getSamples();

        if (CollUtil.isNotEmpty(projects)) {

            for (ShareProject shareProject : projects) {
                ReviewDetailVO detailVO = new ReviewDetailVO();

                Project project = projectRepository.findByNo(shareProject.getProjectNo());
                if (project != null) {
                    detailVO.setType(AuthorizeType.project.name());
                    detailVO.setTypeNo(project.getProjectNo());
                    detailVO.setName(project.getName());
                    detailVO.setDescription(project.getDescription());
                    detailVO.setRelatedLinks(project.getRelatedLinks());
                }
                List<ShareExperiment> shareExperiments = review.getExperiments();

                if (CollUtil.isNotEmpty(shareExperiments)) {
                    List<ReviewDetailVO> childVOList = new ArrayList<>();

                    for (ShareExperiment shareExperiment : shareExperiments) {
                        if (shareExperiment.getProjectNo().equals(shareProject.getProjectNo())) {

                            ReviewDetailVO childVO = new ReviewDetailVO();

                            Experiment experiment = experimentRepository.findByNo(shareExperiment.getExpNo());
                            if (experiment != null) {
                                childVO.setType(AuthorizeType.experiment.name());
                                childVO.setTypeNo(experiment.getExpNo());
                                childVO.setName(experiment.getName());
                                childVO.setDescription(experiment.getDescription());
                                childVO.setRelatedLinks(experiment.getRelatedLinks());
                                childVO.setProtocol(experiment.getProtocol());
                                childVO.setDataType(experiment.getExpType());

                                List<ReviewDetailVO.ReviewDetailData> dataList = getDataListByExpNoAndReview(experiment.getExpNo(), review);
                                childVO.setDataList(dataList);

                                childVOList.add(childVO);
                            }
                        }
                    }
                    detailVO.setChildList(childVOList);
                }
                reviewDetailList.add(detailVO);
            }
        } else if (CollUtil.isNotEmpty(experiments)) {

            for (ShareExperiment shareExperiment : experiments) {
                ReviewDetailVO detailVO = new ReviewDetailVO();

                Experiment experiment = experimentRepository.findByNo(shareExperiment.getExpNo());
                if (experiment != null) {
                    detailVO.setType(AuthorizeType.experiment.name());
                    detailVO.setTypeNo(experiment.getProjectNo());
                    detailVO.setName(experiment.getName());
                    detailVO.setDescription(experiment.getDescription());
                    detailVO.setRelatedLinks(experiment.getRelatedLinks());
                    detailVO.setDataType(experiment.getExpType());

                    List<ReviewDetailVO.ReviewDetailData> dataList = getDataListByExpNoAndReview(experiment.getExpNo(), review);
                    detailVO.setDataList(dataList);
                }
                reviewDetailList.add(detailVO);
            }

        } else if (CollUtil.isNotEmpty(samples)) {

            for (ShareSample shareSample : samples) {
                ReviewDetailVO detailVO = new ReviewDetailVO();

                Sample sample = sampleRepository.findSampleBySampleNo(shareSample.getSapNo());
                if (sample != null) {
                    detailVO.setType(AuthorizeType.sample.name());
                    detailVO.setTypeNo(sample.getSapNo());
                    detailVO.setName(sample.getName());
                    detailVO.setDescription(sample.getDescription());
                    detailVO.setRelatedLinks(sample.getRelatedLinks());
                    detailVO.setDataType(sample.getSubjectType());

                    List<ReviewDetailVO.ReviewDetailData> dataList = getDataListBySapNoAndReview(sample.getSapNo(), review);
                    detailVO.setDataList(dataList);
                }
                reviewDetailList.add(detailVO);
            }
        }

        List<ShareAnalysis> analysisList = review.getAnalysis();

        if (CollUtil.isNotEmpty(analysisList)) {
            for (ShareAnalysis shareAnalysis : analysisList) {
                ReviewDetailVO detailVO = new ReviewDetailVO();

                Analysis analysis = analysisRepository.findByNo(shareAnalysis.getAnalNo());
                if (analysis != null) {
                    detailVO.setType(AuthorizeType.analysis.name());
                    detailVO.setTypeNo(analysis.getAnalysisNo());
                    detailVO.setName(analysis.getName());
                    detailVO.setDescription(analysis.getDescription());

                    detailVO.setDataType(analysis.getAnalysisType());
                    if (StrUtil.isNotBlank(analysis.getCustomAnalysisType())) {
                        detailVO.setDataType(analysis.getCustomAnalysisType());
                    }

                    List<ReviewDetailVO.ReviewDetailData> dataList = getDataListByAnalysisNoAndReview(analysis.getAnalysisNo(), review);
                    if (CollUtil.isNotEmpty(dataList)) {
                        detailVO.setDataList(dataList);
                    }
                    reviewDetailList.add(detailVO);
                }
            }
        }
        review.setSee("Yes");
        reviewRepository.save(review);
        String ipAddr = IpUtils.getIpAddr();
        // 将漏扫服务器访问的数量排除
        Set<String> scanIp = ConfigUtils.getBugScanIP();
        if (!scanIp.contains(ipAddr)) {
            logService.addVisitor(AuthorizeType.review.name(), reviewNo, SecurityUtils.getMemberId(), review.getCreator(), ipAddr);
        }

        return AjaxResult.success(reviewDetailList);
    }

    public List<ReviewDetailVO.ReviewDetailData> getDataListByExpNoAndReview(String expNo, Review review) {
        List<ReviewDetailVO.ReviewDetailData> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(review.getDatas())) {
            for (int i = 0; i < review.getDatas().size(); i++) {
                Run run = runRepository.findByNo(review.getDatas().get(i).getRunNo());
                if (run != null && run.getExpNo().equals(expNo)) {
                    Data data = dataRepository.findByNo(review.getDatas().get(i).getDataNo());
                    if (data != null) {
                        ReviewDetailVO.ReviewDetailData reviewData = new ReviewDetailVO.ReviewDetailData();
                        reviewData.setExpNo(run.getExpNo());
                        reviewData.setSapNo(run.getSapNo());
                        reviewData.setRunNo(run.getRunNo());
                        reviewData.setFileName(data.getName());
                        reviewData.setDatNo(data.getDatNo());
                        reviewData.setLink(getDataLink(review, data.getDatNo()));
                        result.add(reviewData);
                    }
                }
            }
        }
        return result;
    }

    private String getDataLink(Review review, String dataNo) {
        return WebConstants.WEB_DOMAIN + "/download/node/review/" + review.getReviewId() + "/" + review.getCode() + "/" + dataNo;
    }

    public List<ReviewDetailVO.ReviewDetailData> getDataListBySapNoAndReview(String sapNo, Review review) {
        List<ReviewDetailVO.ReviewDetailData> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(review.getDatas())) {
            for (int i = 0; i < review.getDatas().size(); i++) {
                Run run = runRepository.findByNo(review.getDatas().get(i).getRunNo());
                if (run != null && run.getSapNo().equals(sapNo)) {
                    Data data = dataRepository.findByNo(review.getDatas().get(i).getDataNo());
                    if (data != null) {
                        ReviewDetailVO.ReviewDetailData reviewData = new ReviewDetailVO.ReviewDetailData();
                        reviewData.setExpNo(run.getExpNo());
                        reviewData.setSapNo(run.getSapNo());
                        reviewData.setRunNo(run.getRunNo());
                        reviewData.setFileName(data.getName());
                        reviewData.setDatNo(data.getDatNo());
                        reviewData.setLink(getDataLink(review, data.getDatNo()));
                        result.add(reviewData);
                    }
                }
            }
        }
        return result;
    }

    public List<ReviewDetailVO.ReviewDetailData> getDataListByAnalysisNoAndReview(String analysisNo, Review review) {
        List<ReviewDetailVO.ReviewDetailData> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(review.getDatas())) {
            for (int i = 0; i < review.getDatas().size(); i++) {
                if (analysisNo.equals(review.getDatas().get(i).getAnlysisNo())) {
                    Data data = dataRepository.findByNo(review.getDatas().get(i).getDataNo());
                    if (data != null) {
                        ReviewDetailVO.ReviewDetailData reviewData = new ReviewDetailVO.ReviewDetailData();
                        reviewData.setAnalNo(data.getAnalNo());
                        reviewData.setFileName(data.getName());
                        reviewData.setDatNo(data.getDatNo());
                        reviewData.setLink(getDataLink(review, data.getDatNo()));
                        result.add(reviewData);
                    }
                }
            }
        }
        return result;
    }
}
