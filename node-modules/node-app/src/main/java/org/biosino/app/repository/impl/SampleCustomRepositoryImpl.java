package org.biosino.app.repository.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import org.biosino.app.dto.BrowseStatDTO;
import org.biosino.app.dto.UserCenterListSearchDTO;
import org.biosino.app.repository.SampleCustomRepository;
import org.biosino.app.service.ProjectService;
import org.biosino.app.vo.PrjExpSapListSearchVO;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.ShareStatusEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.core.exception.auth.NotPermissionException;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.entity.Share;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.es.api.dto.ProjSapNosDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class SampleCustomRepositoryImpl implements SampleCustomRepository {

    private final MongoTemplate mongoTemplate;

    @Override
    public Class<Sample> clz() {
        return Sample.class;
    }

    @Override
    public Sample findByNo(String sapNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sap_no").is(sapNo);
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Sample.class);
    }

    @Override
    public boolean existVisibleByNo(String sapNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sap_no").is(sapNo);
        criteria.and("visible_status").is(VisibleStatusEnum.Accessible.name());
        query.addCriteria(criteria);
        return mongoTemplate.exists(query, Sample.class);
    }

    @Override
    public List<Sample> findDetailBySapNoIn(Collection<String> sapNos) {
        Criteria criteria = Criteria.where("sap_no").in(sapNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        Query query = new Query(criteria);
        query.fields().include("sap_no")
                .include("name")
                .include("description")
                .include("organism")
                .include("subject_type");
        return mongoTemplate.find(query, clz());
    }

    @Override
    public BrowseStatDTO getBrowseStatInfo(String sapNo) {
        final BrowseStatDTO result = new BrowseStatDTO();
        findRelationalStatInfo(mongoTemplate, CollUtil.toList(sapNo), result);
        return result;
    }

    /**
     * 查询sample的关联data和exp信息
     */
    public static void findRelationalStatInfo(final MongoTemplate mongoTemplate, final Collection<String> sapNos, final BrowseStatDTO result) {
        if (CollUtil.isEmpty(sapNos)) {
            return;
        }
        // 查询关联的data信息和exp编号
        RunCustomRepositoryImpl.findBrowseStatInfoByExpOrSap(mongoTemplate, null, sapNos, result);
        // 查询关联的实验
        ExperimentCustomRepositoryImpl.findStatInfoFromSample(mongoTemplate, result.getExpNos(), result);
    }

    /**
     * 查询sample信息
     */
    public static void findStatInfoFromExp(final MongoTemplate mongoTemplate, final Collection<String> sapNos, final BrowseStatDTO result) {
        if (CollUtil.isEmpty(sapNos)) {
            return;
        }
        // 查询Sample信息
        Query query = new Query();
        query.addCriteria(Criteria.where("sap_no").in(sapNos).and("visible_status").is(VisibleStatusEnum.Accessible.name()));
        query.fields().include("sap_no", "subject_type");
        List<Sample> sampleList = mongoTemplate.find(query, Sample.class);
        final Set<String> sapTypes = result.getSapTypes();
        final Set<String> sapNosSet = result.getSapNos();
        for (Sample sample : sampleList) {
            sapTypes.add(sample.getSubjectType());
            sapNosSet.add(sample.getSapNo());
        }
    }

    @Override
    public Sample findBySapNoWithPermission(String sapNo) {
        Criteria criteria = new Criteria().orOperator(Criteria.where("sap_no").is(sapNo),
                Criteria.where("used_ids").in(sapNo));
        Query query = Query.query(criteria);
        Sample sample = mongoTemplate.findOne(query, clz());
        if (sample == null) {
            throw new ServiceException("Not found Sample");
        }
        if (sample.getVisibleStatus().equals(VisibleStatusEnum.Unaccessible.name())
                && !sample.getCreator().equals(SecurityUtils.getMemberId())
                && !shareToMember(sample, SecurityUtils.getMemberEmail())) {
            throw new NotPermissionException("Not permission");
        }
        return sample;
    }

    public boolean shareToMember(Sample sample, String... shareTo) {
        List<String> nos = new ArrayList<>();
        nos.add(sample.getSapNo());
        if (CollUtil.isNotEmpty(sample.getUsedIds())) {
            nos.addAll(sample.getUsedIds());
        }
        return mongoTemplate.exists(new Query(
                Criteria.where("share_to").in(shareTo)
                        .and("samples.sap_no").in(nos)
                        .and("status").is(ShareStatusEnum.sharing.name())), Share.class);
    }

    @Override
    public List<Sample> findSampleBySearch(String field, String value, List<String> sapNos) {
        Query query = new Query();
        Criteria criteria = Criteria.where(field).is(value)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.andOperator(Criteria.where("sap_no").in(sapNos));
        query.addCriteria(criteria);
        query.fields().include("sap_no");
        return mongoTemplate.find(query, Sample.class);
    }

    @Override
    public List<Sample> findSampleBySapNoIn(List<String> sapNos) {
        Query query = Query.query(Criteria.where("sap_no").in(sapNos)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query.fields().include("sap_no").include("name").include("organism").include("tissue").include("subject_type");
        return mongoTemplate.find(query, Sample.class);
    }

    @Override
    public Sample findSampleBySampleNo(String sapNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sap_no").is(sapNo);
        criteria.andOperator(Criteria.where("source").exists(false)
                .and("ownership").is(OwnershipEnum.self_support.getDesc())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        query.addCriteria(criteria);
        return mongoTemplate.findOne(query, Sample.class);
    }

    @Override
    public List<String> getSampleNosByRunNos(Collection<String> runNos) {
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").in(runNos).and("sap_no").exists(true)
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus())
                .and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        query.fields().include("sap_no");
        final List<Run> runList = mongoTemplate.find(query, Run.class);
        final Set<String> result = new LinkedHashSet<>();
        if (CollUtil.isNotEmpty(runList)) {
            for (Run run : runList) {
                if (StrUtil.isNotBlank(run.getSapNo())) {
                    result.add(run.getSapNo());
                }
            }
        }
        return new ArrayList<>(result);
    }

    @Override
    public Page<Sample> findSamplePage(UserCenterListSearchDTO queryDTO) {
        List<Criteria> criteriaList = new ArrayList<>();
        criteriaList.add(Criteria.where("creator").is(queryDTO.getCreator()));
        criteriaList.add(Criteria.where("audited").is(AuditEnum.audited.name()));
        criteriaList.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            Pattern pattern = Pattern.compile("^.*" + queryDTO.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteriaList.add(new Criteria().orOperator(Criteria.where("sap_no").regex(pattern),
                    Criteria.where("name").regex(pattern)));
        }

        if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime()) && ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())).lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getBeginTime())) {
            criteriaList.add(Criteria.where("submission_date").gte(DateUtil.beginOfDay(queryDTO.getBeginTime())));
        } else if (ObjectUtil.isNotEmpty(queryDTO.getEndTime())) {
            criteriaList.add(Criteria.where("submission_date").lte(DateUtil.endOfDay(queryDTO.getEndTime())));
        }
        if (CollUtil.isNotEmpty(queryDTO.getOrganisms())) {
            criteriaList.add(Criteria.where("organism").in(queryDTO.getOrganisms()));
        }
        if (CollUtil.isNotEmpty(queryDTO.getSapTypes())) {
            criteriaList.add(Criteria.where("subject_type").in(queryDTO.getSapTypes()));
        }

        Query query = new Query(new Criteria().andOperator(criteriaList));

        // 查询数据量
        long total = mongoTemplate.count(query, Sample.class);

        // 添加分页和排序
        query.with(queryDTO.getPageable());

        // 查询query
        List<Sample> content = mongoTemplate.find(query, Sample.class);

        return new PageImpl<>(content, queryDTO.getPageable(), total);
    }

    @Override
    public List<String> getUserAuditedOrganism(String memberId) {
        Query query = new Query(Criteria.where("creator").is(memberId)
                .and("audited").is(AuditEnum.audited.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.findDistinct(query, "organism", Sample.class, String.class);
    }

    @Override
    public List<String> getUserAuditedSapType(String memberId) {
        Query query = new Query(Criteria.where("creator").is(memberId)
                .and("audited").is(AuditEnum.audited.name())
                .and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        return mongoTemplate.findDistinct(query, "subject_type", Sample.class, String.class);
    }

    @Override
    public void incHitNum(String id) {
        Query query = Query.query(Criteria.where("_id").is(id));
        Update update = new Update();
        update.inc("hit_num", 1);

        mongoTemplate.updateFirst(query, update, Sample.class);
    }

    @Override
    public Optional<Sample> findTopBySapNo(String sapNo) {
        if (StrUtil.isBlank(sapNo)) {
            return Optional.empty();
        }

        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("sap_no").is(sapNo),
                Criteria.where("used_ids").in(sapNo)));

        Query query = new Query(new Criteria().andOperator(condition));
        Sample sample = mongoTemplate.findOne(query, Sample.class);
        return Optional.ofNullable(sample);
    }

    @Override
    public List<Sample> findAllBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("sap_no").in(sapNos),
                Criteria.where("used_ids").in(sapNos)));

        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Sample.class);
    }

    @Override
    public List<Sample> findHasTempDataBySapNoIn(Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(new Criteria().orOperator(Criteria.where("sap_no").in(sapNos),
                Criteria.where("used_ids").in(sapNos)));

        condition.add(Criteria.where("temp_data").exists(true).ne(null));

        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Sample.class);
    }

    @Override
    public PageImpl<Sample> search(PrjExpSapListSearchVO searchVO) {

        final ProjSapNosDTO sapNosDTO = searchVO.getSapNosDTO();
        final Set<String> privateNos = sapNosDTO.getPrivateNos();
        final Set<String> publicOrRestrictedNos = sapNosDTO.getPublicOrRestrictedNos();
        final Pageable pageable = searchVO.initPageInfo();
        if (CollUtil.isEmpty(privateNos) && CollUtil.isEmpty(publicOrRestrictedNos)) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }

        final List<Criteria> conditions = new ArrayList<>();
        conditions.add(Criteria.where("audited").ne(AuditEnum.init.name()));
        conditions.add(Criteria.where("subject_type").is(searchVO.getDataType()));
        final Boolean ownerFlag = searchVO.getOwnerFlag();
        if (ownerFlag == null) {
            // 未登录
            conditions.add(Criteria.where("sapNo").in(publicOrRestrictedNos));
        } else {
            // 已登录
            if (ownerFlag) {
                // 拥有者
                conditions.add(Criteria.where("sapNo").in(CollUtil.addAll(publicOrRestrictedNos, privateNos)));
            } else {
                // 非拥有者，被共享
                conditions.add(Criteria.where("sapNo").in(CollUtil.addAll(
                        publicOrRestrictedNos,
                        ShareCustomRepositoryImpl.sharedSapNo(mongoTemplate, searchVO.getEmail(), privateNos)
                )));
            }
        }

        final String searchId = searchVO.getSearchId();
        if (StrUtil.isNotBlank(searchId)) {
            conditions.add(Criteria.where("sapNo").regex(".*" + ReUtil.escape(searchId) + ".*", "i"));
        }
        final Query query = new Query(new Criteria().andOperator(conditions));
        final long total = mongoTemplate.count(query, Sample.class);
        List<Sample> list = new ArrayList<>();
        if (total > 0) {
            query.with(pageable);
            list = mongoTemplate.find(query, Sample.class);

            // 将Other类型属性数据补充到自定义数据中
            for (Sample item : list) {
                if (ProjectService.OTHER_TYPE.equalsIgnoreCase(item.getSubjectType())) {
                    setCustomAttr(item);
                }
            }
        }
        return new PageImpl<>(list, pageable, total);
    }

    private void setCustomAttr(final Sample dbData) {
        final Map<String, String> attributes = dbData.getAttributes();
        if (CollUtil.isNotEmpty(attributes)) {
            final Map<String, String> customAttrMap = new LinkedHashMap<>();
            for (Map.Entry<String, String> entry : attributes.entrySet()) {
                final String attr = StrUtil.trimToNull(entry.getKey());
                final String val = StrUtil.trimToNull(entry.getValue());
                // 排除空属性
                if (attr != null && val != null) {
                    customAttrMap.put(attr, val);
                }
            }

            if (!customAttrMap.isEmpty()) {
                dbData.setCustomAttrOther(customAttrMap);
            }
        }
    }

    @Override
    public List<Sample> findAllBySubjectTypeAndSapNoIn(String subjectType, Collection<String> sapNos) {
        if (CollUtil.isEmpty(sapNos) || StrUtil.isBlank(subjectType)) {
            return Collections.emptyList();
        }
        List<Criteria> condition = new ArrayList<>();
        condition.add(Criteria.where("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus()));
        condition.add(Criteria.where("subject_type").is(subjectType));
        condition.add(new Criteria().orOperator(Criteria.where("sap_no").in(sapNos),
                Criteria.where("used_ids").in(sapNos)));

        Query query = new Query(new Criteria().andOperator(condition));
        return mongoTemplate.find(query, Sample.class);
    }
}
