package org.biosino.upload.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.AuthorizeType;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.dto.TypeInformation;
import org.biosino.common.mongo.entity.*;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.CustomTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.sequence.SequenceType;
import org.biosino.common.security.utils.SecurityUtils;
import org.biosino.upload.api.vo.DeleteCheckResultVO;
import org.biosino.upload.api.vo.DeleteErrorMsgVO;
import org.biosino.upload.dto.*;
import org.biosino.upload.dto.mapper.AnalysisDTOMapper;
import org.biosino.upload.repository.*;
import org.biosino.upload.vo.AnalysisVO;
import org.biosino.upload.vo.ErrorMsgVO;
import org.biosino.upload.vo.PublishVO;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Li
 * @date 2024/1/10
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnalysisService extends BaseService {
    private final AnalysisRepository analysisRepository;
    private final DataRepository dataRepository;
    private final ProjectRepository projectRepository;
    private final ExperimentRepository experimentRepository;
    private final SampleRepository sampleRepository;
    private final RunRepository runRepository;
    private final ShareRepository shareRepository;
    private final PublishRepository publishRepository;
    private final ResourceAuthorizeRepository authorizeRepository;
    private final static String ANALYSIS_NAME = "analysis_name";
    private final static String ANALYSIS_TYPE = "analysis_type";
    private final static String INDEX = "index";


    public Page<SelectOption> getTargetOptions(SelectQueryDTO queryDTO) {
        TypeInformation typeInformation = TypeInformation.typeInfoMap.get(queryDTO.getType());

        queryDTO.setIdField(typeInformation.getMongoField());


        // 别人主动分享给当前用户的
        List<Share> shareList = shareRepository.findShareByShareTo(queryDTO.getEmail());
        for (Share share : shareList) {
            List<Object> objects = (List<Object>) ReflectUtil.getFieldValue(share, typeInformation.getShareListField());
            if (CollUtil.isNotEmpty(objects)) {
                for (Object obj : objects) {
                    String no = (String) ReflectUtil.getFieldValue(obj, typeInformation.getField());
                    if (StrUtil.isNotBlank(no)) {
                        queryDTO.getShareNos().add(no);
                    }
                }
            }
        }

        if (AuthorizeType.data.name().equals(queryDTO.getType())) {
            // 用户请求得到授权的
            List<String> authToNos = authorizeRepository.searchAuthorizeDataList(SecurityUtils.getMemberId());
            queryDTO.getShareNos().addAll(authToNos);
        }

        Page page = analysisRepository.findAccessableSelectPage(queryDTO, typeInformation.getClazz());
        Page result = page.map(x -> {
            SelectOption option = new SelectOption();
            // 通过反射获取name字段的值
            String name = (String) ReflectUtil.getFieldValue(x, "name");
            String no = (String) ReflectUtil.getFieldValue(x, typeInformation.getField());
            option.setValue(no);
            option.setLabel(StrUtil.format("{} ({})", no, name));
            return option;
        });

        return result;
    }


    public Page<SelectOption> getPipelineOptions(SelectQueryDTO queryDTO) {
        queryDTO.setCreator(queryDTO.getCreator());
        Page<Data> page = dataRepository.getAccessableDataPage(queryDTO);
        Page<SelectOption> result = page.map(x -> {
            SelectOption option = new SelectOption();
            String no = x.getDatNo();
            String name = x.getName();
            option.setValue(no);
            option.setLabel(StrUtil.format("{} ({})", no, name));
            return option;
        });
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public AnalysisVO save(AnalysisDTO dto) {
        String subNo = dto.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);
        String analNo = submission.getAnalSingleNo();

        Analysis existAnal = analysisRepository.validateAnalysisName(SecurityUtils.getMemberId(), analNo, dto.getName());
        if (existAnal != null && existAnal.getTempData().getSubNo() != null) {
            throw new ServiceException("The analysis name already exists already exists in submission: " + existAnal.getTempData().getSubNo());
        }

        // 新增analysis
        Analysis analysis;
        if (StrUtil.isBlank(analNo)) {
            analysis = new Analysis();
            AnalysisDTOMapper.INSTANCE.copyToDb(dto, analysis);
            // 临时ID(submission审核的时候才会用到，可能没用)
            analysis.setId(IdUtil.objectId());
            analysis.setAnalysisNo(IdUtil.fastSimpleUUID());
            Date now = new Date();
            analysis.setCreateDate(now);
            analysis.setUpdateDate(now);
            analysis.setCreator(SecurityUtils.getMemberId());
            analysis.setOwnership(OwnershipEnum.self_support.getDesc());
            analysis.setSubmitter(submission.getSubmitter());
            analysis.setHitNum(0L);
            analysis.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
            analysis.setAudited(AuditEnum.init.name());

            // 获取target
            if (CollUtil.isNotEmpty(dto.getTarget())) {
                List<AnalysisTarget> list = obtainAnalysisTargets(dto.getTarget());
                if (CollUtil.isEmpty(list) && CollUtil.isEmpty(dto.getCustomTarget())) {
                    throw new ServiceException("The ID filled in by Target is incorrect, please check");
                }
                analysis.setTarget(list);
            }
            submission.setAnalSingleNo(analysis.getAnalysisNo());
            // 将数据临时 tempData 存一份
            Analysis tempData = new Analysis();
            AnalysisDTOMapper.INSTANCE.copy(analysis, tempData);
            analysis.setTempData(tempData);

            savePublish(dto.getPublish(), AuthorizeType.analysis, analysis.getAnalysisNo());

            saveEditSubmission(submission);
        } else {
            analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("Not found data"));
            Analysis tempData = analysis.getTempData();
            AnalysisDTOMapper.INSTANCE.copyToDb(dto, tempData);

            if (CollUtil.isNotEmpty(dto.getTarget())) {
                List<AnalysisTarget> list = obtainAnalysisTargets(dto.getTarget());
                if (CollUtil.isEmpty(list) && CollUtil.isEmpty(dto.getCustomTarget())) {
                    throw new ServiceException("The ID filled in by Target is incorrect, please check");
                }
                dto.setTarget(list);
                tempData.setTarget(list);
            }
            if (CollUtil.isNotEmpty(dto.getPipeline())) {
                List<Pipeline> list = obtainPipeline(dto.getPipeline());
                dto.setPipeline(list);
                tempData.setPipeline(list);
            }
            if (AuditEnum.audited.name().equals(tempData.getAudited())) {
                tempData.setAudited(AuditEnum.unaudited.name());
            }
            // 如果外层是init才更新外层数据
            if (analysis.getAudited().equals(AuditEnum.init.name())) {
                AnalysisDTOMapper.INSTANCE.copyToDb(dto, analysis);
            }
            // 临时数据，外层也要更新
            tempData.setUpdateDate(new Date());
            analysis.setTempData(tempData);

            savePublish(dto.getPublish(), AuthorizeType.analysis, analysis.getAnalysisNo());
        }
        analysisRepository.save(analysis);
        return getAnalysisByNo(analysis.getAnalysisNo());
    }

    private List<AnalysisTarget> obtainAnalysisTargets(List<AnalysisTarget> list) {
        List<AnalysisTarget> results = new ArrayList<>();
        Map<String, Function<String, ?>> typeToFunctionMap = new HashMap<>();
        typeToFunctionMap.put(AuthorizeType.project.name(), projectRepository::findByNo);
        typeToFunctionMap.put(AuthorizeType.experiment.name(), experimentRepository::findByNo);
        typeToFunctionMap.put(AuthorizeType.sample.name(), sampleRepository::findByNo);
        typeToFunctionMap.put(AuthorizeType.run.name(), runRepository::findByNo);
        typeToFunctionMap.put(AuthorizeType.data.name(), dataRepository::getNoOpenDataByDatNo);
        typeToFunctionMap.put(AuthorizeType.analysis.name(), analysisRepository::findByAnalNo);

        for (AnalysisTarget target : list) {
            Function<String, ?> function = typeToFunctionMap.get(target.getType());
            if (function != null) {
                List<String> nos = target.getNos().stream().distinct().collect(Collectors.toList());
                HashSet<String> resultNos = new HashSet<>();
                for (String no : nos) {
                    if (function.apply(no) != null) {
                        resultNos.add(no);
                    }
                }
                if (CollUtil.isNotEmpty(resultNos)) {
                    target.setNos(new ArrayList<>(resultNos));
                    results.add(target);
                }
            }
        }

        return results;
    }

    private List<Pipeline> obtainPipeline(List<Pipeline> pipeline) {
        List<Pipeline> results = new ArrayList<>();
        for (int i = 0; i < pipeline.size(); i++) {
            Pipeline item = new Pipeline();
            BeanUtil.copyProperties(pipeline.get(i), item);
            item.setIndex(i + 1);
            results.add(item);
        }
        return results;
    }

    public AnalysisVO getAnalysisByNo(String analNo) {
        if (StrUtil.isBlank(analNo)) {
            throw new ServiceException("Analysis ID cannot be empty");
        }
        Optional<Analysis> optional = analysisRepository.findFirstByAnalysisNo(analNo);
        if (!optional.isPresent()) {
            return null;
        }
        Analysis analysis = optional.get();
        AnalysisVO result = new AnalysisVO();
        // 从临时数据里面拷贝
        if (analysis.getAudited().equals(AuditEnum.audited.name()) && analysis.getTempData() == null) {
            AnalysisDTOMapper.INSTANCE.copyToVo(analysis, result);
            List<PublishVO> publishVO = getPublishVO(AuthorizeType.analysis, analNo);
            result.setPublish(publishVO);
        } else {
            AnalysisDTOMapper.INSTANCE.copyToVo(analysis.getTempData(), result);
            List<PublishVO> publishVo = getTempPublishVO(AuthorizeType.analysis, analNo);
            result.setPublish(publishVo);
        }
        return result;
    }

    public String validateAnalysisName(String analysisNo, String name) {
        if (StringUtil.isBlank(name)) {
            return "The analysis name cannot be blank";
        }
        Analysis existAnal = analysisRepository.validateAnalysisName(SecurityUtils.getMemberId(), analysisNo, name);
        if (existAnal != null && existAnal.getTempData().getSubNo() != null) {
            return "The analysis name already exists already exists in submission: " + existAnal.getTempData().getSubNo();
        }
        return null;
    }

    public List<SelectOption> getAnalysisOptions(SelectQueryDTO queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        List<Analysis> list = analysisRepository.findAllByCreator(queryDTO.getCreator());
        return list.stream().map(x -> {
            SelectOption option = new SelectOption();
            String analysisNo = x.getAnalysisNo();
            option.setValue(analysisNo);
            if (analysisNo.startsWith("OEZ")) {
                option.setLabel(StrUtil.format("{} ({})", analysisNo, x.getName()));
            } else {
                option.setLabel(x.getName());
            }
            return option;

        }).collect(Collectors.toList());
    }

    public Page<SelectOption> getAnalysisOptionsByPage(ArchivedSelectQueryDTO queryDTO) {
        queryDTO.setCreator(SecurityUtils.getMemberId());
        Page<Analysis> page = analysisRepository.findAllByPage(queryDTO);
        return page.map(x -> {
            SelectOption option = new SelectOption();
            String analysisNo = x.getAnalysisNo();
            option.setValue(analysisNo);
            if (analysisNo.startsWith(SequenceType.ANALYSIS.getPrefix())) {
                option.setLabel(StrUtil.format("{} ({})", analysisNo, x.getName()));
            } else {
                option.setLabel(x.getName());
            }
            return option;
        });
    }

    /**
     * 获取表格数据
     *
     * @param subNo    Submission No
     * @param editPage 编辑页 or 详情页
     */
    public List<AnalysisImportDTO> getMultiAnalysisBySubNo(String subNo, boolean editPage) {
        Submission submission = getSubmissionByNo(subNo);
        if (editPage) {
            submission = getEditSubmissionByNo(subNo);
        }

        List<String> analMultipleNos = submission.getAnalMultipleNos();

        if (!editPage) {
            if (CollUtil.isEmpty(analMultipleNos)) {
                analMultipleNos = new ArrayList<>();
            }
            String analSingleNo = submission.getAnalSingleNo();
            if (analSingleNo != null) {
                analMultipleNos.add(analSingleNo);
            }
        }

        if (CollUtil.isEmpty(analMultipleNos)) {
            return null;
        }

        List<Analysis> analysisList = analysisRepository.findAllByAnalysisNoIn(analMultipleNos);

        // 从临时数据字段中获取数据
        List<Analysis> allList = analysisList.stream().map(Analysis::getTempData).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollUtil.isEmpty(allList)) {
            return null;
        }
        List<AnalysisImportDTO> result = new ArrayList<>();

        if (CollUtil.isNotEmpty(allList)) {

            for (Analysis analysis : allList) {

                AnalysisImportDTO dto = new AnalysisImportDTO();

                if (!editPage || analysis.getAnalysisNo().startsWith(SequenceType.ANALYSIS.getPrefix())) {
                    dto.setAnalysisId(analysis.getAnalysisNo());
                }

                dto.setAnalysisName(analysis.getName());
                dto.setDescription(analysis.getDescription());
                dto.setAnalysisType(analysis.getAnalysisType());
                dto.setCustomAnalysisType(analysis.getCustomAnalysisType());

                List<AnalysisTarget> target = analysis.getTarget();
                if (CollUtil.isNotEmpty(target)) {
                    for (AnalysisTarget analysisTarget : target) {
                        String type = analysisTarget.getType();
                        List<String> nos = analysisTarget.getNos();
                        if (CollUtil.isNotEmpty(nos)) {
                            String join = StrUtil.join(";", nos);
                            if (type.equals(AuthorizeType.project.name())) {
                                dto.setTargetProject(join);
                            } else if (type.equals(AuthorizeType.experiment.name())) {
                                dto.setTargetExperiment(join);
                            } else if (type.equals(AuthorizeType.sample.name())) {
                                dto.setTargetSample(join);
                            } else if (type.equals(AuthorizeType.analysis.name())) {
                                dto.setTargetAnalysis(join);
                            } else if (type.equals(AuthorizeType.run.name())) {
                                dto.setTargetRun(join);
                            } else if (type.equals(AuthorizeType.data.name())) {
                                dto.setTargetData(join);
                            }
                        }
                    }
                }

                List<CustomTarget> customTargetList = analysis.getCustomTarget();
                if (CollUtil.isNotEmpty(customTargetList)) {
                    List<String> names = new ArrayList<>();
                    List<String> links = new ArrayList<>();
                    for (CustomTarget customTarget : customTargetList) {
                        names.add(customTarget.getName());
                        links.add(customTarget.getLink());
                    }
                    dto.setTargetOtherName(StrUtil.join(";", names));
                    dto.setTargetOtherLink(StrUtil.join(";", links));
                }

                List<Pipeline> pipelineList = analysis.getPipeline();
                if (CollUtil.isNotEmpty(pipelineList)) {
                    for (int i = 1; i <= pipelineList.size(); i++) {
                        Pipeline pipeline = pipelineList.get(i - 1);
                        AnalysisImportDTO item = new AnalysisImportDTO();
                        BeanUtil.copyProperties(dto, item);
                        item.setIndex(i);
                        item.setProgram(pipeline.getProgram());
                        item.setLink(pipeline.getLink());
                        item.setVersion(pipeline.getVersion());
                        item.setNote(pipeline.getNote());
                        item.setOutputFile(StrUtil.join(";", pipeline.getOutput()));
                        result.add(item);
                    }
                } else {
                    result.add(dto);
                }
            }
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<ErrorMsgVO> batchSave(AnalysisBatchDTO dto) {
        String memberId = SecurityUtils.getMemberId();

        if (CollUtil.isEmpty(dto.getDatas())) {
            throw new ServiceException("Data cannot be empty");
        }

        // 找到submission
        Submission submission = getEditSubmissionByNo(dto.getSubNo());

        // 获取analMultipleNos
        List<String> analMultipleNos = submission.getAnalMultipleNos();
        if (analMultipleNos == null) {
            analMultipleNos = new ArrayList<>();
        }

        // 记录错误信息
        List<ErrorMsgVO> errors = new ArrayList<>();
        // 数据转为列 map
        Map<String, List<Object>> excelMap = getExcelMap(dto.getTitles(), dto.getDatas());

        // 校验必填项
        mustFilledCheck(excelMap, ANALYSIS_NAME, errors);
        mustFilledCheck(excelMap, ANALYSIS_TYPE, errors);

        // 将数据转为AnalysisImportDTO
        List<AnalysisImportDTO> analysisImportDTOs = new ArrayList<>();
        for (int i = 0; i < dto.getDatas().size(); i++) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("rowIndex", i);
            for (int j = 0; j < dto.getTitles().size(); j++) {
                Object o = dto.getDatas().get(i)[j];
                if (o == null) {
                    jsonObject.put(dto.getTitles().get(j), null);
                } else {
                    jsonObject.put(dto.getTitles().get(j), StrUtil.trimToNull(o.toString()));
                }
            }
            analysisImportDTOs.add(jsonObject.toJavaObject(AnalysisImportDTO.class));
        }

        // 因为批量导入analysis需要根据analysis_id分组，所以校验前需要将能list中能查询到的analysis_id换成新版的analysis_no，如果用的used_id,得换成对应的analysis_no
        // parseNoToMainNo(analysisImportDTOs);

        // 记录校验过的no
        Map<String, Boolean> projectNoRecordMap = new HashMap<>();
        Map<String, Boolean> expNoRecordMap = new HashMap<>();
        Map<String, Boolean> sapNoRecordMap = new HashMap<>();
        Map<String, Boolean> analNoRecordMap = new HashMap<>();
        Map<String, Boolean> runNoRecordMap = new HashMap<>();
        Map<String, Boolean> datNoRecordMap = new HashMap<>();
        Map<String, Boolean> outputRecordMap = new HashMap<>();

        // 过滤出analysis_id为空的
        List<AnalysisImportDTO> analIdNullList = analysisImportDTOs.stream()
                .filter(x -> StrUtil.isBlank(x.getAnalysisId())).collect(Collectors.toList());
        // 过滤出analysis_id不为空的
        List<AnalysisImportDTO> analIdNonList = analysisImportDTOs.stream()
                .filter(x -> StrUtil.isNotBlank(x.getAnalysisId())).collect(Collectors.toList());
        // 过滤出所有analysis_id
        List<String> analIdList = analysisImportDTOs.stream().map(AnalysisImportDTO::getAnalysisId)
                .filter(StrUtil::isNotBlank).distinct().collect(Collectors.toList());

        // 根据AnalysisName字段分组
        Map<String, List<AnalysisImportDTO>> analIdNullMap = analIdNullList.stream()
                .collect(Collectors.groupingBy(AnalysisImportDTO::getAnalysisName, LinkedHashMap::new, Collectors.toList()));

        // 找出没有analysis_id的所有的name
        Set<String> names = analIdNullMap.keySet();
        // List<String> officialIds = analMultipleNos.stream().filter(x -> !x.startsWith(SequenceType.ANALYSIS.getPrefix())).collect(Collectors.toList());
        // Map<String, String> existNameSubNoMap = analysisRepository.findNameByNamesInAndAnalNoNotIn(names, memberId, officialIds);
        Map<String, String> existNameSubNoMap = analysisRepository.findTempNameByNamesInAndNoNotIn(names, memberId, analMultipleNos);

        Map<String, Boolean> userAnalNoRecordMap = analysisRepository.existsByCreatorAndNos(analIdList, memberId);
        List<Analysis> analysisList = analysisRepository.findAllByAnalysisNoIn(analIdList);
        Map<String, Analysis> analNoToAnalMap = analysisList.stream().collect(Collectors.toMap(Analysis::getAnalysisNo, Function.identity(), (existingValue, newValue) -> existingValue));
        for (String analName : names) {
            List<AnalysisImportDTO> list = analIdNullMap.get(analName);

            // 校验名称是否重复
            for (AnalysisImportDTO line : list) {
                if (existNameSubNoMap.containsKey(line.getAnalysisName())) {
                    errors.add(errMsg(line.getRowIndex(), ANALYSIS_NAME, line.getAnalysisName(), "The analysis_name already exists already exists in submission: " + existNameSubNoMap.get(line.getAnalysisName())));
                }
            }

            // 校验pipeline 内容是否正确
            validatePipeline(list, errors, outputRecordMap);
            // 校验相同列是否相同
            validateCommonColumns(list, errors);
            // target验证,必须填一个，且内容正确
            validateColumnTarget(list, errors, projectNoRecordMap, expNoRecordMap, sapNoRecordMap, analNoRecordMap, runNoRecordMap, datNoRecordMap);
        }

        // 根据AnalysisId字段分组
        Map<String, List<AnalysisImportDTO>> analIdNonMap = analIdNonList.stream()
                .collect(Collectors.groupingBy(AnalysisImportDTO::getAnalysisId, LinkedHashMap::new, Collectors.toList()));


        for (String analId : analIdNonMap.keySet()) {
            List<AnalysisImportDTO> list = analIdNonMap.get(analId);

            for (AnalysisImportDTO line : list) {
                // 校验id是否存在
                String item = line.getAnalysisId();
                Boolean b = userAnalNoRecordMap.get(item);
                // 先判断id是否存在，再校验analysis_id有没有被其他地方用
                if (!b) {
                    errors.add(errMsg(line.getRowIndex(), "analysis_id", item, "not exist"));
                } else {
                    // 校验analysisNo是否在其他submission中
                    Analysis analysis = analNoToAnalMap.get(item);
                    if (analysis.getTempData() != null && !analysis.getTempData().getSubNo().equals(submission.getSubNo())) {
                        errors.add(errMsg(line.getRowIndex(), "analysis_id", item, "The Analysis ID has already been used in submission: " + analysis.getTempData().getSubNo()));
                    }
                }
            }

            // 校验analysis_name是否相同
            Set<String> analNameSet = new HashSet<>();
            for (AnalysisImportDTO line : list) {
                if (!analNameSet.add(line.getAnalysisName())) {
                    errors.add(errMsg(line.getRowIndex(), ANALYSIS_NAME, line.getAnalysisName(), "The same analysis_id have different analysis_name"));
                }
            }

            // 判断list中index是否有续且重复
            validatePipeline(list, errors, outputRecordMap);

            // 校验公共列是否相同
            validateCommonColumns(list, errors);

            // target验证,必须填一个
            validateColumnTarget(list, errors, projectNoRecordMap, expNoRecordMap, sapNoRecordMap, analNoRecordMap, runNoRecordMap, datNoRecordMap);
        }

        if (CollUtil.isNotEmpty(errors)) {
            return errors;
        }

        // 查询当前用户没有正式id的analysis
        Map<String, Analysis> nameItemOfTempData = analysisRepository.findTempNameByNamesInAndNoIn(names, memberId, analMultipleNos);
        // 不被删除非正式的id
        List<String> notDeleteNotOfficialNos = new ArrayList<>();
        // 保存新增的数据（没有id）
        List<Analysis> addList = new ArrayList<>();
        for (String analName : names) {
            List<AnalysisImportDTO> list = analIdNullMap.get(analName);
            // 获取analysis的数据
            Analysis analysis = obtainAnalysis(list, submission);
            // 如果已经存在这个名字的非正式id，直接沿用非正式id
            if (nameItemOfTempData.containsKey(analName)) {
                Analysis tempAnalysis = nameItemOfTempData.get(analName);
                if (!tempAnalysis.getAnalysisNo().startsWith(SequenceType.ANALYSIS.getPrefix())) {
                    notDeleteNotOfficialNos.add(tempAnalysis.getAnalysisNo());
                    analysis.setId(tempAnalysis.getId());
                    analysis.setAnalysisNo(tempAnalysis.getAnalysisNo());
                }
            }
            // 将数据放一份到临时字段里面
            Analysis tempData = new Analysis();
            AnalysisDTOMapper.INSTANCE.copy(analysis, tempData);
            tempData.setAudited(AuditEnum.init.name());
            analysis.setTempData(tempData);

            addList.add(analysis);
        }

        List<Analysis> updateList = new ArrayList<>();
        // 保存更新的数据（有id）
        for (String analId : analIdNonMap.keySet()) {
            List<AnalysisImportDTO> list = analIdNonMap.get(analId);
            Analysis analysis = analNoToAnalMap.get(analId);
            Analysis tempData = obtainAnalysis(list, submission);
            tempData.setAudited(AuditEnum.unaudited.name());
            tempData.setAnalysisNo(analysis.getAnalysisNo());
            tempData.setCreateDate(analysis.getCreateDate());
            tempData.setUpdateDate(analysis.getUpdateDate());
            analysis.setTempData(tempData);

            updateList.add(analysis);
        }

        // 保存新增的analysis
        analysisRepository.saveAll(addList);
        // 保存更新的analysis
        analysisRepository.saveAll(updateList);
        // 删除原有的analMultipleNos中不是正式id的且没有二次编辑的
        analMultipleNos.removeIf(x -> {
            if (!x.startsWith(SequenceType.ANALYSIS.getPrefix()) && !notDeleteNotOfficialNos.contains(x)) {
                analysisRepository.deleteByAnalysisNo(x);
            }
            return !x.startsWith(SequenceType.ANALYSIS.getPrefix()) && !notDeleteNotOfficialNos.contains(x);
        });
        // 过滤出正式id
        List<String> officialNos = analMultipleNos.stream().filter(x -> x.startsWith(SequenceType.ANALYSIS.getPrefix())).collect(Collectors.toList());
        // 求差集，差集就是需要回滚的数据
        Collection<String> needRollBackNos = CollUtil.subtract(officialNos, analIdNonMap.keySet());
        List<Analysis> needRollbackAnals = analysisRepository.findAllByAnalysisNoIn(needRollBackNos);
        needRollbackAnals.forEach(x -> {
            // 将正式数据放到临时字段里面
            x.setTempData(null);
        });
        // 数组中，删除回滚的编号
        analMultipleNos.removeAll(needRollBackNos);
        // 保存数据
        analysisRepository.saveAll(needRollbackAnals);

        // 添加新增的编号
        analMultipleNos.addAll(addList.stream().map(Analysis::getAnalysisNo).collect(Collectors.toList()));
        // 添加修改的编号
        analMultipleNos.addAll(updateList.stream().map(Analysis::getAnalysisNo).collect(Collectors.toList()));
        // 保存到字段中，保存之前需要去个重
        submission.setAnalMultipleNos(analMultipleNos.stream().distinct().collect(Collectors.toList()));
        saveEditSubmission(submission);
        // 不返回数据
        return null;
    }

    private void parseNoToMainNo(List<AnalysisImportDTO> analysisImportDTOs) {
        Map<String, String> recordMap = new LinkedHashMap<>();
        for (AnalysisImportDTO dto : analysisImportDTOs) {
            if (StrUtil.isNotBlank(dto.getAnalysisId())) {
                if (recordMap.containsKey(dto.getAnalysisId())) {
                    dto.setAnalysisId(recordMap.get(dto.getAnalysisId()));
                } else {
                    Optional<Analysis> optional = analysisRepository.findFirstByAnalysisNo(dto.getAnalysisId());
                    if (optional.isPresent()) {
                        dto.setAnalysisId(optional.get().getAnalysisNo());
                        recordMap.put(dto.getAnalysisId(), optional.get().getAnalysisNo());
                    }
                }
            }

            if (StrUtil.isNotBlank(dto.getTargetProject())) {
                List<String> items = splitMetadataNo(dto.getTargetProject());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Project> optional = projectRepository.findTopByProjectNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getProjectNo());
                            recordMap.put(item, optional.get().getProjectNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setTargetProject(CollUtil.join(list, ";"));
            }

            if (StrUtil.isNotBlank(dto.getTargetExperiment())) {
                List<String> items = splitMetadataNo(dto.getTargetExperiment());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Experiment> optional = experimentRepository.findTopByExpNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getExpNo());
                            recordMap.put(item, optional.get().getExpNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setTargetExperiment(CollUtil.join(list, ";"));
            }

            if (StrUtil.isNotBlank(dto.getTargetSample())) {
                List<String> items = splitMetadataNo(dto.getTargetSample());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Sample> optional = sampleRepository.findTopBySapNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getSapNo());
                            recordMap.put(item, optional.get().getSapNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setTargetSample(CollUtil.join(list, ";"));
            }

            if (StrUtil.isNotBlank(dto.getTargetRun())) {
                List<String> items = splitMetadataNo(dto.getTargetRun());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Run> optional = runRepository.findFirstByRunNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getRunNo());
                            recordMap.put(item, optional.get().getRunNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setTargetRun(CollUtil.join(list, ";"));
            }

            if (StrUtil.isNotBlank(dto.getTargetData())) {
                List<String> items = splitMetadataNo(dto.getTargetData());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Data> optional = dataRepository.findByDatNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getDatNo());
                            recordMap.put(item, optional.get().getDatNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setTargetData(CollUtil.join(list, ";"));
            }

            if (StrUtil.isNotBlank(dto.getTargetAnalysis())) {
                List<String> items = splitMetadataNo(dto.getTargetAnalysis());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Analysis> optional = analysisRepository.findFirstByAnalysisNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getAnalysisNo());
                            recordMap.put(item, optional.get().getAnalysisNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setTargetAnalysis(CollUtil.join(list, ";"));
            }

            if (StrUtil.isNotBlank(dto.getOutputFile())) {
                List<String> items = splitMetadataNo(dto.getOutputFile());
                ArrayList<String> list = new ArrayList<>();
                for (String item : items) {
                    if (recordMap.containsKey(item)) {
                        list.add(recordMap.get(item));
                    } else {
                        Optional<Data> optional = dataRepository.findByDatNo(item);
                        if (optional.isPresent()) {
                            list.add(optional.get().getDatNo());
                            recordMap.put(item, optional.get().getDatNo());
                        } else {
                            list.add(item);
                            recordMap.put(item, item);
                        }
                    }
                }
                dto.setOutputFile(CollUtil.join(list, ";"));
            }
        }
    }

    private List<String> splitMetadataNo(String line) {
        String[] split = line.replaceAll("\\s+", "").replace("；", ";").split(";");
        return Arrays.asList(split);
    }


    private void validateColumnTarget(List<AnalysisImportDTO> list,
                                      Collection<ErrorMsgVO> errors,
                                      Map<String, Boolean> projNoRecordMap,
                                      Map<String, Boolean> expNoRecordMap,
                                      Map<String, Boolean> sapNoRecordMap,
                                      Map<String, Boolean> analNoRecordMap,
                                      Map<String, Boolean> runNoRecordMap,
                                      Map<String, Boolean> datNoRecordMap) {
        for (AnalysisImportDTO line : list) {
            // 判断targetProject到targetData是否同时为空
            if ((StrUtil.isBlank(line.getTargetProject())
                    && StrUtil.isBlank(line.getTargetExperiment())
                    && StrUtil.isBlank(line.getTargetSample())
                    && StrUtil.isBlank(line.getTargetAnalysis())
                    && StrUtil.isBlank(line.getTargetRun())
                    && StrUtil.isBlank(line.getTargetData())) &&
                    (StrUtil.isBlank(line.getTargetOtherName())
                            && StrUtil.isBlank(line.getTargetOtherLink()))) {
                errors.add(errMsg(line.getRowIndex(), "column:(target_project to target_data) or (target_other_name to target_other_link)", null, "From (target_project to target_data) or (target_other_name to target_other_link) at least filled one"));
            }
            // 判断targetOtherName和targetOtherLink同时填写
            if (!(StrUtil.isBlank(line.getTargetOtherName()) == StrUtil.isBlank(line.getTargetOtherLink()))) {
                errors.add(errMsg(line.getRowIndex(), "column:(target_other_name and target_other_link)", null, "Must be filled in together"));
            }
            // 判断targetOtherName和targetOtherLink的个数是否相同
            if (StrUtil.isNotBlank(line.getTargetOtherName()) && StrUtil.isNotBlank(line.getTargetOtherLink())) {
                String[] nameArr = line.getTargetOtherName().split(";");
                String[] linkArr = line.getTargetOtherLink().split(";");
                if (nameArr.length != linkArr.length) {
                    errors.add(errMsg(line.getRowIndex(), "column:(target_other_name and target_other_link)", null, "The number of items filled in must be the same length"));
                }
            }
            targetCheck(line.getTargetProject(), projNoRecordMap, line.getRowIndex(), "target_project", errors, AuthorizeType.project.name());
            targetCheck(line.getTargetExperiment(), expNoRecordMap, line.getRowIndex(), "target_experiment", errors, AuthorizeType.experiment.name());
            targetCheck(line.getTargetSample(), sapNoRecordMap, line.getRowIndex(), "target_sample", errors, AuthorizeType.sample.name());
            targetCheck(line.getTargetAnalysis(), analNoRecordMap, line.getRowIndex(), "target_analysis", errors, AuthorizeType.analysis.name());
            targetCheck(line.getTargetRun(), runNoRecordMap, line.getRowIndex(), "target_run", errors, AuthorizeType.run.name());
            targetCheck(line.getTargetData(), datNoRecordMap, line.getRowIndex(), "target_data", errors, AuthorizeType.data.name());
        }
    }

    private void validateCommonColumns(List<AnalysisImportDTO> list, List<ErrorMsgVO> errors) {
        // 校验公共列是否相同
        Set<String> commonFieldSet = new HashSet<>();
        for (AnalysisImportDTO line : list) {
            String join = StrUtil.join("#*#",
                    line.getDescription(),
                    line.getTargetProject(),
                    line.getTargetExperiment(),
                    line.getTargetSample(),
                    line.getTargetAnalysis(),
                    line.getTargetRun(),
                    line.getTargetData(),
                    line.getTargetOtherName(),
                    line.getTargetOtherLink());
            commonFieldSet.add(join);
            if (commonFieldSet.size() > 1) {
                errors.add(errMsg(line.getRowIndex(), "description to target_data", null, "When analysis_name is same,column from description to target_data should be same"));
            }
            if ("Other".equalsIgnoreCase(line.getAnalysisType()) && StrUtil.isBlank(line.getCustomAnalysisType())) {
                errors.add(errMsg(line.getRowIndex(), "other_analysis_type", null, "When analysis_type is Other,custom_analysis_type can't be empty"));
            }
        }
    }

    private void validatePipeline(List<AnalysisImportDTO> list, List<ErrorMsgVO> errors, Map<String, Boolean> recordMap) {
        // 判断list中index是否有续且重复
        Set<Integer> indexSet = new TreeSet<>();
        int index = 1;
        for (AnalysisImportDTO line : list) {
            // 如果index不为空，就判断是否有重复
            if (line.getIndex() != null && indexSet.contains(line.getIndex())) {
                errors.add(errMsg(line.getRowIndex(), INDEX, String.valueOf(line.getIndex()), "The same analysis_name have repeated index"));
            } else if (line.getIndex() == null &&
                    (StrUtil.isNotBlank(line.getProgram())
                            || StrUtil.isNotBlank(line.getLink())
                            || StrUtil.isNotBlank(line.getVersion()))) {
                // 如果index为空，且list的长度大于1，就报错
                errors.add(errMsg(line.getRowIndex(), INDEX, null, "If you filled the pipeline information, index can't be empty"));
            } else if (line.getIndex() != null) {
                indexSet.add(line.getIndex());
                // 如果index不为空，就判断是否有续
                if (index != line.getIndex()) {
                    errors.add(errMsg(line.getRowIndex(), INDEX, String.valueOf(line.getIndex()), "The same analysis_name's index should be continuous,start from 1"));
                }
                // 判断program是否为空
                if (StrUtil.isBlank(line.getProgram())) {
                    errors.add(errMsg(line.getRowIndex(), "program", line.getProgram(), "The program can't be filled in when index is not empty"));
                }
                // 判断link是否为空
                if (StrUtil.isNotBlank(line.getOutputFile())) {
                    String[] split = line.getOutputFile().split(";");
                    for (String item : split) {
                        if (StrUtil.isBlank(item)) {
                            recordMap.put(item, false);
                        }
                        Boolean b = recordMap.getOrDefault(item, null);
                        // 如果不在map中，就去数据库中查询
                        if (b == null) {
                            boolean exist = dataRepository.existsUserData(item, SecurityUtils.getMemberId());
                            b = exist;
                            recordMap.put(item, exist);
                        }
                        if (!b) {
                            errors.add(errMsg(line.getRowIndex(), "output_file", item, "not exist"));
                        }
                    }
                }
            }
            index++;
        }
    }

    private Analysis obtainAnalysis(List<AnalysisImportDTO> list, Submission submission) {
        AnalysisImportDTO analysisImportDTO = list.get(0);
        Analysis analysis = new Analysis();
        // 临时ID(submission审核的时候才会用到，可能没用)
        analysis.setId(IdUtil.objectId());
        analysis.setAnalysisNo(IdUtil.fastSimpleUUID());
        analysis.setName(analysisImportDTO.getAnalysisName());
        analysis.setDescription(analysisImportDTO.getDescription());
        analysis.setAnalysisType(analysisImportDTO.getAnalysisType());
        analysis.setCustomAnalysisType(analysisImportDTO.getCustomAnalysisType());

        List<AnalysisTarget> analysisTargets = new ArrayList<>();
        AnalysisTarget analysisTarget = tran2AnalysisTarget(analysisImportDTO.getTargetProject(), AuthorizeType.project.name());
        if (analysisTarget != null) {
            analysisTargets.add(analysisTarget);
        }
        analysisTarget = tran2AnalysisTarget(analysisImportDTO.getTargetExperiment(), AuthorizeType.experiment.name());
        if (analysisTarget != null) {
            analysisTargets.add(analysisTarget);
        }
        analysisTarget = tran2AnalysisTarget(analysisImportDTO.getTargetSample(), AuthorizeType.sample.name());
        if (analysisTarget != null) {
            analysisTargets.add(analysisTarget);
        }
        analysisTarget = tran2AnalysisTarget(analysisImportDTO.getTargetAnalysis(), AuthorizeType.analysis.name());
        if (analysisTarget != null) {
            analysisTargets.add(analysisTarget);
        }
        analysisTarget = tran2AnalysisTarget(analysisImportDTO.getTargetRun(), AuthorizeType.run.name());
        if (analysisTarget != null) {
            analysisTargets.add(analysisTarget);
        }
        analysisTarget = tran2AnalysisTarget(analysisImportDTO.getTargetData(), AuthorizeType.data.name());
        if (analysisTarget != null) {
            analysisTargets.add(analysisTarget);
        }
        analysis.setTarget(analysisTargets);

        List<CustomTarget> customTargets = tran2CustomAnalysisTarget(analysisImportDTO.getTargetOtherName(), analysisImportDTO.getTargetOtherLink());

        analysis.setCustomTarget(customTargets);

        List<Pipeline> pipelines = new ArrayList<>();
        for (AnalysisImportDTO item : list) {
            Pipeline pipeline = new Pipeline();
            if (item.getIndex() != null) {
                pipeline.setIndex(item.getIndex());
                pipeline.setProgram(item.getProgram());
                pipeline.setLink(item.getLink());
                pipeline.setVersion(item.getVersion());
                pipeline.setNote(item.getNote());
                pipeline.setOutput(item.getOutputFile() != null ?
                        Arrays.asList(item.getOutputFile().split(";")).stream().distinct().collect(Collectors.toList()) : new ArrayList<>());
                pipelines.add(pipeline);
            }
        }
        analysis.setPipeline(pipelines);
        analysis.setSubmitter(submission.getSubmitter());
        analysis.setCreator(SecurityUtils.getMemberId());
        Date currDate = new Date();
        analysis.setCreateDate(currDate);
        analysis.setUpdateDate(currDate);

        analysis.setOwnership(OwnershipEnum.self_support.getDesc());
        analysis.setHitNum(0L);
        analysis.setVisibleStatus(VisibleStatusEnum.Unaccessible.name());
        analysis.setAudited(AuditEnum.init.name());
        // 添加subNo数据
        analysis.setSubNo(submission.getSubNo());

        return analysis;
    }

    public AnalysisTarget tran2AnalysisTarget(String targetStr, String type) {
        if (StrUtil.isNotBlank(targetStr)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setType(type);
            analysisTarget.setNos(Arrays.asList(targetStr.split(";")).stream().distinct().collect(Collectors.toList()));
            return analysisTarget;
        }
        return null;
    }

    public List<CustomTarget> tran2CustomAnalysisTarget(String targetOtherName, String targetOtherLink) {
        List<CustomTarget> customTargets = new ArrayList<>();
        if (StrUtil.isNotBlank(targetOtherName) && StrUtil.isNotBlank(targetOtherLink)) {
            String[] nameArr = targetOtherName.split(";");
            String[] linkArr = targetOtherLink.split(";");

            int max = NumberUtil.max(nameArr.length, linkArr.length);

            for (int i = 0; i < max; i++) {
                CustomTarget customTarget = new CustomTarget();
                customTarget.setName(nameArr[i]);
                customTarget.setLink(linkArr[i]);
                customTargets.add(customTarget);
            }
        }

        return customTargets;
    }

    public void targetCheck(String target,
                            Map<String, Boolean> recordMap,
                            int rowIndex, String columnName,
                            Collection<ErrorMsgVO> errors,
                            String type) {
        TypeInformation typeInfo = TypeInformation.typeInfoMap.get(type);
        if (StrUtil.isNotBlank(target)) {
            String[] split = target.split(";");
            List<String> itemsToCheck = new ArrayList<>();

            // 标记itemsToCheck
            for (String item : split) {
                if (StrUtil.isBlank(item)) {
                    recordMap.put(item, false);
                    errors.add(errMsg(rowIndex, columnName, item, "not exist"));
                    continue;
                }
                Boolean cachedValue = recordMap.getOrDefault(item, null);
                if (cachedValue != null) {
                    if (!cachedValue) {
                        errors.add(errMsg(rowIndex, columnName, item, "not exist"));
                    }
                } else {
                    itemsToCheck.add(item);
                }
            }

            // 批量查询数据库
            if (!itemsToCheck.isEmpty()) {
                Set<String> accessibleItems = new HashSet<>();

                // analysisRepository 批量查询
                accessibleItems.addAll(analysisRepository.getAccessableNos(itemsToCheck, typeInfo, SecurityUtils.getMemberId()));

                // shareRepository 批量查询
                accessibleItems.addAll(shareRepository.getAccessableNos(itemsToCheck, typeInfo, SecurityUtils.getMemberEmail()));

                // authorizeRepository 批量查询
                accessibleItems.addAll(authorizeRepository.getAccessableDataNos(itemsToCheck, SecurityUtils.getMemberId()));

                // 更新 recordMap 和 errors
                for (String item : itemsToCheck) {
                    boolean isAccessible = accessibleItems.contains(item);
                    recordMap.put(item, isAccessible);
                    if (!isAccessible) {
                        errors.add(errMsg(rowIndex, columnName, item, "not exist"));
                    }
                }
            }
        }
    }


    public List<DeleteErrorMsgVO> delete(String subNo, Boolean single) {
        if (StrUtil.isBlank(subNo) || single == null) {
            throw new ServiceException("The request parameter is illegal");
        }
        Submission submission = getEditSubmissionByNo(subNo);
        List<String> analNos;
        if (single) {
            analNos = CollUtil.newArrayList(submission.getAnalSingleNo());
            submission.setAnalSingleNo(null);
        } else {
            analNos = submission.getAnalMultipleNos();
            submission.setAnalMultipleNos(null);
        }
        if (CollUtil.isEmpty(analNos)) {
            throw new ServiceException("There is no data to delete");
        }
        List<DeleteErrorMsgVO> vos = new ArrayList<>();

        for (String analNo : analNos) {
            // 查询是否有Data绑定了此AnalNo
            List<Data> datas = dataRepository.findTempDetailByAnalNo(analNo);
            if (CollUtil.isNotEmpty(datas)) {
                for (Data data : datas) {
                    Data tempData = data.getTempData();
                    DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                    Analysis analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("not found analysis: " + analNo));

                    vo.setTarget(analysis.getName());
                    vo.setName(tempData.getName());
                    String tempSubNo = tempData.getSubNo();
                    vo.setSubNo(tempSubNo.equals(subNo) ? tempSubNo + " (Current Submission)" : tempSubNo);
                    vo.setNo(tempData.getDatNo());
                    vo.setType(AuthorizeType.data.name());

                    vos.add(vo);
                }
            }
        }

        if (CollUtil.isNotEmpty(vos)) {
            return vos;
        }

        // 删除数据
        analysisRepository.deleteTempByNosAndCreator(analNos, SecurityUtils.getMemberId());

        // 更新submission
        saveEditSubmission(submission);
        return null;
    }

    public AnalysisVO saveEdit(AnalysisDTO dto) {
        String subNo = dto.getSubNo();
        Submission submission = getEditSubmissionByNo(subNo);
        String analNo = dto.getAnalysisNo();

        Analysis existAnal = analysisRepository.validateAnalysisName(SecurityUtils.getMemberId(), analNo, dto.getName());
        if (existAnal != null && existAnal.getTempData().getSubNo() != null) {
            throw new ServiceException("The analysis name already exists already exists in submission: " + existAnal.getTempData().getSubNo());
        }
        Analysis analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("Not found data"));

        Analysis tempData;
        if (analysis.getTempData() == null) {
            tempData = AnalysisDTOMapper.INSTANCE.copy(analysis);
        } else {
            tempData = analysis.getTempData();
        }

        AnalysisDTOMapper.INSTANCE.copyToDb(dto, tempData);

        // 获取target
        if (CollUtil.isNotEmpty(dto.getTarget())) {
            List<AnalysisTarget> list = obtainAnalysisTargets(dto.getTarget());
            if (CollUtil.isEmpty(list) && CollUtil.isEmpty(dto.getCustomTarget())) {
                throw new ServiceException("The ID filled in by Target is incorrect, please check");
            }
            tempData.setTarget(list);
        } else {
            tempData.setTarget(null);
        }

        tempData.setSubmitter(submission.getSubmitter());
        tempData.setUpdateDate(new Date());
        tempData.setAudited(AuditEnum.unaudited.name());

        saveEditPublish(dto.getPublish(), AuthorizeType.analysis, analysis.getAnalysisNo());

        analysis.setTempData(tempData);
        analysisRepository.save(analysis);

        submission.setAnalSingleNo(analNo);
        saveEditSubmission(submission);

        return getAnalysisByNo(analysis.getAnalysisNo());

    }

    public DeleteCheckResultVO deleteCheck(String analNo, String memberId, boolean validateShare) {
        Analysis analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("not found analysis: " + analNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(analysis.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }

        Set<DeleteErrorMsgVO> errors = new HashSet<>();

        if (analysis.getTempData() != null) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(analNo);
            vo.setNo(analNo);
            vo.setType(AuthorizeType.analysis.name());
            vo.setName(analysis.getTempData().getName());
            vo.setSubNo(analysis.getTempData().getSubNo());
            errors.add(vo);
        }

        // 找到这个analysis下的data
        List<Data> dataList = dataRepository.findAllByAnalysisNo(analNo);
        List<String> dataNos = dataList.stream().map(Data::getDatNo).distinct().collect(Collectors.toList());

        for (Data data : dataList) {
            if (data.getTempData() != null) {
                DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
                vo.setTarget(analNo);
                vo.setNo(data.getTempData().getDatNo());
                vo.setType(AuthorizeType.data.name());
                vo.setName(data.getTempData().getName());
                vo.setSubNo(data.getTempData().getSubNo());
                errors.add(vo);
            }
        }

        List<Data> tempDataList = dataRepository.findTempByAnalysisNo(analNo);
        for (Data data : tempDataList) {
            DeleteErrorMsgVO vo = new DeleteErrorMsgVO();
            vo.setTarget(analNo);
            vo.setNo(data.getTempData().getDatNo());
            vo.setType(AuthorizeType.data.name());
            vo.setName(data.getTempData().getName());
            vo.setSubNo(data.getTempData().getSubNo());
            errors.add(vo);
        }

        DeleteCheckResultVO resultVO = new DeleteCheckResultVO();
        resultVO.setAnalNos(CollUtil.newArrayList(analNo));
        resultVO.setDataNos(dataNos);
        resultVO.setErrors(new ArrayList<>(errors));

        // 是否需要校验被删除的数据在share review request里面使用
        if (validateShare) {
            validateShareAndReviewAndRequest(resultVO, memberId);
        }

        return resultVO;
    }

    public void deleteAnalysisAll(String analNo, String memberId) {
        Analysis analysis = analysisRepository.findFirstByAnalysisNo(analNo).orElseThrow(() -> new ServiceException("not found analysis: " + analNo));
        // 当前用户是否是owner
        if (!StrUtil.equals(analysis.getCreator(), memberId)) {
            throw new ServiceException("No Permission!");
        }
        DeleteCheckResultVO checkResultVO = deleteCheck(analNo, memberId, false);
        // 删除数据
        if (CollUtil.isNotEmpty(checkResultVO.getErrors())) {
            throw new ServiceException("The Analysis cannot be deleted because it is associated with other data");
        }
        // 添加删除的日志
        addUserCenterDeleteLog(analNo, AuthorizeType.analysis.name(), checkResultVO);
        // 将visible_status更新为delete
        analysisRepository.updateToDeleteAllByAnalysisNoIn(checkResultVO.getAnalNos());
        dataRepository.updateToDeleteAllByDatNoIn(checkResultVO.getDataNos());

        // 将相关的publish也删除
        publishRepository.updateToDeleteByTypeAndTypeId(AuthorizeType.analysis.name(), checkResultVO.getAnalNos());


        // 通知删除es索引
        updateEsData(AuthorizeType.analysis.name(), analNo);
    }


}
