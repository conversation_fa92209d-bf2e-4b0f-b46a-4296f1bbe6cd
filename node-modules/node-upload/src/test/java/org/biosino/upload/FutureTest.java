package org.biosino.upload;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import org.biosino.common.core.utils.DateUtils;
import org.biosino.common.core.utils.ThreadPoolUtil;
import org.junit.jupiter.api.Test;

import java.util.*;
import java.util.concurrent.*;

public class FutureTest {

    @Test
    public void testA() {
        ArrayList<String> analMultipleNos = CollUtil.newArrayList("OEZ000001", "OEZ000002", "OEZ000003", "OEZ000004", "OEZ000005", "OEZ000006");
        Submission submission = new Submission();
        Map<String, String> idMap = new HashMap<>();
        updateAnalyses(analMultipleNos, "LRJ", "submitter-LRJ", submission, idMap);
    }


    public void updateAnalyses(List<String> analMultipleNos, String creator, String submitter, Submission submission, Map<String, String> idMap) {
        List<String> newAnalNos = new ArrayList<>();

        ExecutorService executorService = ThreadPoolUtil.executor();

        List<Future<String>> futures = new ArrayList<>();

        for (String analMultipleNo : analMultipleNos) {
            System.out.println(1);
            Callable<String> task = () -> {
                String newAnalNo = updateAnalysis(analMultipleNo, creator, submitter);
                if (StrUtil.isNotBlank(newAnalNo)) {
                    idMap.put(analMultipleNo, newAnalNo);
                    return newAnalNo;
                } else {
                    return analMultipleNo;
                }
            };
            futures.add(executorService.submit(task));
        }

        for (Future<String> future : futures) {
            System.out.println(2);
            try {
                newAnalNos.add(future.get());
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
                // Handle the exception as needed
            }
        }

        System.out.println(3);
        executorService.shutdown();
        System.out.println(4);
        try {
            executorService.awaitTermination(5, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println(5);
        submission.setAnalMultipleNos(newAnalNos);
        saveSubmission(submission);

        System.out.println("==========================================================");


        List<Future<String>> futures2 = new ArrayList<>();

        for (String analMultipleNo : analMultipleNos) {
            System.out.println(1);
            Callable<String> task = () -> {
                String newAnalNo = updateAnalysis(analMultipleNo, creator, submitter);
                if (StrUtil.isNotBlank(newAnalNo)) {
                    idMap.put(analMultipleNo, newAnalNo);
                    return newAnalNo;
                } else {
                    return analMultipleNo;
                }
            };
            futures2.add(executorService.submit(task));
        }

        for (Future<String> future : futures2) {
            System.out.println(2);
            try {
                newAnalNos.add(future.get());
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
                // Handle the exception as needed
            }
        }

        System.out.println(3);
        executorService.shutdown();
        System.out.println(4);
        try {
            executorService.awaitTermination(5, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        System.out.println(5);
    }

    private String updateAnalysis(String analMultipleNo, String creator, String submitter) {
        ThreadUtil.safeSleep(1000);
        System.out.println(DateUtils.formatDateTime(new Date()));
        // Implementation of updateAnalysis method
        return "";
    }

    private void saveSubmission(Submission submission) {
        // Implementation of saveSubmission method
    }

    // Assuming StrUtil and Submission classes are defined elsewhere
    static class StrUtil {
        static boolean isNotBlank(String str) {
            return str != null && !str.trim().isEmpty();
        }
    }

    static class Submission {
        private List<String> analMultipleNos;

        public void setAnalMultipleNos(List<String> analMultipleNos) {
            this.analMultipleNos = analMultipleNos;
        }
    }
}
