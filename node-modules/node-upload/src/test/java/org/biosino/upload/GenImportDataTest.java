package org.biosino.upload;

import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.SecureUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.biosino.common.core.exception.ServiceException;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/7
 */
public class GenImportDataTest {

    @Test
    public void gen5000Experiment() {
        List<LinkedHashMap<String, String>> datas = new ArrayList<>();
        for (int i = 0; i < 5000; i++) {
            LinkedHashMap<String, String> data = new LinkedHashMap<>();
            data.put("experiment_name", "experiment_name_" + (i + 1));
            data.put("project_name", "基本project");
            data.put("description", "description_" + (i + 1));
            data.put("related_links", "related_links_" + (i + 1));
            data.put("library_selection", "RANDOM");
            data.put("library_strategy", "RNA-Seq");
            data.put("library_layout", "Paired");
            data.put("platform", "454 GS");
            data.put("mate_pair", "Y");
            data.put("read_length_for_mate1(bp)", i + 1 + "");
            datas.add(data);
        }
        File file = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_Genomic_v1.0.xlsx");
        File outputFile = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_Genomic_v1.0_5000.xlsx");
        FileUtil.touch(outputFile);
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, datas, 0, 12);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }

    }

    @Test
    public void gen5000Sample() {
        List<LinkedHashMap<String, String>> datas = new ArrayList<>();
        for (int i = 0; i < 5000; i++) {
            LinkedHashMap<String, String> data = new LinkedHashMap<>();
            data.put("sample_name", "sample_name_" + (i + 1));
            data.put("organism", "Homo sapiens");
            data.put("tissue", "tissue_" + (i + 1));
            data.put("description", "description_" + (i + 1));
            data.put("sample_collection_date", "2019-01-21");
            data.put("sex", "male");
            data.put("age", "18y");
            data.put("isolate", "isolate_" + (i + 1));
            datas.add(data);
        }
        File file = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_SAP_Human_v2.0.xlsx");
        File outputFile = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_SAP_Human_v2.0_5000.xlsx");
        FileUtil.touch(outputFile);
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, datas, 0, 12);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }

    }

    @Test
    public void gen5000Data() {
        File dir = FileUtil.file("D:\\test\\node\\data\\data\\test2_5000");
        FileUtil.mkdir(dir);
        for (int i = 0; i < 5000; i++) {
            File file = FileUtil.writeUtf8String("test_" + i, FileUtil.file(dir, "test_" + i + ".txt"));
            FileUtil.writeUtf8String(SecureUtil.md5(file), FileUtil.file(dir, "test_" + i + ".txt" + ".md5"));
        }
    }

    @Test
    public void gen5000RawDataArchive() {
        List<LinkedHashMap<String, String>> datas = new ArrayList<>();
        for (int i = 0; i < 5000; i++) {
            LinkedHashMap<String, String> data = new LinkedHashMap<>();
            data.put("experiment_name", "experiment_name_" + (i + 1));
            data.put("sample_name", "sample_name_" + (i + 1));
            data.put("run_name", "run_name_" + (i + 1));
            data.put("data_id", "OED00" + (i + 935703));
            datas.add(data);
        }
        File file = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_archiving_rawData_v1.0.xlsx");
        File outputFile = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_archiving_rawData_v1.0_5000.xlsx");
        FileUtil.touch(outputFile);
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, datas, 0, 12);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
    }

    @Test
    public void gen5000Analysis() {
        List<LinkedHashMap<String, String>> datas = new ArrayList<>();
        for (int i = 0; i < 5000; i++) {
            LinkedHashMap<String, String> data = new LinkedHashMap<>();
            data.put("analysis_name", "analysis_name_" + (i + 1));
            data.put("analysis_type", "De Novo Assembly");
            data.put("target_project", "OEP00005465");
            data.put("description", "description_" + (i + 1));
            datas.add(data);
        }
        File file = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_analysis_v1.0.xlsx");
        File outputFile = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_analysis_v1.0_5000.xlsx");
        FileUtil.touch(outputFile);
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, datas, 0, 12);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
    }

    @Test
    public void gen5000AnalysisDataArchive() {
        List<LinkedHashMap<String, String>> datas = new ArrayList<>();
        for (int i = 0; i < 5000; i++) {
            LinkedHashMap<String, String> data = new LinkedHashMap<>();
            data.put("analysis_name", "analysis_name_" + (i + 1));
            data.put("data_id", "OED00" + (i + 940735));
            datas.add(data);
        }
        File file = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_archiving_analysis_v1.0.xlsx");
        File outputFile = FileUtil.file("D:\\test\\node\\data\\documents\\download\\NODE_archiving_analysis_v1.0_5000.xlsx");
        FileUtil.touch(outputFile);
        try (FileInputStream fis = new FileInputStream(file)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);
            writeSheet(workbook, datas, 0, 12);
            try (FileOutputStream fos = new FileOutputStream(outputFile)) {
                workbook.write(fos);
            }
        } catch (Exception e) {
            throw new ServiceException("Write excel error: " + e.getMessage());
        }
    }


    private static void writeSheet(XSSFWorkbook workbook, List<LinkedHashMap<String, String>> dataList, Integer sheetIndex, Integer headRowIndex) {
        XSSFSheet sheet0 = workbook.getSheetAt(sheetIndex);
        Row headerRow = sheet0.getRow(headRowIndex);
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet0.createRow(13 + i); // 从第11行开始写入
            Map<String, String> dataMap = dataList.get(i);

            for (int j = 0; j < headerRow.getLastCellNum(); j++) {
                Cell headerCell = headerRow.getCell(j);
                if (headerCell != null) {
                    headerCell.setCellType(CellType.STRING);
                    String header = headerCell.getStringCellValue();
                    String value = dataMap.get(header);
                    Cell cell = row.createCell(j);
                    cell.setCellValue(value != null ? value : "");
                }
            }
        }
    }

}
