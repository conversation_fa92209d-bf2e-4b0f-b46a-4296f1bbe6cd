package org.biosino.upload;

import org.biosino.common.core.utils.AESUtil;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

public class TokenTest {
    /**
     * 生成组学license
     */
    @Test
    void experiment() throws Exception {

        String license = encrypt("Genomic,Transcriptomic,Metagenomic,Metatranscriptomic,Genomic single cell," +
                "Transcriptomic single cell,Proteomic,Metabolomic,Electron microscopy,Microarray,Synthetic,Viral RNA,Flow cytometry");

        System.out.println("License:");
        System.out.println(license);

        System.out.println("=========================================");

        System.out.println("License逆向解析验证");
        String originalType = checkLicense(license);
        System.out.println(originalType);
    }

    /**
     * 生成样本组学license
     */
    @Test
    void sample() throws Exception {

        String license = encrypt("Human,Animalia,Plantae,Pathogen affecting public health,Cell line,Environment host," +
                "Environment non-host,Microbe,Marine-water,Marine-sediment,Estuary-water,Estuary-sediment,Lake-water,Lake-sediment," +
                "Wetland-soil,Wetland-sediment,Acid_mine-water,Acid_mine-sediment,Acid_mine-wastewater");

        System.out.println("License:");
        System.out.println(license);

        System.out.println("=========================================");

        System.out.println("License逆向解析验证");
        String originalType = checkLicense(license);
        System.out.println(originalType);
    }

    String encrypt(String inputType) throws Exception {
        return AESUtil.encrypt(inputType, StandardCharsets.UTF_8, AESUtil.DEFAULT_KEY);
    }

    String checkLicense(String license) {
        try {
            return AESUtil.decrypt(license, StandardCharsets.UTF_8, AESUtil.DEFAULT_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            return "解析失败";
        }
    }

    @Test
    void testName() {
        // 编辑
        for (int i = 1; i < 10001; i++) {
            System.out.println("Genomic_name_" + i);
        }
    }

    @Test
    void testId() {
        // 编辑
        for (int i = 1; i < 10001; i++) {
            System.out.println("OEP004707");
        }
    }
}
