package org.biosino.node.fastqc.mq;

import com.alibaba.fastjson2.JSON;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.FastQCTaskStatusEnum;
import org.biosino.common.rabbitmq.MessageSender;
import org.biosino.common.rabbitmq.msg.FastQCTaskStartMsg;
import org.biosino.common.rabbitmq.msg.FastQCTaskStatusMsg;
import org.biosino.node.fastqc.service.FastqcService;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/5/21
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FastqcTaskListener {

    private final FastqcService fastqcService;

    private final MessageSender messageSender;

    @RabbitListener(queues = "fastqc_task_start_queue", concurrency = "10")
    @RabbitHandler
    public void handlerTaskStart(@Payload FastQCTaskStartMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("fastqc_task_start_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            fastqcService.handlerTaskStart(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 发送错误消息给前端
            FastQCTaskStatusMsg statusMsg = new FastQCTaskStatusMsg();
            statusMsg.setDataNo(msg.getDataNo());
            statusMsg.setStatus(FastQCTaskStatusEnum.failed.name());
            statusMsg.setFailCause("fastqc service error, please contact admin");
            messageSender.sendDelayMsg("fastqc_task_status_routing_key", statusMsg);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }

    /**
     * high priority
     */
    @RabbitListener(queues = "fastqc_hp_task_start_queue", concurrency = "10")
    @RabbitHandler
    public void handlerHpTaskStart(@Payload FastQCTaskStartMsg msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        log.info("fastqc_hp_task_start_queue {} 接收到消息: {}", this.getClass().getCanonicalName(), JSON.toJSONString(msg));
        try {
            fastqcService.handlerTaskStart(msg);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            // 发送错误消息给前端
            FastQCTaskStatusMsg statusMsg = new FastQCTaskStatusMsg();
            statusMsg.setDataNo(msg.getDataNo());
            statusMsg.setStatus(FastQCTaskStatusEnum.failed.name());
            statusMsg.setFailCause("fastqc service error, please contact admin");
            messageSender.sendDelayMsg("fastqc_task_status_routing_key", statusMsg);
        } finally {
            // 确认消息
            channel.basicAck(tag, false);
        }
    }
}
