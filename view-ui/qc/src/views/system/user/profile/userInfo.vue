<template>
  <el-form ref="userRef" :model="form" :rules="rules" label-width="130px">
    <el-form-item label="Nick Name" prop="nickName">
      <el-input v-model="form.nickName" style="width: 350px" />
    </el-form-item>
    <el-form-item label="Phone Number" prop="phonenumber">
      <el-input v-model="form.phonenumber" style="width: 350px" />
    </el-form-item>
    <el-form-item label="Email" prop="email">
      <el-input v-model="form.email" style="width: 350px" />
    </el-form-item>
    <el-form-item label="Gender">
      <el-radio-group v-model="form.sex" style="width: 350px">
        <el-radio label="0">male</el-radio>
        <el-radio label="1">female</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item class="justify-center">
      <div style="margin-left: 80px">
        <el-button type="primary" @click="submit">Save</el-button>
        <el-button type="danger" @click="close">Close</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { updateUserProfile } from '@/api/system/user';

  const props = defineProps({
    user: {
      type: Object,
    },
  });

  const { proxy } = getCurrentInstance();

  const form = ref({});
  const rules = ref({
    nickName: [
      { required: true, message: '用户昵称不能为空', trigger: 'blur' },
    ],
    email: [
      { required: true, message: '邮箱地址不能为空', trigger: 'blur' },
      {
        type: 'email',
        message: '请输入正确的邮箱地址',
        trigger: ['blur', 'change'],
      },
    ],
    phonenumber: [
      { required: true, message: '手机号码不能为空', trigger: 'blur' },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: '请输入正确的手机号码',
        trigger: 'blur',
      },
    ],
  });

  /** 提交按钮 */
  function submit() {
    proxy.$refs.userRef.validate(valid => {
      if (valid) {
        updateUserProfile(form.value).then(response => {
          proxy.$modal.msgSuccess('修改成功');
          props.user.phonenumber = form.value.phonenumber;
          props.user.email = form.value.email;
        });
      }
    });
  }

  /** 关闭按钮 */
  function close() {
    proxy.$tab.closePage();
  }

  // 回显当前登录用户信息
  watch(
    () => props.user,
    user => {
      if (user) {
        form.value = {
          nickName: user.nickName,
          phonenumber: user.phonenumber,
          email: user.email,
          sex: user.sex,
        };
      }
    },
    { immediate: true },
  );
</script>
<style lang="scss" scoped>
  .el-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    .el-form-item {
      width: 50%;
    }
  }
</style>
