<template>
  <div class="app-container">
    <div class="card list">
      <el-row :gutter="20">
        <el-col :span="24" :xs="24">
          <el-form ref="queryRef" :model="queryParams" :inline="true">
            <el-form-item label="Submission ID" prop="subNo">
              <el-input
                v-model="queryParams.subNo"
                style="width: 150px"
                clearable
                @keyup.enter="getDataList"
              />
            </el-form-item>

            <el-form-item label="Creator" prop="creator">
              <el-input
                v-model="queryParams.creatorEmail"
                clearable
                @keyup.enter="getDataList"
              />
            </el-form-item>

            <el-form-item label="Auditor">
              <el-select
                v-model="queryParams.auditor"
                style="width: 150px"
                @change="getDataList"
              >
                <el-option
                  v-for="(item, idx) in roleList"
                  :key="'role-' + idx"
                  :label="item.userName"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="Submission Time">
              <el-date-picker
                v-model="dateRange"
                clearable
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="Start Date"
                end-placeholder="End Date"
                style="width: 220px"
              ></el-date-picker>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >Search</el-button
              >
              <el-button icon="Refresh" @click="reset">Reset</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <el-divider class="mb-05 mt-1"></el-divider>

      <div class="status-select mb-1">
        <span class="font-600 text-main-color mr-2">Status:</span>
        <el-radio-group v-model="queryParams.status" @change="getDataList">
          <el-radio label="All">All</el-radio>
          <el-radio label="waiting">Waiting Review</el-radio>
          <el-radio label="reviewing">Reviewing</el-radio>
          <el-radio label="rejected">Rejected</el-radio>
          <el-radio label="editing">Rejected & Editing</el-radio>
          <el-radio label="complete">Complete</el-radio>
        </el-radio-group>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        :default-sort="defaultSort"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        :row-style="{
          position: 'relative',
        }"
        border
        @sort-change="handleSortChange"
      >
        <el-table-column
          prop="subNo"
          label="Submission ID"
          width="140"
          sortable
        >
          <template #default="scope">
            <span
              class="text-primary cursor-pointer"
              @click="toDetail(scope.row)"
            >
              {{ scope.row.subNo }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="dataNumber"
          width="360"
          label="Number of data entries"
        >
          <template #default="scope">
            <div class="d-flex">
              <template v-if="scope.row.dataType === 'rawData'">
                <div class="d-flex align-items-center mr-1">
                  <svg-icon icon-class="proj" class-name="svg-data"></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.projNum }}
                  </span>
                </div>
                <div class="d-flex align-items-center mr-1">
                  <svg-icon icon-class="exp" class-name="svg-data"></svg-icon>
                  <span class="number ml-03"> {{ scope.row.expNum }}</span>
                </div>
                <div class="d-flex align-items-center mr-1">
                  <svg-icon icon-class="samp" class-name="svg-data"></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.sapNum }}
                  </span>
                </div>
                <div class="d-flex align-items-center mr-1">
                  <svg-icon icon-class="run" class-name="svg-data"></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.runNum }}
                  </span>
                </div>
                <div class="d-flex align-items-center">
                  <svg-icon icon-class="data" class-name="svg-data"></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.dataNum }}
                  </span>
                </div>
              </template>

              <template v-else-if="scope.row.dataType === 'analysisData'">
                <div class="d-flex align-items-center mr-1">
                  <svg-icon
                    icon-class="analysis"
                    class-name="svg-data"
                  ></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.analNum }}
                  </span>
                </div>
                <div class="d-flex align-items-center">
                  <svg-icon icon-class="data" class-name="svg-data"></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.dataNum }}
                  </span>
                </div>
              </template>
              <template v-else-if="scope.row.dataType === 'project'">
                <svg-icon icon-class="proj" class-name="svg-data"></svg-icon>
                <span class="number ml-03">
                  {{ scope.row.projNum }}
                </span>
              </template>
              <template v-else-if="scope.row.dataType === 'experiment'">
                <svg-icon icon-class="exp" class-name="svg-data"></svg-icon>
                <span class="number ml-03"> {{ scope.row.expNum }}</span>
              </template>
              <template v-else-if="scope.row.dataType === 'sample'">
                <svg-icon icon-class="samp" class-name="svg-data"></svg-icon>
                <span class="number ml-03">
                  {{ scope.row.sapNum }}
                </span>
              </template>
              <template v-else-if="scope.row.dataType === 'run'">
                <svg-icon icon-class="run" class-name="svg-data"></svg-icon>
                <span class="number ml-03">
                  {{ scope.row.runNum }}
                </span>
              </template>
              <template v-else-if="scope.row.dataType === 'data'">
                <svg-icon icon-class="data" class-name="svg-data"></svg-icon>
                <span class="number ml-03">
                  {{ scope.row.dataNum }}
                </span>
              </template>
              <template v-else-if="scope.row.dataType === 'analysis'">
                <div class="d-flex align-items-center mr-1">
                  <svg-icon
                    icon-class="analysis"
                    class-name="svg-data"
                  ></svg-icon>
                  <span class="number ml-03">
                    {{ scope.row.analNum }}
                  </span>
                </div>
              </template>
              <template v-else-if="scope.row.dataType === 'publish'">
                <svg-icon
                  icon-class="publish"
                  class-name="svg-data"
                  class="publish-icon"
                ></svg-icon>
                <span class="number ml-03">
                  {{ 1 }}
                </span>
              </template>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="submitter"
          label="Submitter"
          min-width="110"
          sortable
        />
        <el-table-column
          prop="creatorEmail"
          label="Creator"
          min-width="200"
          sortable
        />
        <el-table-column
          prop="submitTime"
          label="Submission Time"
          width="160"
          sortable
        />
        <el-table-column prop="status" label="Status" width="155" sortable>
          <template #default="scope">
            <div
              v-if="
                scope.row.status !== 'rejected' &&
                scope.row.status !== 'editing' &&
                scope.row.status !== 'reviewing' &&
                scope.row.status !== 'waiting'
              "
              class="d-flex align-items-center"
            >
              <el-icon
                v-if="scope.row.status === 'complete'"
                color="#07BCB4"
                size="19"
              >
                <CircleCheckFilled />
              </el-icon>

              <svg-icon
                v-if="scope.row.status === 'editing'"
                icon-class="editing"
                class-name="edit-svg"
              >
              </svg-icon>

              <span
                class="ml-05"
                :style="{
                  color: iconColor(scope.row.status),
                }"
                >{{
                  scope.row.status.charAt(0).toUpperCase() +
                  scope.row.status.slice(1)
                }}
              </span>
            </div>

            <div
              v-else-if="scope.row.status === 'waiting'"
              class="d-flex align-items-center"
            >
              <svg-icon
                icon-class="waiting"
                class-name="edit-waiting"
              ></svg-icon>

              <span
                class="ml-05"
                :style="{
                  color: iconColor(scope.row.status),
                }"
                >{{ scope.row.processing ? 'Processing' : 'Waiting review' }}
              </span>
            </div>

            <div
              v-else-if="scope.row.status === 'reviewing'"
              class="d-flex align-items-center"
            >
              <svg-icon
                icon-class="reviewing"
                class-name="edit-waiting"
              ></svg-icon>

              <span
                class="ml-05"
                :style="{
                  color: iconColor(scope.row.status),
                }"
                >Reviewing
              </span>
            </div>

            <div
              v-else-if="scope.row.status === 'editing'"
              class="d-flex align-items-center"
            >
              <svg-icon icon-class="editing" class-name="edit-svg"> </svg-icon>

              <span
                class="ml-05"
                :style="{
                  color: iconColor(scope.row.status),
                }"
                >Rejected & Editing
              </span>
            </div>

            <div v-else class="d-flex align-items-center">
              <el-icon size="19" color="#FF8181">
                <CircleCloseFilled />
              </el-icon>
              <span class="ml-05 mr-05" style="color: #ff8989">Rejected</span>
              <el-popover placement="right" width="450" trigger="hover">
                <template #reference>
                  <el-icon color="#DA0619" class="cursor-pointer">
                    <ChatDotRound />
                  </el-icon>
                </template>
                <div v-if="scope.row.rejectReason">
                  <div class="text-main-color font-600 font-16 text-center">
                    Reason for rejection
                  </div>
                  <el-divider class="mb-1 mt-1"></el-divider>
                  <div class="mb-1">
                    <div
                      v-for="(item, idx) in scope.row.rejectReason"
                      :key="'row-rejectReason' + idx"
                    >
                      <el-divider
                        v-if="idx !== 0"
                        class="mb-1 mt-1"
                      ></el-divider>
                      <div class="mb-05">
                        <span
                          class="text-secondary-color fail-label font-600 mr-05"
                          >Reason Type:</span
                        >
                        <span class="text-secondary-color">{{
                          item.type
                        }}</span>
                      </div>
                      <div class="d-flex">
                        <span
                          class="text-secondary-color fail-label font-600 label mr-05"
                          >Details:</span
                        >
                        <span class="text-secondary-color">{{
                          item.reason
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="auditor" label="Auditor" min-width="100" />
        <el-table-column
          prop="auditTime"
          label="Audit Time"
          width="160"
          sortable
        />
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { useRouter } from 'vue-router';
  import { listSubmission } from '@/api/submission/audit';
  import { findQcUser } from '@/api/system/user';
  import useUserStore from '@/store/modules/user';

  const { proxy } = getCurrentInstance();
  const userStore = useUserStore();

  const router = useRouter();

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'updateTime',
      isAsc: 'descending',
      auditor: '',
      status: 'All',
      subNo: '',
      creatorEmail: '',
    },
    dateRange: [],
    roleList: [],
    loading: false,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  const {
    tableData,
    total,
    queryParams,
    dateRange,
    loading,
    defaultSort,
    roleList,
  } = toRefs(data);

  onMounted(() => {
    findQcUserData();
  });

  /** 触发排序事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  /** 查询审核员列表*/
  function findQcUserData() {
    findQcUser().then(response => {
      roleList.value = response.data;

      queryParams.value.auditor = userStore.id;

      getDataList();
    });
  }

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listSubmission(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 重置查询*/
  function reset() {
    proxy.resetForm('queryRef');
    dateRange.value = [];
    getDataList();
  }

  const iconColor = status => {
    if (status === 'complete') {
      return '#07BCB4';
    } else if (status === 'rejected') {
      return '#FF8989';
    } else if (status === 'editing') {
      return '#3A78E8';
    } else return '#FE7F2B';
  };

  const toDetail = row => {
    router.push({
      path: `/submission/detail/audit/${row.subNo}`,
    });
  };
</script>

<style lang="scss" scoped>
  :deep(.el-input__wrapper) {
    border-radius: 12px;
  }

  .svg-data {
    width: 20px;
    height: 22px;
  }

  .edit-svg {
    width: 17px;
    height: 17px;
    color: #409eff;
  }

  .edit-waiting {
    width: 16px;
    height: 16px;
  }

  .review-svg {
    width: 18px;
    height: 18px;
    cursor: pointer;
  }

  :deep(.el-form--inline .el-form-item) {
    margin-bottom: 0 !important;
  }

  .fail-label {
    display: inline-block;
    width: 85px;
    text-align: right;
  }

  .publish-icon {
    height: 22px;
    width: 22px;
    margin-left: -2px;
  }
</style>
