<template>
  <div v-loading="loading" class="card mt-1">
    <el-row>
      <el-col :span="24" align="center" class="d-flex justify-center">
        <span
          v-for="(item, index) in Object.keys(statusStatInfo)"
          :key="`status_stat_${index}`"
          class="ml-05 d-flex align-items-center"
        >
          <el-icon :class="statusMap[item].textCls" class="ml-1 mr-03">
            <component :is="statusMap[item].icon" />
          </el-icon>
          {{ getStatusName(item) }}：{{ statusStatInfo[item] }}</span
        >
      </el-col>
    </el-row>
    <el-divider class="mb-1 mt-1"></el-divider>
    <el-row :gutter="20">
      <el-col :span="24" :xs="24">
        <el-form ref="searchFormRef" :model="queryParams" :inline="true">
          <el-form-item label="数据" prop="dataNo">
            <el-input
              v-model="queryParams.dataNo"
              style="width: 300px"
              clearable
              placeholder="搜索数据ID或名称"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="getDataList"
              >搜索
            </el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="d-flex align-items-center mb-05">
      <span class="font-600 text-secondary-color mr-1">状态: </span>
      <el-radio-group v-model="queryParams.status" @change="getDataList">
        <el-radio value="" label="All">全部</el-radio>
        <el-radio value="ready" label="Ready">就绪</el-radio>
        <el-radio value="queuing" label="Queuing">排队中</el-radio>
        <el-radio value="running" label="Running">运行中</el-radio>
        <el-radio value="success" label="Success">成功</el-radio>
        <el-radio value="failed" label="Failed">失败</el-radio>
      </el-radio-group>
    </div>

    <el-table
      tooltip-effect="light"
      :data="tableData"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-style="{
        position: 'relative',
      }"
      max-height="550"
      class="data-list"
      border
      :default-sort="defaultSort"
      @sort-change="handleSortChange"
    >
      <el-table-column label="数据ID" prop="dataNo" min-width="115" sortable />
      <el-table-column
        label="数据名称"
        prop="dataFileName"
        min-width="200"
        show-overflow-tooltip
      />
      <el-table-column
        label="文件大小"
        prop="dataFileSize"
        min-width="100"
        sortable
      >
        <template #default="scope">
          {{ filesize(scope.row.dataFileSize) }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态 : 原因"
        prop="status"
        min-width="90"
        width="200"
        sortable
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.status
          }}{{
            scope.row.status === 'failed' ? ` : ${scope.row.failCause}` : ''
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="创建日期"
        prop="createDate"
        min-width="160"
        sortable
      />
      <el-table-column
        label="状态更新日期"
        prop="updateDate"
        min-width="170"
        sortable
      />
      <el-table-column label="操作" fixed="right" align="center" width="110">
        <template #default="scope">
          <el-tooltip
            v-if="scope.row.status === 'failed'"
            content="查看错误日志"
          >
            <svg-icon
              icon-class="review"
              class-name="download download-svg"
              @click="toErrorLogPage(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
          <el-tooltip v-if="scope.row.status === 'failed'" content="重试">
            <svg-icon
              icon-class="retry"
              class-name="ml-05 download download-svg"
              @click="retryTask(scope.row.dataNo)"
            ></svg-icon>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      :auto-scroll="false"
      @pagination="getDataList"
    />
  </div>
</template>
<script setup>
  import {
    defineEmits,
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    getSamToolQcDetail,
    getSamToolQcStatusStat,
    retrySamToolTask,
  } from '@/api/submission/audit';
  import {
    Failed,
    InfoFilled,
    Loading,
    Stopwatch,
    SuccessFilled,
  } from '@element-plus/icons-vue';
  import { filesize } from 'filesize';

  const { proxy } = getCurrentInstance();
  const emit = defineEmits();

  let props = defineProps({
    subNo: {
      type: String,
      required: true,
    },
    qcFinishFlag: {
      type: Boolean,
      required: true,
    },
  });

  onMounted(() => {
    getDataList();
  });

  const { subNo } = props;

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      subNo: '',
      dataNo: '',
      status: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'dataNo',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'dataNo', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  let statusStatInfo = ref({});

  function getDataList() {
    queryParams.value.subNo = subNo;
    getSamToolQcDetail(queryParams.value)
      .then(response => {
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
        getSamToolQcStatusStat(subNo).then(res => {
          statusStatInfo.value = res.data;
        });
      });
  }

  watch(
    statusStatInfo,
    newVal => {
      let successTask =
        newVal['ready'] === 0 &&
        newVal['queuing'] === 0 &&
        newVal['running'] === 0 &&
        newVal['failed'] === 0;
      emit('update:qcFinishFlag', successTask);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  let statusMap = {
    ready: {
      textCls: 'text-primary',
      icon: InfoFilled,
    },
    queuing: {
      textCls: 'text-warning',
      icon: Stopwatch,
    },
    running: {
      textCls: 'text-warning',
      icon: Loading,
    },
    success: {
      textCls: 'text-success',
      icon: SuccessFilled,
    },
    failed: {
      textCls: 'text-danger',
      icon: Failed,
    },
  };

  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('searchFormRef');
    getDataList();
  }

  function toErrorLogPage(dataNo) {
    window.open(
      `${import.meta.env.VITE_APP_BASE_API}/app/fastqc/samtool/errorLog/${dataNo}`,
    );
  }

  function retryTask(no) {
    proxy.$modal.confirm(`确认重试任务 ${no}？`).then(() => {
      retrySamToolTask({
        dataNos: no,
      })
        .then(response => {
          proxy.$modal.alertSuccess('任务已重新提交');
          getDataList();
        })
        .finally(() => {});
    });
  }

  function getStatusName(status) {
    const statusNameMap = {
      ready: '就绪',
      queuing: '排队中',
      running: '运行中',
      success: '成功',
      failed: '失败',
    };
    return statusNameMap[status] || status;
  }
</script>

<style lang="scss" scoped>
  .downloadTable {
    :deep(.el-table td.el-table__cell div) {
      display: flex;
      align-items: center;
    }
  }

  .download-svg {
    width: 20px;
    height: 20px;
  }

  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }

  :deep(.el-table td.el-table__cell:last-child div) {
    justify-content: center !important;
  }

  .download {
    width: 20px;
    cursor: pointer;
  }
</style>
