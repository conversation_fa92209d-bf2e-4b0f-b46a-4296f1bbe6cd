<template>
  <div v-if="showPublish">
    <el-divider v-if="showTitle" content-position="left"
      ><h3 class="preview-title">Publications</h3></el-divider
    >

    <div
      v-for="(it, index) in publishData"
      :key="'publish-preview-' + index"
      class="content bg-gray plr-20"
    >
      <div v-if="index > 0" class="mt-05"></div>
      <p
        class="text-secondary-color font-600"
        :class="publishData.length === 1 ? 'ml-1' : ''"
      >
        <span v-if="publishData.length > 1">{{ index + 1 }}.</span>
        {{ it.articleName }}
      </p>
      <p class="text-other-color reference ml-1" v-html="it.reference"></p>
      <p class="ml-05">
        <span v-if="it.pmid" class="mr-1">
          <el-tag
            v-if="showJournal && it.publication"
            round
            type="success"
            effect="light"
            class="mr-1"
          >
            {{ it.publication }}
          </el-tag>
          <span>( PMID: </span>
          <svg-icon icon-class="link" class-name="svg svg-link"></svg-icon>
          <a
            target="_blank"
            :href="`https://pubmed.ncbi.nlm.nih.gov/${it.pmid}/`"
            class="text-success cursor-pointer"
          >
            &nbsp;{{ `${it.pmid}` }}</a
          >
          )
        </span>
        <span v-if="it.doi">
          <span>( DOI: </span>
          <svg-icon icon-class="link" class-name="svg svg-link"></svg-icon>
          <a
            target="_blank"
            :href="`https://doi.org/${it.doi}`"
            class="text-success cursor-pointer"
            >&nbsp;{{ it.doi }}</a
          >
          )
        </span>
      </p>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue';

  const props = defineProps({
    showTitle: {
      type: Boolean,
      required: false,
      default: true,
    },
    showJournal: {
      type: Boolean,
      required: false,
      default: true,
    },
    publishData: {
      type: Array,
    },
  });

  const showPublish = ref(false);

  onMounted(() => {
    updatePublish(props.publishData);
  });

  function updatePublish(newValue) {
    showPublish.value = false;

    if (newValue && newValue.length > 0) {
      newValue.forEach(obj => {
        for (const key in obj) {
          if (
            key !== 'id' &&
            key !== 'typeInfoList' &&
            key !== 'sort' &&
            obj[key] &&
            obj[key].trim() !== ''
          ) {
            showPublish.value = true;
          }
        }
      });
    }
  }

  watch(props.publishData, newValue => {
    updatePublish(newValue);
  });
</script>

<style scoped>
  .svg-link {
    width: 12px;
    height: 12px;
  }
</style>
