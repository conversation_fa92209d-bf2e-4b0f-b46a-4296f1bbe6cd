<template>
  <div class="app-container">
    <div class="radius-12 card">
      <div v-loading="loading" class="radius-12 sub-detail card">
        <div class="submit-card">
          <div
            class="align-items-center card-shadow radius-12 w-35 bg-white p-20 pl-10"
          >
            <el-row
              :gutter="20"
              class="submit-info align-items-center pl-10"
              :class="computedBgClass"
            >
              <el-col :span="12">
                <span class="font-600 text-main-color font-24">
                  ID: {{ subNo }}
                </span>
              </el-col>
              <el-col :span="12">
                <span
                  v-if="processing"
                  class="sub-state w-60"
                  :class="linearBg"
                >
                  数据处理中
                </span>
                <span v-else class="sub-state w-60" :class="linearBg">
                  {{ statusName }}
                </span>
              </el-col>
              <el-col :span="12">
                <span>最后修改: {{ submission.updateTime }}</span>
              </el-col>
              <el-col v-if="submission.auditor" :span="12">
                <span>审核人: {{ submission.auditor }}</span>
              </el-col>
            </el-row>
          </div>
        </div>

        <div v-if="!loading" class="main-page-container">
          <div
            v-if="submission.rejectReason && submission.status !== 'complete'"
            class="mb-1"
          >
            <el-alert type="error">
              <template #default>
                <span class="font-14 font-600">驳回原因</span>
                <div
                  v-for="(item, idx) in submission.rejectReason"
                  :key="'rejectReason' + idx"
                >
                  <span class="font-14 font-600">{{ item.type }}:</span>
                  <span class="font-14 ml-05">{{ item.reason }}</span>
                </div>
              </template>
            </el-alert>
          </div>

          <!--Raw Data-->
          <template v-if="project">
            <div
              class="text-main-color font-600 category-title"
              :class="computedBgClass"
            >
              项目
            </div>
            <el-row :gutter="20" class="detail-submitter mt-05 row-gap">
              <el-col :span="8">
                <div class="label font-600 text-secondary-color">项目ID</div>
                <div
                  v-if="project.projectNo"
                  class="content bg-gray radius-12 text-primary"
                >
                  <a
                    class="text-primary"
                    href="javascript:void(0)"
                    @click="showDetail('project', project.projectNo)"
                  >
                    {{ project.projectNo }}
                  </a>
                </div>
                <div v-else class="content bg-gray radius-12">
                  审核通过后将自动分配。
                </div>
              </el-col>

              <el-col :span="8">
                <div
                  class="label font-600 text-secondary-color"
                  style="width: 200px"
                >
                  项目名称
                </div>

                <el-popover
                  placement="bottom"
                  :width="500"
                  trigger="hover"
                  :content="project.name"
                >
                  <template #reference>
                    <div
                      class="content bg-gray radius-12"
                      style="
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ $text(project.name) }}
                    </div>
                  </template>
                </el-popover>
              </el-col>

              <el-col :span="8">
                <div
                  class="label font-600 text-secondary-color"
                  style="width: 200px"
                >
                  项目描述
                </div>

                <el-popover
                  placement="bottom"
                  :width="500"
                  trigger="hover"
                  :content="project.description"
                >
                  <template #reference>
                    <div
                      class="content bg-gray radius-12"
                      style="
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                      "
                    >
                      {{ $text(project.description) }}
                    </div>
                  </template>
                </el-popover>
              </el-col>

              <el-col
                v-for="(it, index) in project.relatedLinks"
                :key="'project.relatedLinks' + index"
                :span="8"
              >
                <div class="label font-600 text-secondary-color">
                  相关链接 {{ index > 0 ? index + 1 : '' }}
                </div>
                <div class="content bg-gray radius-12 text-primary">
                  <a :href="it" target="_blank">{{ it }}</a>
                </div>
              </el-col>
            </el-row>

            <div
              v-if="
                project?.publish &&
                project?.publish.length !== 0 &&
                project?.publish[0].id
              "
              class="bg-gray p-15 mt-1"
            >
              <PreviewPublish
                :key="'project-publish-' + hotTableKey"
                v-model:publish-data="project.publish"
                :show-title="false"
              ></PreviewPublish>
            </div>
          </template>

          <div
            v-if="experiment"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            实验
          </div>

          <HotTable
            v-for="(exp, idx) in experiment"
            :id="hotTableKey + 'sub-detail-exp-ht-table-id-' + idx"
            :key="hotTableKey + 'sub-detail-exp-ht-table-key-' + idx"
            :hot-table="exp.rows"
            :hot-columns="exp.title"
            type="Experiment"
            :sub-type="exp.type"
            :creator="submission.creator"
          ></HotTable>

          <div
            v-if="expPublish && expPublish.length !== 0"
            class="bg-gray p-15 mt-1"
          >
            <PreviewPublish
              :key="'exp-publish-' + hotTableKey"
              v-model:publish-data="expPublish"
              :show-title="false"
            ></PreviewPublish>
          </div>

          <div
            v-if="sample"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            样本
          </div>

          <HotTable
            v-for="(exp, idx) in sample"
            :id="hotTableKey + 'sub-detail-sample-ht-table-id-' + idx"
            :key="hotTableKey + 'sub-detail-sample-ht-table-key-' + idx"
            :hot-table="exp.rows"
            :hot-columns="exp.title"
            type="Sample"
            :sub-type="exp.type"
            :creator="submission.creator"
          ></HotTable>

          <div
            v-if="sapPublish && sapPublish.length !== 0"
            class="bg-gray p-15 mt-1"
          >
            <PreviewPublish
              :key="'sap-publish-' + hotTableKey"
              v-model:publish-data="sapPublish"
              :show-title="false"
            ></PreviewPublish>
          </div>

          <div
            v-if="rawDataArchiveData"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            归档
          </div>

          <HotTable
            v-if="rawDataArchiveData"
            :id="'sub-detail-archiving-id-' + hotTableKey"
            :key="'sub-detail-archiving-key-' + hotTableKey"
            :hot-table="rawDataArchiveData"
            :hot-columns="archivingColumns"
            type="Archiving"
            sub-type="Raw Data"
            :creator="submission.creator"
          ></HotTable>

          <!--Analysis Data-->
          <div
            v-if="analysis"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            分析
          </div>
          <HotTable
            v-if="analysis"
            :id="'sub-detail-analysis-id-' + hotTableKey"
            :key="'sub-detail-analysis-key-' + hotTableKey"
            :hot-table="analysis"
            :hot-columns="analysisColumns"
            type="Analysis"
            :creator="submission.creator"
          ></HotTable>

          <div
            v-if="analysisPublish && analysisPublish.length !== 0"
            class="bg-gray p-15 mt-1"
          >
            <PreviewPublish
              :key="'analysis-publish-' + hotTableKey"
              v-model:publish-data="analysisPublish"
              :show-title="false"
            ></PreviewPublish>
          </div>

          <div
            v-if="analysisArchiveData"
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            归档
          </div>
          <HotTable
            v-if="analysisArchiveData"
            :id="'sub-detail-analysis-archiving-id-' + hotTableKey"
            :key="'sub-detail-analysis-archiving-key-' + hotTableKey"
            :hot-table="analysisArchiveData"
            :hot-columns="analysisArchivingColumns"
            type="Archiving"
            sub-type="Analysis"
            :creator="submission.creator"
          ></HotTable>

          <template v-if="publishInfo?.doi">
            <div
              class="text-main-color font-600 category-title mt-1"
              :class="computedBgClass"
            >
              发布
            </div>
            <div class="bg-gray p-15 mt-1">
              <PreviewPublish
                :key="'add-publish-' + hotTableKey"
                v-model:publish-data="publishArray"
                :show-title="false"
              ></PreviewPublish>
            </div>

            <el-table
              class="mt-1"
              :data="publishInfo?.typeInfoList"
              style="width: 100%; margin-bottom: 20px"
              :header-cell-style="{
                backgroundColor: '#f2f2f2',
                color: '#333333',
                fontWeight: 700,
              }"
              border
              max-height="300"
            >
              <el-table-column
                prop="typeNo"
                label="相关ID"
                width="120"
                sortable
              >
                <template #default="scope">
                  <a
                    class="text-primary"
                    href="javascript:void(0)"
                    @click="showDetail(scope.row.type, scope.row.typeNo)"
                  >
                    {{ scope.row.typeNo }}
                  </a>
                </template>
              </el-table-column>

              <el-table-column
                prop="typeName"
                label="相关名称"
                sortable
                show-overflow-tooltip
              />

              <el-table-column
                prop="articleName"
                label="发布标题"
                sortable
                show-overflow-tooltip
              />
              <el-table-column
                prop="email"
                label="创建者"
                sortable
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.username }} ({{ scope.row.email }})
                </template>
              </el-table-column>
              <el-table-column
                prop="createDate"
                label="发布创建日期"
                sortable
                width="180"
              />
            </el-table>
          </template>
          <div
            v-if="
              enableFastQC === 'enable' && submission.dataType === 'rawData'
            "
          >
            <div
              class="text-main-color font-600 category-title mt-1"
              :class="computedBgClass"
            >
              数据质控信息
            </div>
            <related-data-qc-info-list
              v-model:qc-finish-flag="qcFinishFlag"
              :sub-no="subNo"
            ></related-data-qc-info-list>
          </div>
          <div v-if="enableFastQC === 'enable'">
            <div
              class="text-main-color font-600 category-title mt-1"
              :class="computedBgClass"
            >
              SamTools质控信息
            </div>
            <related-sam-tool-qc-info-list
              v-model:qc-finish-flag="samToolQcFinishFlag"
              :sub-no="subNo"
            ></related-sam-tool-qc-info-list>
          </div>

          <div
            class="text-main-color font-600 category-title mt-1"
            :class="computedBgClass"
          >
            提交者
          </div>
          <el-row :gutter="20" class="detail-submitter mt-05">
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">名</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.firstName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">中间名</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.middleName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">姓</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.lastName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">组织机构</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.orgName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">部门</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.deptName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">PI姓名</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.piName) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">邮箱</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.email) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">电话</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.phone) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">传真</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.fax) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">街道</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.street) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">城市</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.city) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">州/省</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.stateProvince) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">邮政编码</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.postalCode) }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label font-600 text-secondary-color">国家/地区</div>
              <div class="content bg-gray radius-12">
                {{ $text(submission?.submitter?.countryRegion) }}
              </div>
            </el-col>
          </el-row>

          <div class="text-align-right mt-2 pr-20">
            <el-button
              v-if="!processing && submission.status === 'waiting'"
              type="warning"
              class="btn-warning btn btn-s btn-shadow"
              round
              @click="startReviewDialog = true"
              >开始审核
            </el-button>
            <el-button
              v-if="!processing && submission.status === 'reviewing'"
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              :disabled="
                enableFastQC === 'enable' &&
                ((!qcFinishFlag && submission.dataType === 'rawData') ||
                  !samToolQcFinishFlag)
              "
              @click="passDialog = true"
              >通过
            </el-button>
            <el-button
              v-if="!processing && submission.status === 'reviewing'"
              type="danger"
              class="mr-07"
              round
              @click="rejectDialog = true"
              >驳回
            </el-button>
            <el-button class="btn-primary btn btn-round" round @click="goBack">
              返回
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="rejectDialog"
      title="驳回原因"
      width="1000"
      center
      align-center
      class="radius-14"
    >
      <div
        v-for="(r, idx) in reject"
        :key="'reject' + idx"
        class="d-flex align-items-center mb-1"
      >
        <div class="text-center">
          <span class="text-secondary-color text-align-right mr-05"
            >驳回原因类型</span
          >
          <el-select
            v-model="reject[idx].type"
            filterable
            placeholder="请选择驳回原因类型"
            style="width: 180px"
          >
            <el-option
              v-for="dict in qc_reject_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
          <span class="text-secondary-color text-align-right mr-05 ml-1"
            >详细信息</span
          >
          <el-input
            v-model="reject[idx].reason"
            :rows="1"
            type="textarea"
            style="width: 485px; border-radius: 12px"
            placeholder="请输入详细的驳回原因"
          ></el-input>
          <el-button
            v-if="idx === 0"
            type="primary"
            :icon="Plus"
            circle
            plain
            class="ml-1"
            @click="addReject"
          />

          <el-button
            v-if="idx !== 0"
            type="warning"
            :icon="Minus"
            circle
            plain
            class="ml-1"
            @click="removeReject(idx)"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button
            class="radius-8"
            round
            type="primary"
            @click="rejectSubmit"
          >
            确认
          </el-button>
          <el-button
            plain
            round
            class="radius-8"
            type="primary"
            @click="rejectDialog = false"
            >取消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="startReviewDialog"
      title="确认审核此提交？"
      width="600"
      center
      :align-center="true"
      class="radius-14 passDialog"
    >
      <div class="font-16">
        如果您点击"确认"按钮，提交将进入审核流程且无法撤回。
      </div>
      <template #footer>
        <div class="dialog-footer mt-05 text-center">
          <el-button
            class="radius-8"
            round
            type="primary"
            @click="startReviewSubmit"
          >
            确认
          </el-button>
          <el-button
            plain
            round
            class="radius-8"
            type="primary"
            @click="startReviewDialog = false"
            >取消
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="passDialog"
      title="确认此提交已通过审核？"
      width="600"
      center
      :align-center="true"
      class="radius-14 passDialog"
    >
      <div class="font-16">
        如果您点击"确认"按钮，此提交中包含的所有数据将同步到NODE系统并分配正式的访问编号。
      </div>
      <template #footer>
        <div class="dialog-footer mt-05 text-center">
          <el-button class="radius-8" round type="primary" @click="passSubmit">
            确认
          </el-button>
          <el-button
            plain
            round
            class="radius-8"
            type="primary"
            @click="passDialog = false"
            >取消
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { computed, getCurrentInstance, onMounted, ref } from 'vue';
  import HotTable from '@/views/submission/detail/hotTable/table.vue';
  import { useRoute } from 'vue-router';
  import {
    getDetailData,
    pass,
    rejectSubmission,
    startReview,
  } from '@/api/submission/audit';
  import PreviewPublish from '@/views/submission/detail/common/PreviewPublish.vue';
  import { Minus, Plus } from '@element-plus/icons-vue';
  import { createAccessToken } from '@/api/login';
  import RelatedDataQcInfoList from '@/views/submission/detail/common/RelatedDataQcInfoList.vue';
  import RelatedSamToolQcInfoList from '@/views/submission/detail/common/RelatedSamToolQcInfoList.vue';
  import { getConfigKey } from '@/api/system/config';

  const { proxy } = getCurrentInstance();
  const route = useRoute();

  const { qc_reject_type } = proxy.useDict('qc_reject_type');

  const subNo = route.params.subNo;

  const submission = ref({});
  const project = ref({});

  const hotTableKey = ref(1);
  const loading = ref(false);

  const experiment = ref([]);
  const expPublish = ref({});

  const sample = ref([]);
  const sapPublish = ref({});

  const rawDataArchiveData = ref([]);

  const analysis = ref([]);
  const analysisPublish = ref([]);
  const analysisArchiveData = ref([]);

  const publishArray = ref([]);
  const publishInfo = ref({});

  let enableFastQC = ref('disable');
  let qcFinishFlag = ref(false);
  let samToolQcFinishFlag = ref(false);

  const processing = ref(false);

  onMounted(() => {
    getConfigKey('node.fastqc.status').then(response => {
      enableFastQC.value = response.msg;
    });
    // 初始化数据
    initData();
  });

  /** 初始化数据 */
  function initData() {
    loading.value = true;
    getDetailData(subNo)
      .then(response => {
        const result = response.data;
        submission.value = result.submission;
        processing.value = result.processing;

        if (
          submission.value.rejectReason &&
          submission.value.rejectReason.length !== 0
        ) {
          reject.value = submission.value.rejectReason;
        }

        project.value = result.project;

        if (project.value && project.value.projectNo) {
          const regex = /^[0-9a-z]{32}$/; // 匹配32位的UUID字符串的正则表达式

          if (regex.test(project.value.projectNo)) {
            project.value.projectNo = undefined;
          }
        }

        experiment.value = result.experiment;

        expPublish.value = result.expPublish;

        sample.value = result.sample;
        sapPublish.value = result.sapPublish;

        rawDataArchiveData.value = result.rawDataArchiveData;

        analysis.value = result.analysis;
        analysisPublish.value = result.analysisPublish;

        analysisArchiveData.value = result.analysisArchiveData;

        if (result.publish) {
          publishInfo.value = result.publish;
          publishInfo.value.typeInfoList.sort((a, b) => {
            if (
              a.creator === submission.value.creator &&
              b.creator !== submission.value.creator
            ) {
              return -1; // a排在b前面
            } else if (
              a.creator !== submission.value.creator &&
              b.creator === submission.value.creator
            ) {
              return 1; // b排在a前面
            } else {
              return 0; // 保持原有顺序
            }
          });

          publishArray.value.push(result.publish);
        }

        hotTableKey.value++;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function passSubmit() {
    if (!subNo) {
      proxy.$modal.alertWarning('提交编号不能为空');
      return;
    }

    proxy.$modal.loading('保存中，请稍候');

    pass(subNo)
      .then(response => {
        if (response.data) {
          proxy.$alert(
            "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
              response.data +
              '</div>',
            '错误',
            { dangerouslyUseHTMLString: true },
          );
        } else {
          window.location.reload();
        }
      })
      .catch(() => {
        window.location.reload();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function startReviewSubmit() {
    if (!subNo) {
      proxy.$modal.alertWarning('提交编号不能为空');
      return;
    }

    proxy.$modal.loading('修改状态中，请稍候');

    startReview(subNo)
      .then(() => {
        window.location.reload();
      })
      .catch(() => {
        window.location.reload();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const startReviewDialog = ref(false);
  const passDialog = ref(false);
  const rejectDialog = ref(false);
  const reject = ref([{ type: 'Other', reason: '' }]);

  function rejectSubmit() {
    if (!subNo) {
      proxy.$modal.alertWarning('提交编号不能为空');
      return;
    }

    if (
      reject.value[0].type.trim() === '' ||
      reject.value[0].reason.trim() === ''
    ) {
      proxy.$modal.alertWarning('驳回类型和原因为必填项');
      return;
    }

    proxy.$modal.loading('保存中，请稍候');

    const rejectDTO = {
      subNo: subNo,
      reason: reject.value,
    };

    rejectSubmission(rejectDTO)
      .then(() => {
        window.location.reload();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  function showDetail(type, no) {
    proxy.$modal.loading('打开中，请稍候');
    // 预先生成access_token
    createAccessToken({ memberId: submission.value.creator })
      .then(response => {
        const token = response.data;
        let href = `${
          import.meta.env.VITE_APP_WEB_URL
        }/${type}/detail/${no}?access-token=${token}`;
        proxy.$modal.closeLoading();
        // 打开一个新页面
        window.open(href);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  const addReject = () => {
    reject.value.push({ type: 'Other', reason: '' });
  };

  const removeReject = index => {
    reject.value.splice(index, 1);
  };

  const archivingColumns = ref([
    'experiment_id',
    'experiment_name',
    'sample_id',
    'sample_name',
    'run_id',
    'run_name',
    'run_description',
    'data_id',
    'file_name',
    'file_type',
    'file_size',
    'upload_date',
    'file_path',
    'data_remark',
  ]);

  const analysisColumns = ref([
    'analysis_id',
    'analysis_name',
    'description',
    'analysis_type',
    'other_analysis_type',
    'index',
    'program',
    'link',
    'version',
    'note',
    'output_file',
    'target_project',
    'target_experiment',
    'target_sample',
    'target_analysis',
    'target_run',
    'target_data',
    'target_other_name',
    'target_other_link',
  ]);

  const analysisArchivingColumns = ref([
    'analysis_id',
    'analysis_name',
    'data_id',
    'file_name',
    'file_type',
    'file_size',
    'upload_date',
    'file_path',
    'data_remark',
  ]);

  const computedBgClass = computed(() => {
    if (!submission.value.status) {
      return 'editing-bg';
    }
    return `${submission.value.status.toLowerCase()}-bg`;
  });

  const statusName = computed(() => {
    if (!submission.value.status) {
      return '';
    }
    if (submission.value.status === 'waiting') {
      return `等待审核`;
    }
    if (submission.value.status === 'rejected') {
      return `已驳回`;
    }
    if (submission.value.status === 'reviewing') {
      return `审核中`;
    }
    if (submission.value.status === 'complete') {
      return `已完成`;
    }
    // 首字母大写
    return (
      submission.value.status.substring(0, 1).toUpperCase() +
      submission.value.status.substring(1)
    );
  });

  const linearBg = computed(() => {
    if (!submission.value.status) {
      return 'editing-linear-bg';
    }
    return `${submission.value.status.toLowerCase()}-linear-bg`;
  });

  function goBack() {
    window.history.back();
  }
</script>

<style lang="scss" scoped>
  .submission {
    padding: 20px 0 25px 0;
  }

  .main-page-container {
    margin-top: -50px;
  }

  :deep(.el-popper.is-dark) {
    max-width: 30% !important;
  }

  :deep(.el-alert__description) {
    margin-top: 0;
  }

  :deep(.el-textarea__inner) {
    border-radius: 12px;
  }

  :deep(.el-alert__description) {
    margin-top: 0;
  }

  :deep(.el-dialog__body) {
    padding: 0 20px;
  }

  .fail-label {
    display: inline-block;
    width: 130px;
  }

  :deep(.passDialog .el-dialog__title) {
    font-size: 20px !important;
  }

  strong {
    font-weight: bold;
  }
</style>
<style lang="scss">
  strong {
    font-weight: bold !important;
  }
</style>
