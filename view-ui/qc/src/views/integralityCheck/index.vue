<template>
  <div class="app-container">
    <div class="card list">
      <el-row>
        <el-col :span="24" justify="space-between">
          <el-form ref="formRef" :model="queryParams" :inline="true">
            <el-form-item label="创建者" prop="creatorEmail">
              <el-input
                v-model="queryParams.creatorEmail"
                style="width: 240px"
                placeholder="请输入创建者邮箱"
                clearable
                @keyup.enter="getDataList"
              ></el-input>
            </el-form-item>
            <el-form-item label="类型" prop="type">
              <el-select
                v-model="queryParams.type"
                clearable
                placeholder="请选择类型"
                style="width: 180px"
                @change="getDataList"
              >
                <el-option value="project" label="项目"></el-option>
                <el-option value="experiment" label="实验"></el-option>
                <el-option value="sample" label="样本"></el-option>
                <el-option value="run" label="批次"></el-option>
                <el-option value="data" label="数据"></el-option>
                <el-option value="analysis" label="分析"></el-option>
                <el-option value="share" label="共享"></el-option>
                <el-option value="review" label="审核"></el-option>
                <el-option value="publish" label="发布"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="类型编号" prop="typeNos">
              <el-input
                v-model="queryParams.typeNos"
                type="textarea"
                rows="1"
                style="width: 180px"
                placeholder="请输入类型编号"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="主题类型" prop="subjectType">
              <el-select
                v-model="queryParams.subjectType"
                clearable
                style="width: 180px"
                :teleported="false"
                placeholder="请选择主题类型"
                @change="getDataList"
              >
                <el-option
                  v-for="(item, index) in subjectTypes"
                  :key="index"
                  style="width: 180px"
                  :value="item"
                  :label="item"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="标签" prop="tag">
              <el-select
                v-model="queryParams.tags"
                clearable
                style="width: 180px"
                :teleported="false"
                placeholder="请选择标签"
                @change="getDataList"
              >
                <el-option
                  v-for="dict in tag"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-checkbox
                v-model="queryParams.hasFilterField"
                value="true"
                label="过滤字段"
              >
                <template #default>
                  <el-tooltip
                    effect="dark"
                    raw-content
                    content="如果您填写了过滤字段并勾选复选框，当字段等于过滤字段时数据将被过滤。<br>
                    如果您填写了过滤字段但未勾选复选框，当字段不等于过滤字段时数据将被过滤。"
                  >
                    <span class="font-700">过滤字段</span>
                  </el-tooltip>
                  <el-input
                    v-model="queryParams.filterField"
                    class="ml-1"
                    style="width: 180px"
                    placeholder="请输入过滤字段"
                  />
                </template>
              </el-checkbox>
            </el-form-item>
            <el-form-item label="创建日期">
              <el-date-picker
                v-model="dateRange"
                value-format="YYYY-MM-DD"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 240px"
              ></el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="getDataList"
                >搜索
              </el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row class="mb-1">
        <el-col :span="4">
          <el-button type="warning" plain @click="handleDbCheck"
            >完整性检查
          </el-button>
          <el-button type="info" plain @click="handleExport">导出</el-button>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="dbId" label="ID" width="250" />
        <el-table-column prop="typeId" label="编号" width="150" />
        <el-table-column prop="type" label="类型" width="150" />
        <el-table-column prop="subjectType" label="主题类型" width="150" />
        <el-table-column prop="tags" label="标签" width="200" />
        <el-table-column prop="field" label="字段" width="200" />
        <el-table-column
          prop="createDate"
          label="创建日期"
          min-width="140"
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="message" label="描述" min-width="250" />
        <el-table-column prop="creatorEmail" label="创建者" min-width="200" />
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { checkAllDB, checkLogList } from '@/api/dataIntegralityCheck';
  import { getAllNames } from '@/api/standard/expMg';
  import { isStrBlank } from '@/utils';

  let subjectTypes = ref([]);

  onMounted(() => {
    getAllNames().then(res => {
      subjectTypes.value = res.data;
    });
    getDataList();
  });

  const { proxy } = getCurrentInstance();

  const { tag } = proxy.useDict('tag');

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      creatorEmail: '',
      type: '',
      subjectType: '',
      tags: '',
      hasFilterField: false,
      filterField: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'createDate',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  function resetQuery() {
    dateRange.value = [];
    proxy.$refs['formRef'].resetFields();

    getDataList();
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function getDataList() {
    if (
      queryParams.value.hasFilterField &&
      isStrBlank(queryParams.value.filterField)
    ) {
      proxy.$modal.msgError('请输入过滤字段');
      return;
    }

    loading.value = true;
    checkLogList(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function handleDbCheck() {
    proxy.$modal
      .confirm('确定要进行完整性检查吗？这可能需要很长时间')
      .then(() => {
        checkAllDB().then(() => {
          proxy.$modal.msgSuccess('完整性检查中，请稍候...');
        });
      });
  }

  function handleExport() {
    proxy.download(
      '/qc/dataIntegralityCheck/export',
      queryParams.value,
      `db_check_log_${new Date().getTime()}.xlsx`,
    );
  }
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-select,
    .el-input {
      width: 330px;
    }
  }

  :deep(.el-textarea__inner) {
    border-radius: 12px;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
