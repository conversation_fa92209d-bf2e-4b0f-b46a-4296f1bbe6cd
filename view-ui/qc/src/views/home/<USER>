<template>
  <el-row :gutter="20" class="mt-1-5">
    <el-col :span="14" :xs="12">
      <span class="title">质量控制日志</span>
    </el-col>
    <el-col :span="10" :xs="12">
      <DateQuery @query="getData"></DateQuery>
    </el-col>
  </el-row>

  <!--操作统计-->
  <el-row :gutter="20" class="mt-1">
    <el-col :span="10" :xs="24">
      <div class="card h-100 pt-20">
        <el-row :gutter="20" class="row-gap-20">
          <el-col :span="8" :xs="24">
            <LogData
              icon="proj"
              name="项目"
              :total="dataResult.projNum"
              :count="dataResult.projSuccessNum"
            ></LogData>
          </el-col>
          <el-col :span="8" :xs="24">
            <LogData
              icon="exp"
              name="实验"
              :total="dataResult.expNum"
              :count="dataResult.expSuccessNum"
            ></LogData>
          </el-col>
          <el-col :span="8" :xs="24">
            <LogData
              icon="samp"
              name="样本"
              :total="dataResult.sapNum"
              :count="dataResult.sapSuccessNum"
            ></LogData>
          </el-col>
          <el-col :span="8" :xs="24">
            <LogData
              icon="anal"
              name="分析"
              :total="dataResult.analNum"
              :data="dataResult.analData"
              :size="dataResult.analDataSizeStr"
              :count="dataResult.analSuccessNum"
            ></LogData>
          </el-col>
          <el-col :span="8" :xs="24">
            <LogData
              icon="run"
              name="批次"
              :total="dataResult.runNum"
              :data="dataResult.rawData"
              :size="dataResult.rawDataSizeStr"
              :count="dataResult.runSuccessNum"
            ></LogData>
          </el-col>
          <el-col :span="8" :xs="24">
            <LogData
              icon="publish"
              name="文献"
              :total="dataResult.publish"
              :count="dataResult.publishSuccess"
            ></LogData>
          </el-col>
        </el-row>
      </div>
    </el-col>
    <el-col :span="7" :xs="24">
      <div class="card">
        <h3>操作员统计</h3>
        <el-divider class="mt-1"> </el-divider>
        <div v-if="!loading" class="d-flex">
          <PieChart
            id="operatorPie"
            width="50%"
            height="170px"
            :color="operatorColor"
            :data="operatorData"
            title="操作员"
          ></PieChart>
          <div class="w-50">
            <div
              v-for="(item, idx) in dataResult.topQcOperator"
              :key="'topQcOperator-' + idx"
            >
              <template v-if="idx < 3">
                <el-divider v-if="idx !== 0" class="mt-03 mb-0"></el-divider>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 one"
                    >{{
                      ((item.value * 100) / dataResult.qcOperatorTotal).toFixed(
                        2,
                      )
                    }}%</span
                  >
                  <span>{{ item.value }}</span>
                </div>
                <div class="text-other-color ml-1">{{ item.name }}</div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="7" :xs="24">
      <div class="card">
        <h3>操作内容统计</h3>
        <el-divider class="mt-1"> </el-divider>
        <div v-if="!loading" class="d-flex">
          <PieChart
            id="pie4"
            width="50%"
            height="170px"
            :color="operatorColor"
            :data="operationChartData"
            :title="'操作\n\n内容'"
          ></PieChart>
          <div class="w-50">
            <div v-if="operationData.pass && operationData.pass !== 0">
              <div class="d-flex justify-space-between">
                <span class="before-circle font-600 one"
                  >{{
                    (
                      (operationData.pass * 100) /
                      (operationData.pass +
                        operationData.reject +
                        operationData.startReview)
                    ).toFixed(2)
                  }}%</span
                >
                <span>{{ operationData.pass }}</span>
              </div>
              <div class="text-other-color ml-1">通过</div>
            </div>
            <el-divider
              v-if="operationData.reject && operationData.reject !== 0"
              class="mt-03 mb-0"
            ></el-divider>
            <div v-if="operationData.reject && operationData.reject !== 0">
              <div class="d-flex justify-space-between">
                <span class="before-circle font-600 two"
                  >{{
                    (
                      (operationData.reject * 100) /
                      (operationData.pass +
                        operationData.reject +
                        operationData.startReview)
                    ).toFixed(2)
                  }}%</span
                >
                <span>{{ operationData.reject }}</span>
              </div>
              <div class="text-other-color ml-1">拒绝</div>
            </div>
            <el-divider
              v-if="
                operationData.startReview && operationData.startReview !== 0
              "
              class="mt-03 mb-0"
            ></el-divider>
            <div
              v-if="
                operationData.startReview && operationData.startReview !== 0
              "
            >
              <div class="d-flex justify-space-between">
                <span class="before-circle font-600 two"
                  >{{
                    (
                      (operationData.startReview * 100) /
                      (operationData.pass +
                        operationData.reject +
                        operationData.startReview)
                    ).toFixed(2)
                  }}%</span
                >
                <span>{{ operationData.startReview }}</span>
              </div>
              <div class="text-other-color ml-1">开始审核</div>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import { getQualityLog } from '@/api/statistics';
  import DateQuery from '@/views/home/<USER>/DateQuery.vue';
  import PieChart from '@/views/home/<USER>/pieChart.vue';
  import LogData from '@/views/home/<USER>/logData.vue';

  const { proxy } = getCurrentInstance();

  const loading = ref(false);
  const dataResult = ref({});

  function getData(dateRange) {
    loading.value = true;
    getQualityLog(proxy.addDateRange(null, dateRange))
      .then(response => {
        dataResult.value = response.data;

        operatorData.value = dataResult.value.topQcOperator;

        operationData.value.pass = response.data.pass;
        operationData.value.reject = response.data.reject;
        operationData.value.startReview = response.data.startReview;

        operationChartData.value[0].value = response.data.pass;
        operationChartData.value[1].value = response.data.reject;
        operationChartData.value[2].value = response.data.startReview;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const operatorColor = ref(['#768FC7', '#88AFD9', '#8DCCDE']);
  const operatorData = ref([]);

  const operationData = ref({
    pass: 0,
    reject: 0,
    startReview: 0,
  });
  const operationChartData = ref([
    { value: 0, name: '通过' },
    { value: 0, name: '拒绝' },
    { value: 0, name: '开始审核' },
  ]);

  onMounted(() => {
    getData();
  });
</script>

<style scoped lang="scss">
  .title {
    color: #333333;
    font-weight: 600;
    padding-left: 15px;
    font-size: 16px;
    border-left: 3px solid #3a78e8;
  }
</style>
