<template>
  <el-row :gutter="20" class="mb-05">
    <el-col :span="14" :xs="24">
      <span class="title">Data Submission Volume</span>
    </el-col>
    <el-col :span="10" :xs="24">
      <DateQuery @query="getData"></DateQuery>
    </el-col>
  </el-row>

  <el-row v-loading="loading" :gutter="20">
    <el-col :span="3" :xs="12">
      <SubmitVol title="Submission" :count="result.subNum"></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol title="Project" :count="result.projNum"></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol title="Experiment" :count="result.expNum"></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol title="Sample" :count="result.sapNum"></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol title="Run" :count="result.runNum"></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol
        title="Data"
        :count="result.dataNum"
        :size="result.dataSize"
      ></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol title="Analysis" :count="result.analNum"></SubmitVol>
    </el-col>
    <el-col :span="3" :xs="12">
      <SubmitVol title="Publish" :count="result.publish"></SubmitVol>
    </el-col>
  </el-row>
</template>

<script setup>
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import SubmitVol from '@/views/home/<USER>/submitVol.vue';
  import DateQuery from '@/views/home/<USER>/DateQuery.vue';
  import { getDataVolume } from '@/api/statistics';

  const { proxy } = getCurrentInstance();

  const loading = ref(false);

  const result = ref({});

  function getData(dateRange) {
    loading.value = true;
    getDataVolume(proxy.addDateRange(null, dateRange))
      .then(response => {
        result.value = response.data;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  onMounted(() => {
    getData();
  });
</script>

<style scoped lang="scss">
  .title {
    color: #333333;
    font-weight: 600;
    padding-left: 15px;
    font-size: 16px;
    border-left: 3px solid #3a78e8;
  }
</style>
