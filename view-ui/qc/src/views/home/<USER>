<template>
  <el-row :gutter="20" class="mb-05 mt-1-5">
    <el-col :span="14" :xs="12">
      <span class="title">Audit Overview</span>
    </el-col>
    <el-col :span="10" :xs="12">
      <DateQuery @query="getData"></DateQuery>
    </el-col>
  </el-row>
  <el-row v-loading="overviewLoading" :gutter="20">
    <el-col :span="8" :xs="24">
      <AuditOverview
        title="Submission Review Times"
        :count="overviewResult.subReviews"
        count-tip="审核行为数（Audit Log数）"
        total-tip="审核行为数（Audit Log数） + Waiting Review状态的Submission记录数"
        :total="overviewResult.subTotal"
        unit-one="reviews"
        unit-two="total"
      ></AuditOverview>
    </el-col>
    <el-col :span="8" :xs="24">
      <AuditOverview
        title="Implementation Audit Frequency"
        count-tip="审核涉及的各类型数据之和（Audit Log的total之和）"
        total-tip="审核涉及的各类型数据之和 + Waiting Review状态的Submission中各类型数据之和"
        :count="overviewResult.frequencyItem"
        :total="overviewResult.frequencyTotal"
        unit-one="items"
        unit-two="total"
      ></AuditOverview>
    </el-col>
    <el-col :span="8" :xs="24">
      <AuditOverview
        title="Success Submission"
        count-tip="Submission是Complete状态的记录数"
        total-tip="Submission是Editing, Waiting review, Rejected, and Complete的记录数"
        :count="overviewResult.subSuccess"
        :total="overviewResult.submissionTotal"
        unit-one="success"
        unit-two="submission"
      ></AuditOverview>
    </el-col>
  </el-row>

  <!--Audit Data-->
  <el-row :gutter="20" class="mb-05 mt-1-5">
    <el-col :span="17" :xs="24">
      <div class="card h-100">
        <div class="d-flex justify-space-between align-items-center">
          <h3>Audit Detail</h3>
          <div>
            <el-button
              icon="Download"
              class="ml-1"
              size="default"
              type="primary"
              plain
              @click="exportData"
              >Export</el-button
            >
          </div>
        </div>
        <el-divider class="mb-1 mt-1"></el-divider>
        <div class="bg-gray radius-8">
          <div id="auditDetail" style="width: 100%; height: 455px"></div>
        </div>
      </div>
    </el-col>
    <el-col :span="7" :xs="24">
      <div class="card">
        <h3 class="audit-data">Audit Data</h3>
        <el-divider class="mt-1"></el-divider>
        <div id="auditData" style="width: 100%; height: 180px"></div>
        <div class="d-flex justify-space-around mt-1">
          <div>
            <div class="before-circle success text-success font-17 font-600">
              SUCCESS
            </div>
            <div class="ml-1">
              <span v-if="topDataResult.success !== 0" class="font-600 font-18"
                >{{
                  (
                    (topDataResult.success * 100) /
                    (topDataResult.success + topDataResult.fail)
                  ).toFixed(0)
                }}%</span
              >
              <span class="text-other-color ml-1">{{
                topDataResult.success
              }}</span>
            </div>
          </div>
          <div>
            <div class="before-circle fail text-warning font-17 font-600">
              FAIL
            </div>
            <div class="ml-1">
              <span v-if="topDataResult.fail !== 0" class="font-600 font-18"
                >{{
                  (
                    (topDataResult.fail * 100) /
                    (topDataResult.success + topDataResult.fail)
                  ).toFixed(0)
                }}%</span
              >
              <span class="text-other-color ml-1">{{
                topDataResult.fail
              }}</span>
            </div>
          </div>
        </div>
        <el-divider class="mb-1 mt-1"></el-divider>
        <div class="d-flex">
          <PieChart
            v-if="topDataLoading"
            id="failReason"
            width="50%"
            height="175px"
            :color="failColor"
            :data="failData"
            :title="'Failure\n\nReason'"
          ></PieChart>
          <div class="w-50">
            <div
              v-for="(item, idx) in topDataResult.topReason"
              :key="'topDataResult-' + idx"
            >
              <template v-if="idx < 3">
                <el-divider v-if="idx !== 0" class="mt-03 mb-0"></el-divider>
                <div class="d-flex justify-space-between">
                  <span class="before-circle font-600 top1"
                    >Top {{ idx + 1 }}</span
                  >
                  <span
                    >{{
                      (
                        (item.value * 100) /
                        topDataResult.rejectReasonTotal
                      ).toFixed(0)
                    }}%</span
                  >
                </div>
                <div class="text-other-color ml-1">{{ item.name }}</div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script setup>
  import { getCurrentInstance, nextTick, onMounted, ref } from 'vue';
  import DateQuery from '@/views/home/<USER>/DateQuery.vue';
  import {
    getAuditData,
    getAuditDetailData,
    getAuditOverview,
  } from '@/api/statistics';
  import AuditOverview from '@/views/home/<USER>/auditOverview.vue';
  import PieChart from '@/views/home/<USER>/pieChart.vue';
  import * as echarts from 'echarts';

  const { proxy } = getCurrentInstance();

  const overviewLoading = ref(false);
  const overviewResult = ref({});

  function getOverviewData(dateRange) {
    overviewLoading.value = true;
    getAuditOverview(proxy.addDateRange(null, dateRange))
      .then(response => {
        overviewResult.value = response.data;
      })
      .finally(() => {
        overviewLoading.value = false;
      });
  }

  const topDataLoading = ref(false);
  const topDataResult = ref({});

  function getAuditTopData(dateRange) {
    topDataLoading.value = false;
    getAuditData(proxy.addDateRange(null, dateRange)).then(response => {
      topDataResult.value = response.data;

      failData.value = topDataResult.value.topReason;

      topDataLoading.value = true;

      nextTick(() => {
        initEcharts(response.data);
      });
    });
  }

  function getData(dateRange) {
    getOverviewData(dateRange);
    initBarCharts(dateRange);
    getAuditTopData(dateRange);
  }

  const failColor = ref(['#E98BAD', '#EDA2BD', '#F2B9CE', '#F6D0DE']);
  const failData = ref([]);

  let auditDetail = null;
  const initBarCharts = dateRange => {
    // 堆积柱形图
    auditDetail = echarts.init(document.getElementById('auditDetail'));

    auditDetail.showLoading(); // 获取数据前显示加载动画

    getAuditDetailData(proxy.addDateRange(null, dateRange)).then(response => {
      const inputData = response.data;

      // 提取所有人员的列表
      const allNames = Array.from(
        new Set(
          inputData.flatMap(item => item.results.map(result => result.name)),
        ),
      );

      // 构造堆积柱形图的数据
      const seriesData = allNames.map(name => {
        return {
          name,
          type: 'bar',
          stack: 'stack',
          barMaxWidth: 22,
          data: inputData.map(item => {
            const result = item.results.find(result => result.name === name);
            return result ? result.reviews : 0;
          }),
        };
      });

      // 设置堆积柱形图的配置项
      const option = {
        color: [
          '#2ADAC9',
          '#3A78E8',
          '#FEA52B',
          '#A296B1',
          '#b26cc2',
          '#F4DE93',
          '#FFBDBB',
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            const dataIndex = params[0].dataIndex;
            const month = inputData[dataIndex].month;
            let tooltip = month + ': (success / reviews: items)<br/><hr>';
            params.forEach(param => {
              const name = param.seriesName;
              const result = inputData[dataIndex].results.find(
                result => result.name === name,
              );
              if (result) {
                const success = result.success;
                const reviews = result.reviews;
                const items = result.items;
                tooltip += `<span class="tooltip-label">${name}: </span>${success} / ${reviews} : ${items} <br/>`;
              }
            });
            return tooltip;
          },
        },
        legend: {
          data: allNames,
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            show: inputData.length > 12,
            end: 100,
          },
          {
            start: 0,
            show: inputData.length > 12,
            end: 100,
          },
        ],
        grid: {
          top: '8%',
          left: '3%',
          right: '4%',
          bottom: inputData.length > 12 ? '13%' : '5%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: inputData.map(item => item.month),
        },
        yAxis: [
          {
            type: 'value',
            axisLine: {
              show: true,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: seriesData,
      };

      auditDetail.hideLoading(); // 获取数据后隐藏加载动画
      auditDetail.setOption(option);
    });
  };

  function exportData() {
    proxy.download(
      `/qc/statistics/exportAuditDetailData`,
      {},
      `Audit Detail Data.csv`,
    );
  }

  const initEcharts = inputData => {
    // 双圆环
    const auditData = echarts.init(document.getElementById('auditData'));
    if (!auditData) return;

    const color1 = {
      Submission: '#EBACAA',
      Project: '#E98BAD',
      Experiment: '#A296B1',
      Sample: '#768FC7',
      Analysis: '#9BCF99',
      Publish: '#F4DE93',
    };
    const option1 = {
      color: ['#FE7F2B', '#07BCB4'],
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        icon: 'circle',
        top: 'middle',
        orient: 'vertical',
        itemHeight: 6,
        itemWidth: 6,
        align: 'left',
        right: '10%',
        data: [
          'Submission',
          'Project',
          'Experiment',
          'Sample',
          'Analysis',
          'Publish',
        ],
        textStyle: {
          fontSize: 14,
        },
      },
      series: [
        {
          type: 'pie',
          selectedMode: 'single',
          radius: [0, '60%'],
          center: ['35%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: inputData.fail, name: 'Fail' },
            { value: inputData.success, name: 'Success' },
          ],
        },
        {
          type: 'pie',
          center: ['35%', '50%'],
          radius: ['80%', '100%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            { value: inputData.publish, name: 'Publish' },
            { value: inputData.analNum, name: 'Analysis' },
            { value: inputData.sapNum, name: 'Sample' },
            { value: inputData.expNum, name: 'Experiment' },
            { value: inputData.projNum, name: 'Project' },
            { value: inputData.subNum, name: 'Submission' },
          ],
          itemStyle: {
            color: function (params) {
              return color1[params.name];
            },
          },
        },
      ],
    };
    auditData.setOption(option1);

    window.onresize = function () {
      auditData.resize();
      auditDetail.resize();
    };
  };

  onMounted(() => {
    getData();
  });
</script>

<style scoped lang="scss">
  .title {
    color: #333333;
    font-weight: 600;
    padding-left: 15px;
    font-size: 16px;
    border-left: 3px solid #3a78e8;
  }
  :deep(.tooltip-label) {
    text-align: right;
    margin-right: 0.5rem;
    display: inline-block;
    width: 120px;
  }
</style>
