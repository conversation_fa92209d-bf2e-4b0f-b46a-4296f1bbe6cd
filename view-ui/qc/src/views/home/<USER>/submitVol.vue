<template>
  <div class="p-10-15 radius-14 h-100 bg-white">
    <div class="before-circle" :class="circleColor(title)">
      {{ title }}
      <span v-if="title === '数据'" class="text-warning ml-05">{{ size }}</span>
    </div>
    <span class="text-main-color font-600 font-28 ml-1">{{
      formatNumber(count)
    }}</span>
    <span class="text-other-color ml-05">项</span>
  </div>
</template>

<script setup name="pieChart">
  import { defineProps } from 'vue';
  import { formatNumber } from '@/utils';
  defineProps({
    title: {
      type: String,
    },
    count: {
      type: Number,
    },
    size: {
      type: String,
      required: false,
    },
  });
  const circleColor = title => {
    return `${title}-circle`;
  };
</script>

<style scoped lang="scss">
  .before-circle:before {
    width: 7px;
    height: 7px;
  }
  .Submission-circle:before,
  .提交-circle:before {
    background-color: #ebacaa;
  }
  .Project-circle:before,
  .项目-circle:before {
    background-color: #e98bad;
  }
  .Experiment-circle:before,
  .实验-circle:before {
    background-color: #a296b1;
  }
  .Sample-circle:before,
  .样本-circle:before {
    background-color: #768fc7;
  }
  .Run-circle:before,
  .批次-circle:before {
    background-color: #88afd9;
  }
  .Data-circle:before,
  .数据-circle:before {
    background-color: #8dccde;
  }
  .Analysis-circle:before,
  .分析-circle:before {
    background-color: #9bcf99;
  }
  .Publish-circle:before,
  .文献-circle:before {
    background-color: #f4de93;
  }
</style>
