<template>
  <div class="d-flex align-items-center">
    <el-radio-group
      v-model="dateSelect"
      text-color="#1981F4"
      fill="#ffffff"
      @change="changeDateSelect"
    >
      <el-radio-button label="All" />
      <el-radio-button label="Today" class="ml-1" />
      <el-radio-button label="This Month" class="ml-1" />
    </el-radio-group>
    <el-date-picker
      v-model="dateRange"
      value-format="YYYY-MM-DD"
      type="daterange"
      range-separator="To"
      start-placeholder="Start date"
      end-placeholder="End date"
      style="width: 200px"
      class="ml-1"
      @visible-change="changeDate"
    />
  </div>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue';
  const { proxy } = getCurrentInstance();

  const dateSelect = ref('');

  const dateRange = ref([]);

  function changeDate(visibility) {
    // 弹出的选择器关闭时再查询
    if (!visibility) {
      dateSelect.value = '';
      queryData();
    }
  }

  function changeDateSelect() {
    // 获取当前日期
    const currentDate = new Date();

    // 构建日期字符串（YYYY-MM-DD）
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;

    if (dateSelect.value === 'All') {
      dateRange.value = [];
    }
    if (dateSelect.value === 'Today') {
      // 构建包含当日日期的数组
      dateRange.value = [dateString, dateString];
    }
    if (dateSelect.value === 'This Month') {
      // 获取上个月的今天
      const lastMonthToday = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth() - 1,
        currentDate.getDate(),
      );

      // 构建包含当日日期的数组
      dateRange.value = [lastMonthToday.toISOString().slice(0, 10), dateString];
    }
    queryData();
  }

  function queryData() {
    proxy.$emit('query', dateRange.value);
  }
</script>

<style scoped lang="scss">
  .title {
    color: #333333;
    font-weight: 600;
    padding-left: 15px;
    font-size: 16px;
    border-left: 3px solid #3a78e8;
  }
</style>
