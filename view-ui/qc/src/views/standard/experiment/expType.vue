<template>
  <el-tooltip v-if="fullName" placement="top" :content="fullName">
    <div class="experiment-type" :style="{ backgroundColor: color }">
      {{ type === null || type === '' || type === undefined ? 'O' : type }}
    </div>
  </el-tooltip>
  <div
    v-else
    class="experiment-type exptype-small"
    :style="{ backgroundColor: color }"
  >
    {{ type }}
  </div>
</template>

<script setup>
  import { defineProps } from 'vue';

  const props = defineProps({
    type: {
      type: String,
      default: () => 'O',
    },
    color: {
      type: String,
      default: () => '#808080',
    },
    fullName: {
      type: String,
      default: () => '其他',
    },
  });
</script>

<style lang="scss" scoped>
  .experiment-type {
    //padding: 15px;
    width: 25px;
    height: 25px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-weight: 600;
    border-radius: 50%;
    margin-right: 6px;
    background-color: #808080;

    &:hover {
      cursor: pointer;
    }
  }

  .exptype-small {
    width: 22px !important;
    height: 22px !important;
    font-size: 12px !important;
  }
</style>
