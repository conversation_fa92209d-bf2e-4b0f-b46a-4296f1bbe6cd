<template>
  <div class="app-container">
    <div v-loading="loadingFlag" class="card list">
      <el-form
        v-show="showSearch"
        ref="searchFormRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item :label="getTypeTitle()">
          <el-input
            v-model="queryParams.name"
            style="width: 250px"
            @keydown.enter="initList"
          ></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="queryParams.status"
            style="width: 250px"
            placeholder="选择"
            clearable
          >
            <el-option
              v-for="(item, index) in statusListRef"
              :key="`status_opt_${index}`"
              :value="item.value"
              :label="getStatusLabel(item.value)"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="创建时间">
          <el-date-picker
            v-model="auditTime"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          ></el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="initList"
            >搜索
          </el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['standard:experiment:add']"
            type="primary"
            plain
            icon="Plus"
            @click="async () => await handleAdd()"
            >新增
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport"
            >导出标准数据集
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="initList"
        ></right-toolbar>
      </el-row>

      <el-table
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        height="76vh"
      >
        <el-table-column
          prop="name"
          :label="getTypeTitle()"
          min-width="220"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column
          prop="version"
          label="版本"
          min-width="90"
          show-overflow-tooltip
        />
        <el-table-column
          prop="description"
          label="描述"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          v-if="standType === 'experiment'"
          prop="icon"
          label="图标"
          width="53"
        >
          <template #default="scope">
            <ExpType
              :type="scope.row.iconName"
              :color="scope.row.iconColor"
              :full-name="scope.row.name"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="templateName"
          label="模板文件"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          prop="exampleName"
          label="示例文件"
          min-width="220"
          show-overflow-tooltip
        />

        <el-table-column prop="recommendNum" label="推荐数量" width="150">
          <template #default="scope">
            <span>{{ scope.row.recommendNum }}</span>
            <span class="ml-05">{{ `${initRate(scope.row)}` }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="70" />
        <el-table-column prop="status" label="状态" width="70">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="enable"
              inactive-value="disable"
              @change="val => doChangeStatus(scope.row, val)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="160">
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          fixed="right"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="编辑">
              <svg-icon
                icon-class="edits"
                class-name="stand-svg"
                @click="async () => await handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="属性">
              <svg-icon
                icon-class="attr"
                class-name="stand-svg"
                @click="toAttr(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <!--<pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
      />-->
    </div>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" width="940px" class="exp-dialog">
      <el-form
        ref="expRef"
        v-loading="loadingFlag"
        :model="form"
        label-width="155px"
        :inline="true"
        :rules="rules"
      >
        <el-form-item v-if="sampleFlag" label="父类型" class="w-100">
          <el-select
            v-model="form.parentName"
            placeholder="选择"
            clearable
            style="width: 635px"
          >
            <el-option
              v-for="(item, index) in parentListRef"
              :key="`pare_attr_${index}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item prop="name">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700" v-text="getTypeTitle()"></span>
              <el-tooltip
                placement="top"
                content="只能包含数字、字母、空格、'-'和'_'"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.name" :disabled="licenseStatus"></el-input>
        </el-form-item>

        <el-form-item prop="version">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">版本</span>
              <el-tooltip
                placement="top"
                content="只能包含数字和'.'，例如：1.2, 2.0.56"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input v-model="form.version"></el-input>
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            style="width: 635px"
          />
        </el-form-item>

        <el-form-item label="中文描述">
          <el-input
            v-model="form.zhDescription"
            type="textarea"
            :rows="3"
            style="width: 635px"
          />
        </el-form-item>

        <el-form-item label="推荐数量" prop="recommendNum">
          <el-input-number
            v-model="form.recommendNum"
            :min="0"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item label="推荐提示">
          <el-input v-model="form.recommendTip" />
        </el-form-item>

        <el-form-item>
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">描述信息 </span>
              <el-tooltip
                placement="top"
                content="控制NODE数据提交中基本信息描述字段的显示规则"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-radio-group v-model="form.desc">
            <el-radio
              v-for="(item, index) in attrTypeListRef"
              :key="`des_attr_${index}`"
              :value="item.value"
              :label="getStatusLabel(item.value)"
            />
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">协议信息</span>
              <el-tooltip
                placement="top"
                content="控制NODE数据提交中基本信息协议字段的显示规则"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-radio-group v-model="form.protocol">
            <el-radio
              v-for="(item, index) in attrTypeListRef"
              :key="`prot_attr_${index}`"
              :value="item.value"
              :label="getStatusLabel(item.value)"
            />
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="sampleFlag">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">物种</span>
              <el-tooltip
                placement="top"
                content="控制NODE数据提交中基本信息物种字段的显示规则"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-radio-group v-model="form.organism">
            <el-radio
              v-for="(item, index) in attrTypeListRef"
              :key="`orga_attr_${index}`"
              :value="item.value"
              :label="getStatusLabel(item.value)"
            />
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="sampleFlag">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">组织</span>
              <el-tooltip
                placement="top"
                content="控制NODE数据提交中基本信息组织字段的显示规则"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-radio-group v-model="form.tissue">
            <el-radio
              v-for="(item, index) in attrTypeListRef"
              :key="`tissue_attr_${index}`"
              :value="item.value"
              :label="getStatusLabel(item.value)"
            />
          </el-radio-group>
        </el-form-item>

        <el-form-item label="状态">
          <el-radio-group
            v-model="form.status"
            :disabled="addFlag"
            style="width: 230px"
          >
            <el-radio
              v-for="(item, index) in statusListRef"
              :key="`status_ra_${index}`"
              :value="item.value"
              :label="getStatusLabel(item.value)"
            />
          </el-radio-group>
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="1"
            controls-position="right"
          />
        </el-form-item>

        <el-form-item
          v-if="standType === 'experiment'"
          label="图标名称"
          prop="iconName"
        >
          <el-input
            v-model="form.iconName"
            placeholder="1到2个大写字母"
            minlength="1"
            maxlength="2"
          ></el-input>
        </el-form-item>

        <el-form-item
          v-if="standType === 'experiment'"
          label="图标颜色"
          prop="iconColor"
        >
          <el-color-picker
            v-model="form.iconColor"
            style="width: 100px"
          ></el-color-picker>
        </el-form-item>

        <el-form-item label="模板文件" prop="templateName">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">模板文件</span>
              <el-tooltip
                placement="top"
                :content="`文件名包含'${getTypeTitle()}'和'版本'号等数据`"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-upload
            ref="uploadTempRef"
            :key="'uploadTempKey'"
            :limit="1"
            :accept="uploadTemp.fileType.join(',')"
            :headers="uploadTemp.headers"
            :action="`${uploadTemp.url}?id=${currRowId}&name=${form.name}&version=${form.version}&type=${standType}`"
            :disabled="uploadTemp.isUploading"
            :before-upload="checkBeforeTempUpload"
            :on-progress="() => handleFileUploadProgress(false)"
            :on-success="
              (response, file, fileList) =>
                handleFileSuccess(response, file, fileList, false)
            "
            :auto-upload="true"
            :show-file-list="false"
            drag
            style="width: 635px"
            class="upload-demo"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                已上传文件:
                <a
                  style="color: var(--el-color-primary)"
                  href="javascript:void(0);"
                  @click="downloadExampleExcel(form.templateName)"
                >
                  {{ form.templateName }}
                </a>
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item label="示例文件" prop="exampleName">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">示例文件</span>
              <el-tooltip
                placement="top"
                :content="`文件名包含'${getTypeTitle()}'和'版本'号等数据`"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-upload
            ref="uploadExampleRef"
            :key="'uploadExampleKey'"
            :limit="1"
            :accept="uploadExample.fileType.join(',')"
            :headers="uploadExample.headers"
            :action="`${uploadExample.url}?id=${currRowId}&name=${form.name}&version=${form.version}&type=${standType}&isExample=true`"
            :disabled="uploadExample.isUploading"
            :before-upload="checkBeforeTempUpload"
            :on-progress="() => handleFileUploadProgress(true)"
            :on-success="
              (response, file, fileList) =>
                handleFileSuccess(response, file, fileList, true)
            "
            :auto-upload="true"
            :show-file-list="false"
            drag
            style="width: 635px"
            class="upload-demo"
          >
            <el-icon class="el-icon--upload">
              <upload-filled />
            </el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                已上传文件:
                <a
                  style="color: var(--el-color-primary)"
                  href="javascript:void(0);"
                  @click="downloadExampleExcel(form.exampleName)"
                >
                  {{ form.exampleName }}
                </a>
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div v-loading="loadingFlag" class="text-center">
          <el-button type="primary" @click="saveForm">保存</el-button>
          <el-button @click="open = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { getToken } from '@/utils/auth';
  import {
    getCurrentInstance,
    nextTick,
    reactive,
    ref,
    toRaw,
    toRefs,
    watch,
  } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    saveStandMg,
    stdExpList,
    updateStandMgStatus,
  } from '@/api/standard/expMg';
  import { isArrEmpty, titleCase, trimStr } from '@/utils';
  import { getConfigKey } from '@/api/system/config';
  import ExpType from '@/views/standard/experiment/expType.vue';

  const props = defineProps({
    sampleFlag: {
      type: Boolean,
      required: false,
      default() {
        return false;
      },
    },
  });

  const { sampleFlag } = props;

  const standType = ref(sampleFlag ? 'sample' : 'experiment');
  const { proxy } = getCurrentInstance();
  const router = useRouter();

  const data = reactive({
    form: {
      id: null,
      parentName: '',
      name: '',
      version: '1.0',
      description: '',
      zhDescription: '',
      recommendNum: 0,
      recommendTip: '',
      desc: '',
      protocol: '',
      status: 'disable',
      sort: 1,
      iconName: 'O',
      iconColor: '#808080',
      templateName: '',
      exampleName: '',
    },
    queryParams: {
      name: null,
      status: null,
      createDateStart: null,
      createDateEnd: null,
    },
    rules: {
      name: [
        {
          pattern: /^(?!\s+$)[-\w ]{1,40}$/,
          required: true,
          message: 'Illegal name format',
          trigger: 'blur',
        },
      ],
      version: [
        {
          pattern: /^\d+(\.\d+){0,2}$/,
          required: true,
          message: 'Illegal version format',
          trigger: 'blur',
        },
      ],
      recommendNum: [
        {
          required: true,
          message: '推荐数量不能为空',
          trigger: 'blur',
        },
      ],
      templateName: [
        {
          required: true,
          message: '模板文件是必需的',
          trigger: 'change',
        },
      ],
      exampleName: [
        {
          required: true,
          message: '示例文件是必需的',
          trigger: 'change',
        },
      ],
      sort: [
        {
          required: true,
          message: '排序不能为空',
          trigger: 'blur',
        },
      ],
      iconName: [
        {
          pattern: /^[A-Z]{1,2}$/,
          required: true,
          message: '图标名称必须是大写字母',
          trigger: 'blur',
        },
      ],
      iconColor: [
        {
          required: true,
          message: '图标颜色不能为空',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);
  // 表单初始数据
  const formInit = reactive(proxy.$_.cloneDeep(toRaw(data.form)));

  const loadingFlag = ref(false);
  const showSearch = ref(true);
  const auditTime = ref([]);
  // const total = ref(100);
  const tableData = reactive([]);
  const attrTypeListRef = ref([]);
  const statusListRef = ref([]);
  const parentListRef = ref([]);

  const title = ref('');
  const open = ref(false);
  const addFlag = ref(true);

  // 顶部重置搜索栏
  function resetSearch() {
    proxy.resetForm('searchFormRef');
    queryParams.value.name = null;
    queryParams.value.status = null;
    auditTime.value = [];
    initList();
  }

  const currRowId = ref('');

  /*** 模版导入参数 */
  const uploadTemp = reactive({
    fileType: ['.xlsx'],
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + `/system/stdMg/uploadTemplate`,
  });

  /*** 带样例的模版导入参数 */
  const uploadExample = reactive({
    fileType: ['.xlsx'],
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url: import.meta.env.VITE_APP_BASE_API + `/system/stdMg/uploadTemplate`,
  });

  function getMainTitle() {
    return titleCase(standType.value);
  }

  function getTypeTitle() {
    if (getMainTitle() == 'Sample') {
      return '样本类型';
    } else {
      return '实验类型';
    }
  }

  function getStatusLabel(value) {
    const statusMap = {
      optional: '选填',
      required: '必填',
      recommend: '推荐填写',
      none: '无',
      disable: '停用',
      enable: '启用',
    };
    return statusMap[value] || value;
  }

  /**文件上传中处理 */
  function checkBeforeTempUpload(file) {
    return new Promise((resolve, reject) => {
      const fileName = file.name.split('.');
      const fileExt = `.${fileName[fileName.length - 1]}`.toLowerCase();
      const isTypeOk = uploadTemp.fileType.indexOf(fileExt) >= 0;
      if (!isTypeOk) {
        proxy.$modal.msgError(
          `Please upload ${uploadTemp.fileType.join('/')} format file!`,
        );
        reject();
      } else {
        const refObj = proxy.$refs['expRef'];
        // 先调用名称、版本号校验器，显示错误信息
        refObj.validateField('name', () => {});
        refObj.validateField('version', () => {});
        // 名称、版本号全部校验通过才能上传文件
        refObj.validateField('name', valid => {
          if (valid) {
            refObj.validateField('version', valid2 => {
              if (valid2) {
                resolve();
              } else {
                proxy.$modal.alertWarning('请输入有效的版本');
                reject();
              }
            });
          } else {
            proxy.$modal.alertWarning(`请输入有效的${getTypeTitle()}`);
            reject();
          }
        });
      }
    });
  }

  /**文件上传中处理 */
  const handleFileUploadProgress = isExample => {
    if (isExample) {
      uploadExample.isUploading = true;
    } else {
      uploadTemp.isUploading = true;
    }
  };

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList, isExample) => {
    if (isExample) {
      uploadExample.isUploading = false;
      proxy.$refs['uploadExampleRef'].handleRemove(file);
    } else {
      uploadTemp.isUploading = false;
      proxy.$refs['uploadTempRef'].handleRemove(file);
    }
    if (response.code === 200) {
      const refObj = proxy.$refs['expRef'];
      if (isExample) {
        form.value.exampleName = trimStr(response.msg);
        refObj.validateField('exampleName', () => {});
      } else {
        form.value.templateName = trimStr(response.msg);
        refObj.validateField('templateName', () => {});
      }
      proxy.$modal.msgSuccess('上传成功');
    } else {
      if (isExample) {
        form.value.exampleName = '';
      } else {
        form.value.templateName = '';
      }
      proxy.$modal.alertError(response.msg);
    }
  };

  // 计算推荐字段占比
  function initRate(row) {
    const { recommendNum, attributes } = row;
    if (!attributes) {
      return '';
    }
    const num = recommendNum ? recommendNum : 0;
    const total = attributes.length;
    let recAttrCount = 0;
    for (let i = 0; i < total; i++) {
      let attr = attributes[i];
      if ('recommend' === attr['required'] && 'enable' === attr['status']) {
        recAttrCount++;
      }
    }
    const rate = recAttrCount ? (num / recAttrCount) * 100 : 0;
    if (rate) {
      return `(${rate.toFixed(1)}%)`;
    } else {
      return ``;
    }
  }

  // 单行数据状态变更
  function doChangeStatus(row, status) {
    const param = {
      id: row.id,
      disable: status === 'disable',
    };
    updateStandMgStatus(param)
      .then(() => {})
      .catch(() => {
        if (param.disable) {
          row.status = 'enable';
        } else {
          row.status = 'disable';
        }
      });
  }

  /** 重置操作表单 */
  async function reset() {
    await nextTick(() => {
      proxy.resetForm('expRef');
      proxy.$refs['uploadTempRef']?.clearFiles();
      proxy.$refs['uploadExampleRef']?.clearFiles();
      form.value = proxy.$_.cloneDeep(toRaw(formInit));
    });
  }

  /** 导出按钮操作 */
  function handleExport() {
    let param = {
      standType: standType.value,
    };
    proxy.download(
      'system/stdMg/exportStand',
      param,
      `Standard_Data_Set(${standType.value})_${new Date().getTime()}.xlsx`,
    );
  }

  /** 新增按钮操作 */
  async function handleAdd() {
    addFlag.value = true;
    await reset();
    title.value = '新增' + getMainTitle();
    currRowId.value = '';
    open.value = true;
  }

  /** 修改按钮操作 */
  async function handleUpdate(row) {
    row = proxy.$_.cloneDeep(toRaw(row));
    addFlag.value = false;
    await reset();
    // 设置编辑弹窗回显数据
    for (let key in form.value) {
      form.value[key] = row[key];
    }
    title.value = '编辑' + getTypeTitle();
    currRowId.value = row.id;
    open.value = true;
  }

  // 打开属性列表界面
  function toAttr(row) {
    if (sampleFlag) {
      router.push({
        path: `/standard/samp/attr/${standType.value}/${row.id}`,
        query: { type: row.name },
      });
    } else {
      router.push({
        path: `/standard/exp/attr/${standType.value}/${row.id}`,
        query: { type: row.name },
      });
    }
  }

  // 保存表单信息
  function saveForm() {
    proxy.$refs['expRef'].validate(valid => {
      if (valid) {
        loadingFlag.value = true;
        const param = { ...form.value, type: standType.value };
        saveStandMg(param)
          .then(res => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess(`${param.id ? 'Update' : 'Add'} success`);
              open.value = false;
              initList();
            }
          })
          .finally(() => {
            loadingFlag.value = false;
          });
      }
    });
  }

  // 查询列表数据
  function initList() {
    loadingFlag.value = true;
    if (!isArrEmpty(auditTime.value)) {
      queryParams.value.createDateStart = auditTime.value[0];
      queryParams.value.createDateEnd = auditTime.value[1];
    } else {
      queryParams.value.createDateStart = null;
      queryParams.value.createDateEnd = null;
    }
    let param = {
      ...queryParams.value,
      type: standType.value,
    };
    tableData.length = 0;
    attrTypeListRef.value = [];
    stdExpList(param)
      .then(res => {
        // console.log(res);
        const resultData = res.data;
        if (resultData) {
          const { tbData, attrTypeList, statusList, parentList } = resultData;
          if (!isArrEmpty(tbData)) {
            tableData.push(...tbData);
          }
          attrTypeListRef.value = attrTypeList;
          statusListRef.value = statusList;
          parentListRef.value = parentList;
          // 设置提交表单枚举字段默认值
          if (!isArrEmpty(attrTypeList)) {
            const defaultType = attrTypeList[0].value;
            formInit.protocol = defaultType;
            formInit.desc = defaultType;
            if (sampleFlag) {
              formInit.organism = defaultType;
              formInit.tissue = defaultType;
            } else {
              formInit.organism = 'none';
              formInit.tissue = 'none';
            }
          }
        }
      })
      .finally(() => {
        loadingFlag.value = false;
      });
  }

  // 检查名称与上传文件名是否匹配
  function nameMatch(arr = [], val = '') {
    if (!arr || arr.length === 0) {
      return false;
    }
    return arr[arr.length - 2] === val;
  }

  // 检查版本号与上传文件名是否匹配
  function versionMatch(arr = [], val = '') {
    if (!arr || arr.length === 0) {
      return false;
    }
    return arr[arr.length - 1] === `v${val}.xlsx`;
  }

  // 名称变更时，清空上传文件字段
  watch(
    () => form.value.name,
    newVal => {
      const val = trimStr(newVal);
      if (!val) {
        form.value.templateName = '';
        form.value.exampleName = '';
      } else {
        // 名称与文件名不匹配
        const { templateName, exampleName } = form.value;
        if (!nameMatch(templateName.split('_'), val)) {
          form.value.templateName = '';
        }
        if (!nameMatch(exampleName.split('_'), val)) {
          form.value.exampleName = '';
        }
      }
    },
    {
      immediate: false,
      deep: false,
    },
  );
  // 版本号变更时，清空上传文件字段
  watch(
    () => form.value.version,
    newVal => {
      const val = trimStr(newVal);
      if (!val) {
        form.value.templateName = '';
        form.value.exampleName = '';
      } else {
        // 版本号与文件名不匹配
        const { templateName, exampleName } = form.value;
        if (!versionMatch(templateName.split('_'), val)) {
          form.value.templateName = '';
        }
        if (!versionMatch(exampleName.split('_'), val)) {
          form.value.exampleName = '';
        }
      }
    },
    {
      immediate: false,
      deep: false,
    },
  );

  const licenseStatus = ref(false);

  function getLicenseStatus() {
    getConfigKey('node.license.status').then(response => {
      let data = response.msg;
      if (data) {
        licenseStatus.value = 'enable' === data;
      }
    });
  }

  function downloadExampleExcel(fileName) {
    proxy.download(`/system/stdMg/downloadTemplate?fileName=${fileName}`);
  }

  // 进入页面后，调用查询列表方法
  getLicenseStatus();
  initList();
</script>

<style lang="scss" scoped>
  :deep(.el-dialog__body) {
    padding: 10px !important;

    .el-radio-group .el-radio__label {
      color: #848484 !important;
      font-size: 14px !important;
    }

    .el-textarea__inner {
      border-radius: 12px;
    }

    .el-input,
    .el-input-number {
      width: 230px;
    }
  }

  :deep(.el-upload-dragger) {
    padding: 4px 10px !important;

    .el-icon--upload {
      color: #fe7f2b;
      margin-bottom: 0;
      width: 55px;
      height: 50px;
    }
  }

  .stand-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }

  .el-color-picker__trigger {
    width: 200px;
  }
</style>
