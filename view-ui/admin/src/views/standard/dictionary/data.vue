<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="Dictionary Name" prop="dictType">
          <el-select v-model="queryParams.dictType" style="width: 240px">
            <el-option
              v-for="item in typeOptions"
              :key="item.dictId"
              :label="item.dictName"
              :value="item.dictType"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Dictionary Label" prop="dictLabel">
          <el-input
            v-model="queryParams.dictLabel"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 240px"
          >
            <el-option label="Enable" value="0" />
            <el-option label="Disable" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >Search</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:dict:data:add']"
            type="primary"
            icon="Plus"
            @click="handleAdd"
            >Add</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['standard:dict:data:remove']"
            type="danger"
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >Delete
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-if="defaultDictType === 'node_organization'"
            v-hasPermi="['standard:dict:data:remove']"
            type="info"
            icon="upload"
            @click="handleImport"
            >Import
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['standard:dict:data:export']"
            type="warning"
            icon="Download"
            @click="handleExport"
            >Export
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" icon="Close" @click="handleClose"
            >Close
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="dataList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="Dictionary Code"
          align="center"
          width="120"
          prop="dictCode"
        />

        <el-table-column label="Dictionary Label" align="left" prop="dictLabel">
          <template #default="scope">
            <span
              v-if="
                (scope.row.listClass == '' ||
                  scope.row.listClass == 'default') &&
                (scope.row.cssClass == '' || scope.row.cssClass == null)
              "
              >{{ scope.row.dictLabel }}</span
            >
            <el-tag
              v-else
              :type="
                scope.row.listClass == 'primary' ? '' : scope.row.listClass
              "
              :class="scope.row.cssClass"
              >{{ scope.row.dictLabel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="Dictionary Value"
          align="left"
          prop="dictValue"
        />
        <el-table-column
          label="Sort"
          align="center"
          prop="dictSort"
          width="80"
        />
        <el-table-column label="Status" align="center" prop="status" width="80">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="Notes"
          align="left"
          prop="remark"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Create Time"
          align="center"
          prop="createTime"
          width="160"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          align="center"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="Edit">
              <svg-icon
                v-hasPermi="['standard:dict:data:edit']"
                icon-class="edits"
                class-name="meta-svg"
                @click="handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Delete">
              <svg-icon
                v-hasPermi="['standard:dict:data:remove']"
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" width="500px" append-to-body>
      <el-form ref="dataRef" :model="form" :rules="rules" label-width="135px">
        <el-form-item label="Dictionary Type">
          <el-input v-model="form.dictType" :disabled="true" />
        </el-form-item>
        <el-form-item label="Dictionary Label" prop="dictLabel">
          <el-input v-model="form.dictLabel" />
        </el-form-item>
        <el-form-item label="Dictionary Value" prop="dictValue">
          <el-input
            v-model="form.dictValue"
            placeholder="Please enter the data key value"
          />
        </el-form-item>
        <el-form-item label="Sort" prop="dictSort">
          <el-input-number
            v-model="form.dictSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="Status" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">Enable</el-radio>
            <el-radio label="1">Disable</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="Notes" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="Please enter the content"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button type="primary" @click="submitForm">Confirm</el-button>
          <el-button @click="cancel">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- organization字典导入对话框 -->
    <el-dialog
      v-model="upload.open"
      :title="upload.title"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text">
          Drag files here，or<em>&nbsp;click to upload</em>
        </div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <p>Only allow importing xls and xlsx format files</p>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px"
              @click="importTemplate"
              >Download template
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            :disabled="upload.isUploading"
            @click="submitFileForm"
            >Confirm
          </el-button>
          <el-button @click="upload.open = false">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Data">
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';

  import useDictStore from '@/store/modules/dict';
  import {
    getType,
    optionselect as getDictOptionselect,
  } from '@/api/system/dict/type';
  import {
    addData,
    delData,
    getData,
    listData,
    updateData,
  } from '@/api/system/dict/data';
  import { useRoute } from 'vue-router';
  import { getToken } from '@/utils/auth';

  const { proxy } = getCurrentInstance();
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

  const dataList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const defaultDictType = ref('');
  const typeOptions = ref([]);
  const route = useRoute();

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      dictType: undefined,
      dictLabel: undefined,
      status: undefined,
    },
    rules: {
      dictLabel: [
        {
          required: true,
          message: 'The data label cannot be empty',
          trigger: 'blur',
        },
      ],
      dictValue: [
        {
          required: true,
          message: 'The data key value cannot be empty',
          trigger: 'blur',
        },
      ],
      dictSort: [
        {
          required: true,
          message: 'The data order cannot be empty',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询字典类型详细 */
  function getTypes(dictId) {
    getType(dictId).then(response => {
      queryParams.value.dictType = response.data.dictType;
      defaultDictType.value = response.data.dictType;
      getList();
    });
  }

  /** 查询字典类型列表 */
  function getTypeList() {
    getDictOptionselect().then(response => {
      typeOptions.value = response.data;
    });
  }
  /** 查询字典数据列表 */
  function getList() {
    loading.value = true;
    listData(queryParams.value).then(response => {
      dataList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      dictCode: undefined,
      dictLabel: undefined,
      dictValue: undefined,
      cssClass: undefined,
      listClass: 'default',
      dictSort: 0,
      status: '0',
      remark: undefined,
    };
    proxy.resetForm('dataRef');
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 返回按钮操作 */
  function handleClose() {
    const obj = { path: '/standard/dictionary' };
    proxy.$tab.closeOpenPage(obj);
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    queryParams.value.dictType = defaultDictType.value;
    handleQuery();
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = 'Add Dictionary Data';
    form.value.dictType = queryParams.value.dictType;
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.dictCode);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const dictCode = row.dictCode || ids.value;
    getData(dictCode).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = 'Edit Dictionary Data';
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['dataRef'].validate(valid => {
      if (valid) {
        if (form.value.dictCode != undefined) {
          updateData(form.value).then(() => {
            useDictStore().removeDict(queryParams.value.dictType);
            proxy.$modal.msgSuccess('Modified successfully');
            open.value = false;
            getList();
          });
        } else {
          addData(form.value).then(() => {
            useDictStore().removeDict(queryParams.value.dictType);
            proxy.$modal.msgSuccess('Add successfully');
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const dictCodes = row.dictCode || ids.value;
    proxy.$modal
      .confirm(
        'Are you sure to delete the data item with dictionary code "' +
          dictCodes +
          '"?',
      )
      .then(function () {
        return delData(dictCodes);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('Delete successful');
        useDictStore().removeDict(queryParams.value.dictType);
      })
      .catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/dict/data/export',
      {
        ...queryParams.value,
      },
      `dict_data_${new Date().getTime()}.xlsx`,
    );
  }

  /*** organization字典导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    open: false,
    // 弹出层标题（用户导入）
    title: '',
    // 是否禁用上传
    isUploading: false,
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url:
      import.meta.env.VITE_APP_BASE_API +
      '/system/dict/data/importOrganizationData',
  });

  /** 导入按钮操作 */
  function handleImport() {
    upload.title = 'Import';
    upload.open = true;
  }

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download(
      'system/dict/data/importOrganizationTemplate',
      {},
      `organization_template_${new Date().getTime()}.xlsx`,
    );
  }

  /**文件上传中处理 */
  const handleFileUploadProgress = (event, file, fileList) => {
    upload.isUploading = true;
    proxy.$modal.loading('Uploading...');
  };

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    upload.open = false;
    upload.isUploading = false;
    proxy.$modal.closeLoading();
    proxy.$refs['uploadRef'].handleRemove(file);
    proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
        response.msg +
        '</div>',
      'Import Results',
      { dangerouslyUseHTMLString: true },
    );
    getList();
  };

  /** 提交上传文件 */
  function submitFileForm() {
    proxy.$refs['uploadRef'].submit();
  }

  getTypes(route.params && route.params.dictId);
  getTypeList();
</script>
<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
