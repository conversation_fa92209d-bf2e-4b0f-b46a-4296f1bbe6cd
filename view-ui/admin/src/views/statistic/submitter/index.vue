<template>
  <div class="app-container">
    <div class="card list mb-1">
      <div class="d-flex justify-space-between">
        <h3 class="mb-0 mt-0">Temporary Data Statistics</h3>
        <div>
          <span class="font-600 text-main-color ml-1 mr-1">Year:</span>
          <el-select v-model="year" style="width: 250px">
            <el-option value="2024" label="2024" />
            <el-option value="2023" label="2023" />
            <el-option value="2022" label="2022" />
            <el-option value="before 2022" label="before 2022" />
          </el-select>
        </div>
      </div>
      <el-divider></el-divider>
      <el-row :gutter="70" class="justify-center">
        <el-col :span="8" :xs="24">
          <div class="request radius-14 data">
            <div class="before-circle font-600 accessible font-18">Unfiled</div>
            <div class="bg-gray mb-05 item-number">
              <span>Number</span>
              <div>
                <span>230</span>
                <span>times</span>
              </div>
            </div>
            <div class="bg-gray item-number">
              <span>Size</span>
              <div>
                <span>2300</span>
                <span>TB</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8" :xs="24">
          <div class="data radius-14">
            <div class="before-circle font-600 accessible font-18">
              Unchecked
            </div>
            <div class="bg-gray mb-05 item-number">
              <span>Number</span>
              <div>
                <span>230</span>
                <span>times</span>
              </div>
            </div>
            <div class="bg-gray item-number">
              <span>Size</span>
              <div>
                <span>1022</span>
                <span>TB</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div class="card list">
      <h3 class="mb-0 mt-0">Submitter</h3>
      <el-divider></el-divider>

      <el-row :gutter="20" class="radius-12 mb-1">
        <el-col :span="18" :xs="24">
          <span class="font-600 text-main-color ml-1 mr-1">Submitter:</span>
          <el-input v-model="submitter" style="width: 250px"></el-input>
          <span class="font-600 text-main-color ml-1 mr-1">Organization:</span>
          <el-input v-model="organization" style="width: 250px"></el-input>
          <span class="font-600 text-main-color ml-1 mr-1">Year:</span>
          <el-select v-model="year" style="width: 250px">
            <el-option value="2024" label="2024" />
            <el-option value="2023" label="2023" />
            <el-option value="2022" label="2022" />
            <el-option value="before 2022" label="before 2022" />
          </el-select>
        </el-col>
        <el-col :span="6" :xs="24" class="text-align-right"
          ><el-radio-group v-model="selectMetadata">
            <el-radio-button label="Project" value="Project" />
            <el-radio-button label="Experiment" value="Experiment" />
            <el-radio-button label="Sample" value="Sample" />
            <el-radio-button label="Analysis" value="Analysis" />
          </el-radio-group>
        </el-col>
        <el-col :span="24">
          <div id="statistic" style="width: 100%; height: 450px"></div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup name="Index">
  import { nextTick, onMounted, ref, reactive } from 'vue';

  import * as echarts from 'echarts';

  const submitter = ref('');
  const organization = ref('');
  const year = ref('');

  const selectMetadata = ref('Project');
  const statisticData = reactive([
    {
      institution: 'Fudan University',
      accessible: 60,
      unaccessible: 40,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '1022 TB',
      restrictedSize: '452 TB',
      privateSize: '53 TB',
    },
    {
      institution: 'Ocean University of China',
      accessible: 60,
      unaccessible: 45,
      public: 67,
      restricted: 89,
      private: 34,
      publicSize: '1022 TB',
      restrictedSize: '452 TB',
      privateSize: '53 TB',
    },
    {
      institution: 'Sinotechgenomics',
      accessible: 60,
      unaccessible: 87,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '45 TB',
      restrictedSize: '35 TB',
      privateSize: '64 TB',
    },
    {
      institution: 'Shanghai Jiao Tong University',
      accessible: 60,
      unaccessible: 53,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
    {
      institution: 'Guangxi University',
      accessible: 34,
      unaccessible: 40,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
    {
      institution: 'Zhejiang University',
      accessible: 46,
      unaccessible: 64,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
    {
      institution: 'Tongji University',
      accessible: 75,
      unaccessible: 54,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
    {
      institution: 'Wuhan University',
      accessible: 60,
      unaccessible: 40,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
    {
      institution: 'SIBS',
      accessible: 60,
      unaccessible: 40,
      public: 67,
      restricted: 34,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
    {
      institution: 'PICB',
      accessible: 60,
      unaccessible: 40,
      public: 67,
      restricted: 56,
      private: 34,
      publicSize: '74 TB',
      restrictedSize: '84 TB',
      privateSize: '433 TB',
    },
  ]);

  const echartInit = () => {
    const statistiChart = echarts.init(document.getElementById('statistic'));
    const options = {
      color: ['#1981F4', '#2ADAC9'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          var tooltipContent = `${params[0].axisValueLabel}<hr>`;
          params.forEach(function (item) {
            tooltipContent += `<span class="tooltip-label">${item.seriesName}:</span>${item.value}<br>`;
          });
          tooltipContent += `
            <span class="tooltip-label">Public: </span>
            ${statisticData[params[0].dataIndex].public} (${
            statisticData[params[0].dataIndex].publicSize
          })<br>

            <span class="tooltip-label">Restricted:</span> ${
              statisticData[params[0].dataIndex].restricted
            } (${statisticData[params[0].dataIndex].restrictedSize})<br>

            <span class="tooltip-label">Private:</span> ${
              statisticData[params[0].dataIndex].private
            } (${statisticData[params[0].dataIndex].privateSize})<br>
             `;
          return tooltipContent;
        },
        confine: true,
      },
      legend: {
        top: '4%',
      },
      grid: {
        top: '14%',
        left: '3%',
        right: '4%',
        bottom: '0%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: statisticData.map(it => it.institution),
          axisLabel: {
            interval: 0,
            rotate: 40,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            show: true,
          },
          splitLine: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: 'Accessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barMaxWidth: 25,
          data: statisticData.map(it => it.accessible),
        },
        {
          name: 'Unaccessible',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          data: statisticData.map(it => it.unaccessible),
        },
      ],
    };
    statistiChart.setOption(options);

    window.onresize = function () {
      statistiChart.resize();
    };
  };

  onMounted(() => {
    nextTick(() => {
      echartInit();
    });
  });
</script>

<style scoped lang="scss">
  .el-radio-button:first-child {
    :deep(.el-radio-button__inner) {
      border-radius: 12px 0 0 12px;
    }
  }
  .el-radio-button:last-child {
    :deep(.el-radio-button__inner) {
      border-radius: 0 12px 12px 0;
    }
  }
  :deep(.el-form-item__label) {
    color: #858181;
  }
  .data {
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.12);
    padding: 6px 6px 20px 6px;
    .before-circle:before {
      height: 7px;
      width: 7px;
      background-color: #ea8cac;
    }
  }
  .item-number {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 15px;
    span:first-child {
      font-size: 15px;
      color: #666666;
      font-weight: 600;
    }
    div > span:nth-child(1) {
      font-size: 19px;
      font-weight: 600;
      color: #333333;
      margin-right: 8px;
    }
    div > span:nth-child(2) {
      display: inline-block;
      width: 40px;
      color: #999999;
    }
  }
  :deep(.tooltip-label) {
    display: inline-block;
    width: 120px;
  }
</style>
