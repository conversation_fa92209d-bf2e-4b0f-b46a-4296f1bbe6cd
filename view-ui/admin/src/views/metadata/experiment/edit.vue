<template>
  <div class="project app-container w-100">
    <div class="card general-info card-container mt-1 pt-0">
      <FillTip :recommend="true"></FillTip>
      <div class="category-title font-600 text-main-color">
        General Information
      </div>
      <div
        :key="'exp-general-info-' + componentKey"
        class="plr-20 bg-gray mt-1"
      >
        <el-form
          ref="expForm"
          label-position="top"
          :model="form"
          :inline="true"
          :scroll-to-error="true"
          :rules="rules"
          style="padding-top: 8px"
        >
          <el-form-item label="Experiment ID" style="width: calc(30% + 146px)">
            <div class="d-flex align-items-center w-100">
              <el-input v-model="form.expNo" disabled />
              <span
                v-if="form.usedIds"
                class="text-warning"
                style="width: 220px; margin-left: 0.5rem"
              >
                Used ID: {{ form.usedIds.join('; ') }}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="Experiment Name" prop="name">
            <el-input v-model="form.name" />
          </el-form-item>
          <el-form-item label="Project ID" prop="projectNo" class="w-75">
            <el-select
              v-model="form.projectNo"
              class="w-100 m-2"
              placeholder="Please Choose Project"
              filterable
              :teleported="false"
            >
              <el-option
                v-for="item in existProjList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            v-if="hasDesc !== 'none'"
            class="w-100"
            :required="hasDesc === 'required'"
            label="Sample Description"
            prop="description"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon v-if="hasDesc === 'recommend'"></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">Experiment Description</span>
            </template>
            <el-input v-model="form.description" type="textarea" rows="5" />
          </el-form-item>

          <el-form-item
            v-if="hasProtocol !== 'none'"
            class="w-100"
            :required="hasProtocol === 'required'"
            prop="protocol"
          >
            <template #label>
              <!--推荐填写-->
              <recommend-icon
                v-if="hasProtocol === 'recommend'"
              ></recommend-icon>

              <!--无字段描述信息-->
              <span class="font-bold">Experiment Protocol</span>
            </template>
            <el-input v-model="form.protocol" type="textarea" rows="5" />
          </el-form-item>

          <RelatedLinks
            :key="'exp-RelatedLinks' + componentKey"
            v-model:relatedLinks="form.relatedLinks"
          ></RelatedLinks>
        </el-form>
      </div>
      <div class="category-title font-600 text-main-color">
        Experiment Information
      </div>
      <div class="plr-20 new-content mt-1">
        <div class="w-100 exp-type">
          <div class="radius-12 follow-list">
            The following lists common omics experiment types. If you need
            additional omics types for your submission, please e-mail us
            <a class="text-primary" href="mailto:<EMAIL>"
              ><EMAIL></a
            >
            with a brief description of the type of data you are trying to
            submit, and one of our curators will quickly get back to you.
          </div>
          <div class="d-flex flex-column justify-space-between mt-1">
            <p class="font-16 text-main-color font-600 mb-1">Experiment Type</p>
            <el-radio-group
              v-model="form.expType"
              class="exp-radio-group"
              @change="changeExpType"
            >
              <el-radio
                v-for="item in expTypeList"
                :key="item.name"
                :label="item.name"
                >{{ item.name }}
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <ExpAttr
          :key="'attrExp' + attrComponentKey"
          ref="expAttrRef"
          v-model:attributes="form.attributes"
          v-model:recommend-filled-count="recommendFilledCount"
          :exp-type="form.expType"
        ></ExpAttr>
      </div>

      <Publication
        :key="'exp-Publication' + componentKey"
        v-model:publishData="form.publish"
      ></Publication>
      <Submitter
        ref="submitterRef"
        :key="'exp-submitter-' + componentKey"
        v-model:submitter-data="form.submitter"
      ></Submitter>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="saveData"
          >Save
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="handleClose"
          >Back
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import { getExperimentType, getExpSapData } from '@/api/metadata/dict';
  import { editExp, getExpInfo } from '@/api/metadata/experiment';
  import { useRoute } from 'vue-router';
  import ExpAttr from '@/views/metadata/common/ExpAttr.vue';
  import Publication from '@/views/metadata/common/Publications.vue';
  import RelatedLinks from '@/views/metadata/common/RelatedLinks.vue';
  import Submitter from '@/views/metadata/common/Submitter.vue';
  import FillTip from '@/views/metadata/common/FillTip.vue';
  import RecommendIcon from '@/views/metadata/common/RecommendIcon.vue';
  import { getProjectList } from '@/api/metadata/project';

  const route = useRoute();
  let id = route.params.id;

  const { proxy } = getCurrentInstance();

  const existProjList = ref([]); // 用户所有项目列表
  const expTypeList = ref([]); // 系统拥有的组学类型列表
  const componentKey = ref(1);
  const attrComponentKey = ref(1);

  const data = reactive({
    form: {
      expNo: undefined,
      projectNo: undefined,
      name: '',
      description: undefined,
      protocol: undefined,
      expType: 'Genomic',
      usedIds: undefined,
      attributes: {},
      relatedLinks: undefined,
      publish: [
        {
          id: undefined,
          publication: undefined,
          doi: undefined,
          pmid: undefined,
          reference: undefined,
          articleName: undefined,
        },
      ],
      submitter: {},
      creator: '',
    },
    rules: {
      projectNo: [
        {
          required: true,
          message: 'Please Choose Project',
          trigger: 'change',
        },
      ],
      name: [
        {
          required: true,
          trigger: 'blur',
        },
      ],
    },
  });

  const { form, rules } = toRefs(data);

  let resultArr = reactive([]); // 存放子组件的数组
  let errListMsg = ref(''); // 用来存储错误提示

  // 创建Promise 实例，为多个组件校验使用
  const checkForm = formChild => {
    let result = new Promise((resolve, reject) => {
      formChild.validate((valid, fields) => {
        if (valid) {
          resolve();
        } else {
          Object.keys(fields).forEach((v, index) => {
            if (index === 0) {
              // 定位到错误的位置
              // const PropName = fields[v][0].field;
              // formChild.scrollToField(PropName);
              errListMsg.value = fields[v][0].message;
            }
          });
          reject();
        }
      });
    });
    resultArr.push(result);
  };

  let expAttrRef = ref();

  // 必须填写的推荐字段数量
  let mustRecommendNum = ref(0);
  // 自定义表单中填写了推荐字段数量
  let recommendFilledCount = ref(0);

  /** 提交数据 */
  const saveData = () => {
    let allRecommendFilledCount = recommendFilledCount.value;
    if (hasDesc.value === 'recommend' && form.value.description) {
      allRecommendFilledCount++;
    }
    if (hasProtocol.value === 'recommend' && form.value.protocol) {
      allRecommendFilledCount++;
    }
    if (allRecommendFilledCount < mustRecommendNum.value) {
      // 校验推荐填写的数量是否达标
      proxy.$modal.alertWarning(
        'Insufficient data for recommended attributes, please continue to fill in',
      );
      return;
    }

    // 获取该子组件暴露出来的form的ref
    const approvalExpAttrRef = expAttrRef.value.expAttrForm;
    // 调用上面创建好的方法
    checkForm(approvalExpAttrRef);
    checkForm(proxy.$refs['expForm']);

    Promise.all(resultArr)
      .then(() => {
        proxy.$modal.loading('saving...');
        // 校验通过
        editExp(form.value)
          .then(response => {
            form.value = response.data;
            componentKey.value++;
            attrComponentKey.value++;
            proxy.$modal.alertSuccess('Save successful');
            proxy.$modal.closeLoading();
          })
          .finally(() => {
            // 关闭loading
            proxy.$modal.closeLoading();
          });
      })
      .catch(() => {
        // 校验不通过提示
        proxy.$modal.msgError(errListMsg.value);
        // console.error(errListMsg.value, err);
      });
    resultArr = []; // 每次请求完要清空数组
    errListMsg.value = ''; // 提示也需要清空
  };

  /** 加载系统所拥有的组学类型 */
  function loadExperiment() {
    getExperimentType().then(response => {
      expTypeList.value = response.data;
      changeExpType();
    });
  }

  const hasDesc = ref('optional');
  const hasProtocol = ref('optional');

  /** 查询当前组学类型必填的推荐字段数量 */
  function changeExpType() {
    getExpSapData(form.value.expType).then(response => {
      mustRecommendNum.value = response.data.recommendNum;
      hasDesc.value = response.data.desc;
      hasProtocol.value = response.data.protocol;
    });
  }

  /** 加载用户项目列表 */
  function loadUserProject() {
    getProjectList(form.value.creator).then(response => {
      existProjList.value = response.data;
    });
  }

  /** 返回按钮操作 */
  function handleClose() {
    const obj = { path: '/metadata/experiments' };
    proxy.$tab.closeOpenPage(obj);
  }

  onMounted(() => {
    // 获取信息
    getExpInfo(id).then(response => {
      form.value = response.data;
      componentKey.value++;
      attrComponentKey.value++;
      loadUserProject();
      loadExperiment();
    });
  });
</script>

<style lang="scss" scoped>
  .project {
    .exp-radio-group {
      :deep(.el-radio) {
        width: 17%;
      }
    }

    :deep(.el-radio__label) {
      font-size: 16px;
    }

    .tips {
      font-size: 14px;
      margin-left: 1.3rem;
      //margin-top: .8rem;
    }

    .new-content {
      background-color: #fcfdfe;

      .exp-type {
        padding-bottom: 12px;
        border-bottom: 1px solid #e0e0e0;

        .follow-list {
          background-color: #f4f5f6;
          padding: 8px;
          border: 1px solid #d2d2d2;
        }
      }

      .el-form-item {
        width: 30%;

        .el-select {
          width: 100%;
        }
      }
    }

    .links {
      :deep(.el-form-item__label) {
        font-weight: 700;
      }

      :deep(.el-form-item__content) {
        flex-direction: column;
        align-items: flex-start;

        & + .el-form-item__label {
          font-weight: 700;
        }
      }
    }

    .exist-form {
      width: 100%;
      flex-wrap: wrap;

      .el-form-item {
        width: calc((100% - 100px) / 3) !important;
        margin-right: 10px;

        .el-select {
          width: 100%;
        }
      }
    }

    .general-info {
      .el-form {
        .el-form-item {
          width: 30%;

          .el-select-v2 {
            width: 100%;
          }
        }

        :deep(.el-form-item__label) {
          font-weight: 700;
        }
      }
    }

    :deep(.el-form-item__label) {
      font-weight: 700;
    }

    :deep(.el-select-v2__wrapper) {
      border-radius: 12px;
    }
  }
</style>
