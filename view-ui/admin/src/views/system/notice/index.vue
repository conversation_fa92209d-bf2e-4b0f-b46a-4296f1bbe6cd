<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="Notice Title" prop="noticeTitle">
          <el-input
            v-model="queryParams.noticeTitle"
            placeholder="Please input notice title"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="CreateBy" prop="createBy">
          <el-input
            v-model="queryParams.createBy"
            placeholder="Please input operator"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Notice Status" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="Select status"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dict in sys_notice_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >Search</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:notice:add']"
            type="primary"
            icon="Plus"
            @click="handleAdd"
            >Add</el-button
          >
        </el-col>

        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:notice:remove']"
            type="danger"
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >Delete</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="noticeList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="Notice Id"
          align="center"
          prop="noticeId"
          width="100"
        />
        <el-table-column
          label="Notice Title"
          align="center"
          prop="noticeTitle"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Status"
          align="center"
          prop="status"
          width="100"
        >
          <template #default="scope">
            <dict-tag :options="sys_notice_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="Creator"
          align="center"
          prop="createBy"
          width="100"
        />
        <el-table-column label="Start Time" align="center" prop="startTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="End Time" align="center" prop="endTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="Create Time" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="Edit">
              <svg-icon
                v-hasPermi="['system:notice:edit']"
                icon-class="edits"
                class-name="meta-svg"
                @click="handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>

            <el-tooltip content="Delete">
              <svg-icon
                v-hasPermi="['system:notice:remove']"
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改公告对话框 -->
    <el-dialog v-model="open" :title="title" width="780px" append-to-body>
      <el-form ref="noticeRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="Notice Title" prop="noticeTitle">
              <el-input
                v-model="form.noticeTitle"
                placeholder="Please input notice title"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="Start Time" required prop="startTime">
              <el-date-picker
                v-model="form.startTime"
                style="width: 250px"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                range-separator="-"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="End Time">
              <el-date-picker
                v-model="form.endTime"
                style="width: 250px"
                value-format="YYYY-MM-DD HH:mm:ss"
                type="datetime"
                range-separator="-"
              ></el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="Status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in sys_notice_status"
                  :key="dict.value"
                  :label="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Notice Content">
              <editor v-model="form.noticeContent" :min-height="192" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">Confirm</el-button>
          <el-button @click="cancel">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Notice">
  import {
    listNotice,
    getNotice,
    delNotice,
    addNotice,
    updateNotice,
  } from '@/api/system/notice';
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';

  const { proxy } = getCurrentInstance();
  const { sys_notice_status } = proxy.useDict('sys_notice_status');

  const noticeList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      noticeTitle: undefined,
      createBy: undefined,
      status: undefined,
    },
    rules: {
      noticeTitle: [
        {
          required: true,
          message: 'The notification title cannot be empty',
          trigger: 'blur',
        },
      ],
      startTime: [
        {
          required: true,
          message: 'The effective start date cannot be empty',
          trigger: 'blur',
        },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询公告列表 */
  function getList() {
    loading.value = true;
    listNotice(queryParams.value).then(response => {
      noticeList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      noticeId: undefined,
      noticeTitle: undefined,
      noticeType: '1',
      noticeContent: undefined,
      status: '1',
    };
    proxy.resetForm('noticeRef');
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.noticeId);
    single.value = selection.length !== 1;
    multiple.value = !selection.length;
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = 'Add Notice';
  }
  /**修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const noticeId = row.noticeId || ids.value;
    getNotice(noticeId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = 'Edit Notice';
    });
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['noticeRef'].validate(valid => {
      if (valid) {
        if (form.value.noticeId != undefined) {
          updateNotice(form.value).then(() => {
            proxy.$modal.msgSuccess('Edited Successfully');
            open.value = false;
            getList();
          });
        } else {
          addNotice(form.value).then(() => {
            proxy.$modal.msgSuccess('Added Successfully');
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const noticeIds = row.noticeId || ids.value;
    proxy.$modal
      .confirm(
        ' Do you want to confirm the deletion of the data item with notice number"' +
          noticeIds +
          '"？',
      )
      .then(function () {
        return delNotice(noticeIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('Successfully Deleted');
      })
      .catch(() => {});
  }

  getList();
</script>
<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
