<template>
  <el-form ref="pwdRef" :model="user" :rules="rules" label-width="150px">
    <el-form-item label="Old Password" prop="oldPassword">
      <el-input
        v-model="user.oldPassword"
        placeholder="Please enter old password"
        type="password"
        style="width: 350px"
        show-password
      />
    </el-form-item>
    <el-form-item label="New Password" prop="newPassword">
      <el-input
        v-model="user.newPassword"
        placeholder="Please enter new password"
        type="password"
        style="width: 350px"
        show-password
      />
    </el-form-item>
    <el-form-item label="Confirm Password" prop="confirmPassword">
      <el-input
        v-model="user.confirmPassword"
        placeholder="Please confirm the new password"
        type="password"
        style="width: 350px"
        show-password
      />
    </el-form-item>
    <el-form-item>
      <div style="margin-left: 80px">
        <el-button type="primary" @click="submit">Save</el-button>
        <el-button type="danger" @click="close">Close</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup>
  import { updateUserPwd } from '@/api/system/user';

  const { proxy } = getCurrentInstance();

  const user = reactive({
    oldPassword: undefined,
    newPassword: undefined,
    confirmPassword: undefined,
  });

  const equalToPassword = (rule, value, callback) => {
    if (user.newPassword !== value) {
      callback(new Error('两次输入的密码不一致'));
    } else {
      callback();
    }
  };
  const rules = ref({
    oldPassword: [
      { required: true, message: '旧密码不能为空', trigger: 'blur' },
    ],
    newPassword: [
      { required: true, message: '新密码不能为空', trigger: 'blur' },
      { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' },
    ],
    confirmPassword: [
      { required: true, message: '确认密码不能为空', trigger: 'blur' },
      { required: true, validator: equalToPassword, trigger: 'blur' },
    ],
  });

  /** 提交按钮 */
  function submit() {
    proxy.$refs.pwdRef.validate(valid => {
      if (valid) {
        updateUserPwd(user.oldPassword, user.newPassword).then(response => {
          proxy.$modal.msgSuccess('修改成功');
        });
      }
    });
  }
  /** 关闭按钮 */
  function close() {
    proxy.$tab.closePage();
  }
</script>
<style lang="scss" scoped>
  .el-form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    .el-form-item {
      width: 50%;
    }
  }
</style>
