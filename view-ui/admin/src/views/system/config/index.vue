<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="Config Name" prop="configName">
          <el-input
            v-model="queryParams.configName"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Config Key" prop="configKey">
          <el-input
            v-model="queryParams.configKey"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item
          v-hasRole="['admin']"
          label="System Built-in"
          prop="configType"
        >
          <el-select
            v-model="queryParams.configType"
            clearable
            style="width: 100px"
          >
            <el-option
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Create Time" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >Search</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:config:add']"
            type="primary"
            icon="Plus"
            @click="handleAdd"
            >Add</el-button
          >
        </el-col>
        <!--        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:config:edit']"
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            >Edit</el-button
          >
        </el-col>-->
        <!--        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:config:remove']"
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >Delete</el-button
          >
        </el-col>-->
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:config:export']"
            type="warning"
            icon="Download"
            @click="handleExport"
            >Export</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:config:remove']"
            type="danger"
            icon="Refresh"
            @click="handleRefreshCache"
            >Refresh</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        v-loading="loading"
        :data="configList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          label="Config ID"
          align="center"
          width="100"
          prop="configId"
        />
        <el-table-column
          label="Config Name"
          align="center"
          prop="configName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Config Key"
          align="center"
          prop="configKey"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Config Value"
          align="center"
          prop="configValue"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          v-hasRole="['admin']"
          label="仅超管可见"
          align="center"
          prop="configType"
        >
          <template #default="scope">
            <dict-tag :options="sys_yes_no" :value="scope.row.configType" />
          </template>
        </el-table-column>
        <el-table-column
          label="Remark"
          align="center"
          prop="remark"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Create Time"
          align="center"
          prop="createTime"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="Edit">
              <svg-icon
                v-hasPermi="['system:config:edit']"
                icon-class="edits"
                class-name="meta-svg"
                @click="handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <!--            <el-button
              v-hasPermi="['system:config:remove']"
              link
              type="primary"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >Delete</el-button
            >-->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <el-form ref="configRef" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="Config Name" prop="configName">
          <el-input
            v-model="form.configName"
            placeholder="Please input config name"
          />
        </el-form-item>
        <el-form-item v-hasRole="['admin']" label="Config Key" prop="configKey">
          <el-input
            v-model="form.configKey"
            placeholder="Please input config key"
          />
        </el-form-item>
        <el-form-item label="Config Value" prop="configValue">
          <el-input
            v-model="form.configValue"
            type="textarea"
            rows="5"
            placeholder="Please input config value"
          />
        </el-form-item>
        <el-form-item
          v-hasRole="['admin']"
          label="仅超管可见"
          prop="configType"
        >
          <el-radio-group v-model="form.configType">
            <el-radio
              v-for="dict in sys_yes_no"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="Remark" prop="remark">
          <el-input v-model="form.remark" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">Confirm</el-button>
          <el-button @click="cancel">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Config">
  import {
    listConfig,
    getConfig,
    delConfig,
    addConfig,
    updateConfig,
    refreshCache,
  } from '@/api/system/config';

  const { proxy } = getCurrentInstance();
  const { sys_yes_no } = proxy.useDict('sys_yes_no');

  const configList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      configName: undefined,
      configKey: undefined,
      configType: undefined,
    },
    rules: {
      configName: [
        { required: true, message: '参数名称不能为空', trigger: 'blur' },
      ],
      configKey: [
        { required: true, message: '参数键名不能为空', trigger: 'blur' },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询参数列表 */
  function getList() {
    loading.value = true;
    listConfig(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        configList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      },
    );
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 表单重置 */
  function reset() {
    form.value = {
      configId: undefined,
      configName: undefined,
      configKey: undefined,
      configValue: undefined,
      configType: 'Y',
      remark: undefined,
    };
    proxy.resetForm('configRef');
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    queryParams.value.configType = '';
    proxy.resetForm('queryRef');
    handleQuery();
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.configId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = '添加参数';
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const configId = row.configId || ids.value;
    getConfig(configId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = 'Edit Config';
    });
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['configRef'].validate(valid => {
      if (valid) {
        if (form.value.configId != undefined) {
          updateConfig(form.value).then(() => {
            proxy.$modal.msgSuccess('Edited successfully');
            open.value = false;
            getList();
          });
        } else {
          addConfig(form.value).then(() => {
            proxy.$modal.msgSuccess('Added successfully');
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const configIds = row.configId || ids.value;
    proxy.$modal
      .confirm(
        'Are you sure to delete the data item with config id"' +
          configIds +
          '"？',
      )
      .then(function () {
        return delConfig(configIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('Delete successful');
      })
      .catch(() => {});
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/config/export',
      {
        ...queryParams.value,
      },
      `config_${new Date().getTime()}.xlsx`,
    );
  }
  /** 刷新缓存按钮操作 */
  function handleRefreshCache() {
    refreshCache().then(() => {
      proxy.$modal.msgSuccess('刷新缓存成功');
    });
  }

  getList();
</script>
<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
