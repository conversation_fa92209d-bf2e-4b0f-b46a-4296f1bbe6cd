<template>
  <!-- 授权用户 -->
  <el-dialog
    v-model="visible"
    title="Select User"
    width="800px"
    top="5vh"
    append-to-body
  >
    <el-form ref="queryRef" :model="queryParams" :inline="true">
      <el-form-item label="User Name" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder=""
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Phone Number" prop="phonenumber">
        <el-input
          v-model="queryParams.phonenumber"
          placeholder=""
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >Search</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table
        ref="refTable"
        :data="userList"
        height="260px"
        @row-click="clickRow"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          label="User Name"
          prop="userName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Nick Name"
          prop="nickName"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Email"
          prop="email"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Phone Number"
          prop="phonenumber"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="Status" align="center" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="Create Time"
          align="center"
          prop="createTime"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSelectUser">Confirm</el-button>
        <el-button @click="visible = false">Cancel</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="SelectUser">
  import { authUserSelectAll, unallocatedUserList } from '@/api/system/role';

  const props = defineProps({
    roleId: {
      type: [Number, String],
    },
  });

  const { proxy } = getCurrentInstance();
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

  const userList = ref([]);
  const visible = ref(false);
  const total = ref(0);
  const userIds = ref([]);

  const queryParams = reactive({
    pageNum: 1,
    pageSize: 10,
    roleId: undefined,
    userName: undefined,
    phonenumber: undefined,
  });

  // 显示弹框
  function show() {
    queryParams.roleId = props.roleId;
    getList();
    visible.value = true;
  }
  /**选择行 */
  function clickRow(row) {
    proxy.$refs['refTable'].toggleRowSelection(row);
  }
  // 多选框选中数据
  function handleSelectionChange(selection) {
    userIds.value = selection.map(item => item.userId);
  }
  // 查询表数据
  function getList() {
    unallocatedUserList(queryParams).then(res => {
      userList.value = res.rows;
      total.value = res.total;
    });
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm('queryRef');
    handleQuery();
  }
  const emit = defineEmits(['ok']);
  /** 选择授权用户操作 */
  function handleSelectUser() {
    const roleId = queryParams.roleId;
    const uIds = userIds.value.join(',');
    if (uIds == '') {
      proxy.$modal.msgError('请选择要分配的用户');
      return;
    }
    authUserSelectAll({ roleId: roleId, userIds: uIds }).then(res => {
      proxy.$modal.msgSuccess(res.msg);
      if (res.code === 200) {
        visible.value = false;
        emit('ok');
      }
    });
  }

  defineExpose({
    show,
  });
</script>
