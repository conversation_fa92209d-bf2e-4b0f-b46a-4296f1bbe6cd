<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="Role Name" prop="roleName">
          <el-input
            v-model="queryParams.roleName"
            placeholder="Please input role name"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Role Key" prop="roleKey">
          <el-input
            v-model="queryParams.roleKey"
            placeholder="Please input role key"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="status"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Create Time" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >Search</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:role:add']"
            type="primary"
            icon="Plus"
            @click="handleAdd"
            >Add</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:role:remove']"
            type="danger"
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >Delete</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:role:export']"
            type="warning"
            icon="Download"
            @click="handleExport"
            >Export</el-button
          >
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <!-- 表格数据 -->
      <el-table
        v-loading="loading"
        :data="roleList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="Role ID" prop="roleId" width="120" />
        <el-table-column
          label="Role Name"
          prop="roleName"
          :show-overflow-tooltip="true"
          min-width="150"
        />
        <el-table-column label="Remark" prop="remark" />
        <el-table-column
          label="Role Key"
          prop="roleKey"
          :show-overflow-tooltip="true"
          width="150"
        />
        <el-table-column label="Role Sort" prop="roleSort" width="100" />
        <el-table-column label="Status" align="center" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              active-value="0"
              inactive-value="1"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="Create Time"
          align="center"
          prop="createTime"
          width="200"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip content="Edit">
              <svg-icon
                v-if="scope.row.roleId !== 1"
                v-hasPermi="['system:role:edit']"
                icon-class="edits"
                class-name="meta-svg"
                @click="handleUpdate(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <el-tooltip content="Delete">
              <svg-icon
                v-if="scope.row.roleId !== 1"
                v-hasPermi="['system:role:remove']"
                icon-class="delete"
                class-name="meta-svg"
                @click="handleDelete(scope.row)"
              ></svg-icon>
            </el-tooltip>
            <!--            <el-tooltip
              v-if="scope.row.roleId !== 1"
              content="Data permissions"
              placement="top"
            >
              <el-button
                v-hasPermi="['system:role:edit']"
                link
                type="primary"
                icon="CircleCheck"
                @click="handleDataScope(scope.row)"
              ></el-button>
            </el-tooltip>
            <el-tooltip
              v-if="scope.row.roleId !== 1"
              content="User assignment"
              placement="top"
            >
              <el-button
                v-hasPermi="['system:role:edit']"
                link
                type="primary"
                icon="User"
                @click="handleAuthUser(scope.row)"
              ></el-button>
            </el-tooltip>-->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog v-model="open" :title="title" width="600px" append-to-body>
      <el-form ref="roleRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="Role Name" prop="roleName">
          <el-input
            v-model="form.roleName"
            placeholder="Please input rle name"
          />
        </el-form-item>
        <el-form-item prop="roleKey">
          <template #label>
            <span>
              <el-tooltip
                content="Permission characters defined in the controller，for example：@PreAuthorize(`@ss.hasRole('admin')`)"
                placement="top"
              >
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              <span class="font-700">Role Key</span>
            </span>
          </template>
          <el-input
            v-model="form.roleKey"
            placeholder="Please input role key"
          />
        </el-form-item>
        <el-form-item label="Role Sort" prop="roleSort">
          <el-input-number
            v-model="form.roleSort"
            controls-position="right"
            :min="0"
          />
        </el-form-item>
        <el-form-item label="Status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="Menu permissions">
          <el-checkbox
            v-model="menuExpand"
            @change="handleCheckedTreeExpand($event, 'menu')"
            >Expand/Collapse</el-checkbox
          >
          <el-checkbox
            v-model="menuNodeAll"
            @change="handleCheckedTreeNodeAll($event, 'menu')"
            >Select All/Select None</el-checkbox
          >
          <el-checkbox
            v-model="form.menuCheckStrictly"
            @change="handleCheckedTreeConnect($event, 'menu')"
            >Linkage</el-checkbox
          >
          <el-tree
            ref="menuRef"
            class="tree-border"
            :data="menuOptions"
            show-checkbox
            node-key="id"
            :check-strictly="!form.menuCheckStrictly"
            empty-text="Loading, please wait"
            :props="{ label: 'label', children: 'children' }"
          ></el-tree>
        </el-form-item>
        <el-form-item label="Remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="Please input content"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">Confirm</el-button>
          <el-button @click="cancel">Cancel</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分配角色数据权限对话框 -->
    <el-dialog
      v-model="openDataScope"
      :title="title"
      width="600px"
      append-to-body
    >
      <el-form :model="form" label-width="140px">
        <el-form-item label="Role Name">
          <el-input v-model="form.roleName" :disabled="true" />
        </el-form-item>
        <el-form-item label="Role Key">
          <el-input v-model="form.roleKey" :disabled="true" />
        </el-form-item>
        <el-form-item label="Scope of Authority">
          <el-select v-model="form.dataScope" @change="dataScopeSelectChange">
            <el-option
              v-for="item in dataScopeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-show="form.dataScope == 2" label="Data Permission">
          <el-checkbox
            v-model="deptExpand"
            @change="handleCheckedTreeExpand($event, 'dept')"
            >Expand/Collapse</el-checkbox
          >
          <el-checkbox
            v-model="deptNodeAll"
            @change="handleCheckedTreeNodeAll($event, 'dept')"
            >Select All/Select None</el-checkbox
          >
          <el-checkbox
            v-model="form.deptCheckStrictly"
            @change="handleCheckedTreeConnect($event, 'dept')"
            >Linkage</el-checkbox
          >
          <el-tree
            ref="deptRef"
            class="tree-border"
            :data="deptOptions"
            show-checkbox
            default-expand-all
            node-key="id"
            :check-strictly="!form.deptCheckStrictly"
            empty-text="Loading"
            :props="{ label: 'label', children: 'children' }"
          ></el-tree>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDataScope">Confirm</el-button>
          <el-button @click="cancelDataScope">Cancel</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Role">
  import {
    addRole,
    changeRoleStatus,
    dataScope,
    delRole,
    getRole,
    listRole,
    updateRole,
    deptTreeSelect,
  } from '@/api/system/role';
  import {
    roleMenuTreeselect,
    treeselect as menuTreeselect,
  } from '@/api/system/menu';
  import { useRouter } from 'vue-router';
  import { getCurrentInstance, nextTick, reactive, ref, toRefs } from 'vue';

  const router = useRouter();
  const { proxy } = getCurrentInstance();
  const { sys_normal_disable } = proxy.useDict('sys_normal_disable');

  const roleList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref('');
  const dateRange = ref([]);
  const menuOptions = ref([]);
  const menuExpand = ref(false);
  const menuNodeAll = ref(false);
  const deptExpand = ref(true);
  const deptNodeAll = ref(false);
  const deptOptions = ref([]);
  const openDataScope = ref(false);
  const menuRef = ref(null);
  const deptRef = ref(null);

  /** 数据范围选项*/
  const dataScopeOptions = ref([
    { value: '1', label: '全部数据权限' },
    { value: '2', label: '自定数据权限' },
    { value: '3', label: '本部门数据权限' },
    { value: '4', label: '本部门及以下数据权限' },
    { value: '5', label: '仅本人数据权限' },
  ]);

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      roleName: undefined,
      roleKey: undefined,
      status: undefined,
    },
    rules: {
      roleName: [
        { required: true, message: '角色名称不能为空', trigger: 'blur' },
      ],
      roleKey: [
        { required: true, message: '权限字符不能为空', trigger: 'blur' },
      ],
      roleSort: [
        { required: true, message: '角色顺序不能为空', trigger: 'blur' },
      ],
    },
  });

  const { queryParams, form, rules } = toRefs(data);

  /** 查询角色列表 */
  function getList() {
    loading.value = true;
    listRole(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        roleList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      },
    );
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }
  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    handleQuery();
  }
  /** 删除按钮操作 */
  function handleDelete(row) {
    const roleIds = row.roleId || ids.value;
    proxy.$modal
      .confirm(
        'Are you sure to delete the data item with role id"' + roleIds + '"?',
      )
      .then(function () {
        return delRole(roleIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('Delete successful');
      })
      .catch(() => {});
  }
  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/role/export',
      {
        ...queryParams.value,
      },
      `role_${new Date().getTime()}.xlsx`,
    );
  }
  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.roleId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }
  /** 角色状态修改 */
  function handleStatusChange(row) {
    let text = row.status === '0' ? 'Enable' : 'Disable';
    proxy.$modal
      .confirm('Confirm to "' + text + '""' + row.roleName + '" role?')
      .then(function () {
        return changeRoleStatus(row.roleId, row.status);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + ' success');
      })
      .catch(function () {
        row.status = row.status === '0' ? '1' : '0';
      });
  }
  /** 更多操作 */
  function handleCommand(command, row) {
    switch (command) {
      case 'handleDataScope':
        handleDataScope(row);
        break;
      case 'handleAuthUser':
        handleAuthUser(row);
        break;
      default:
        break;
    }
  }
  /** 分配用户 */
  function handleAuthUser(row) {
    router.push('/system/role-auth/user/' + row.roleId);
  }
  /** 查询菜单树结构 */
  function getMenuTreeselect() {
    menuTreeselect().then(response => {
      menuOptions.value = response.data;
    });
  }
  /** 所有部门节点数据 */
  function getDeptAllCheckedKeys() {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value.getCheckedKeys();
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value.getHalfCheckedKeys();
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
  }
  /** 重置新增的表单以及其他数据  */
  function reset() {
    if (menuRef.value != undefined) {
      menuRef.value.setCheckedKeys([]);
    }
    menuExpand.value = false;
    menuNodeAll.value = false;
    deptExpand.value = true;
    deptNodeAll.value = false;
    form.value = {
      roleId: undefined,
      roleName: undefined,
      roleKey: undefined,
      roleSort: 0,
      status: '0',
      menuIds: [],
      deptIds: [],
      menuCheckStrictly: true,
      deptCheckStrictly: true,
      remark: undefined,
    };
    proxy.resetForm('roleRef');
  }
  /** 添加角色 */
  function handleAdd() {
    reset();
    getMenuTreeselect();
    open.value = true;
    title.value = 'Add Role';
  }
  /** 修改角色 */
  function handleUpdate(row) {
    reset();
    const roleId = row.roleId || ids.value;
    const roleMenu = getRoleMenuTreeselect(roleId);
    getRole(roleId).then(response => {
      form.value = response.data;
      form.value.roleSort = Number(form.value.roleSort);
      open.value = true;
      nextTick(() => {
        roleMenu.then(res => {
          let checkedKeys = res.checkedKeys;
          checkedKeys.forEach(v => {
            nextTick(() => {
              menuRef.value.setChecked(v, true, false);
            });
          });
        });
      });
      title.value = 'Edit Role';
    });
  }
  /** 根据角色ID查询菜单树结构 */
  function getRoleMenuTreeselect(roleId) {
    return roleMenuTreeselect(roleId).then(response => {
      menuOptions.value = response.menus;
      return response;
    });
  }
  /** 根据角色ID查询部门树结构 */
  function getDeptTree(roleId) {
    return deptTreeSelect(roleId).then(response => {
      deptOptions.value = response.depts;
      return response;
    });
  }
  /** 树权限（展开/折叠）*/
  function handleCheckedTreeExpand(value, type) {
    if (type === 'menu') {
      let treeList = menuOptions.value;
      for (let i = 0; i < treeList.length; i++) {
        menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
      }
    } else if (type === 'dept') {
      let treeList = deptOptions.value;
      for (let i = 0; i < treeList.length; i++) {
        deptRef.value.store.nodesMap[treeList[i].id].expanded = value;
      }
    }
  }
  /** 树权限（全选/全不选） */
  function handleCheckedTreeNodeAll(value, type) {
    if (type === 'menu') {
      menuRef.value.setCheckedNodes(value ? menuOptions.value : []);
    } else if (type === 'dept') {
      deptRef.value.setCheckedNodes(value ? deptOptions.value : []);
    }
  }
  /** 树权限（父子联动） */
  function handleCheckedTreeConnect(value, type) {
    if (type == 'menu') {
      form.value.menuCheckStrictly = value ? true : false;
    } else if (type == 'dept') {
      form.value.deptCheckStrictly = value ? true : false;
    }
  }
  /** 所有菜单节点数据 */
  function getMenuAllCheckedKeys() {
    // 目前被选中的菜单节点
    let checkedKeys = menuRef.value.getCheckedKeys();
    // 半选中的菜单节点
    let halfCheckedKeys = menuRef.value.getHalfCheckedKeys();
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs['roleRef'].validate(valid => {
      if (valid) {
        if (form.value.roleId !== undefined) {
          form.value.menuIds = getMenuAllCheckedKeys();
          updateRole(form.value).then(() => {
            proxy.$modal.msgSuccess('Modified successfully');
            open.value = false;
            getList();
          });
        } else {
          form.value.menuIds = getMenuAllCheckedKeys();
          addRole(form.value).then(() => {
            proxy.$modal.msgSuccess('Add successfully');
            open.value = false;
            getList();
          });
        }
      }
    });
  }
  /** 取消按钮 */
  function cancel() {
    open.value = false;
    reset();
  }
  /** 选择角色权限范围触发 */
  function dataScopeSelectChange(value) {
    if (value !== '2') {
      deptRef.value.setCheckedKeys([]);
    }
  }
  /** 分配数据权限操作 */
  function handleDataScope(row) {
    reset();
    const deptTreeSelect = getDeptTree(row.roleId);
    getRole(row.roleId).then(response => {
      form.value = response.data;
      openDataScope.value = true;
      nextTick(() => {
        deptTreeSelect.then(res => {
          nextTick(() => {
            if (deptRef.value) {
              deptRef.value.setCheckedKeys(res.checkedKeys);
            }
          });
        });
      });
      title.value = 'Assign data permissions';
    });
  }
  /** 提交按钮（数据权限） */
  function submitDataScope() {
    if (form.value.roleId != undefined) {
      form.value.deptIds = getDeptAllCheckedKeys();
      dataScope(form.value).then(() => {
        proxy.$modal.msgSuccess('Modified successfully');
        openDataScope.value = false;
        getList();
      });
    }
  }
  /** 取消按钮（数据权限）*/
  function cancelDataScope() {
    openDataScope.value = false;
    reset();
  }

  getList();
</script>
<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
