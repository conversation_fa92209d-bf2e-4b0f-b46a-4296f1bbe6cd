<template>
  <div class="app-container">
    <div class="card">
      <el-form
        v-show="showSearch"
        ref="queryRef"
        :model="queryParams"
        :inline="true"
      >
        <el-form-item label="System" prop="system">
          <el-select
            v-model="queryParams.system"
            clearable
            style="width: 200px"
            @change="testChange"
          >
            <el-option value="ADMIN" label="ADMIN">Admin</el-option>
            <el-option value="APP" label="APP">APP</el-option>
            <el-option value="QC" label="QC">QC</el-option>
            <el-option value="OTHER" label="OTHER">Other</el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="Module 1" prop="module1">
          <el-input
            v-model="queryParams.module1"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="Module 2" prop="module2">
          <el-input
            v-model="queryParams.module2"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="Module 3" prop="module3">
          <el-input
            v-model="queryParams.module3"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="Operator" prop="operName">
          <el-input
            v-model="queryParams.operName"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="Type" prop="businessType">
          <el-select
            v-model="queryParams.businessType"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_oper_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Operation IP" prop="operIp">
          <el-input
            v-model="queryParams.operIp"
            clearable
            style="width: 240px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>

        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 240px"
          >
            <el-option
              v-for="dict in sys_common_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Operation Time" style="width: 308px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 1, 1, 23, 59, 59),
            ]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:operlog:remove']"
            type="danger"
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            >Delete
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:operlog:remove']"
            type="danger"
            icon="Delete"
            @click="handleClean"
            >Clear
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
            v-hasPermi="['system:operlog:export']"
            type="warning"
            icon="Download"
            @click="handleExport"
            >Export
          </el-button>
        </el-col>
        <right-toolbar
          v-model:showSearch="showSearch"
          @query-table="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        ref="operlogRef"
        v-loading="loading"
        :data="operlogList"
        :default-sort="defaultSort"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="ID" align="center" prop="operId" width="100" />
        <el-table-column
          label="System"
          align="center"
          width="80"
          prop="system"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Module 1"
          align="center"
          prop="module1"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Module 2"
          align="center"
          prop="module2"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Module 3"
          align="center"
          prop="module3"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Description"
          align="center"
          prop="description"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="Type"
          align="center"
          width="100"
          prop="businessType"
        >
          <template #default="scope">
            <dict-tag
              :options="sys_oper_type"
              :value="scope.row.businessType"
            />
          </template>
        </el-table-column>
        <!--        <el-table-column
          label="Request Method"
          align="center"
          prop="requestMethod"
        />-->
        <el-table-column
          label="Operator"
          align="center"
          prop="operName"
          width="200"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        />
        <!--<el-table-column
          label="Operation IP"
          align="center"
          prop="operIp"
          width="130"
          :show-overflow-tooltip="true"
        />-->
        <el-table-column label="Status" align="center" width="80" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_common_status" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column
          label="Operation Time"
          align="center"
          prop="operTime"
          width="160"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.operTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Consume Time "
          align="center"
          prop="costTime"
          width="140"
          :show-overflow-tooltip="true"
          sortable="custom"
          :sort-orders="['descending', 'ascending']"
        >
          <template #default="scope">
            <span>{{ scope.row.costTime }}ms</span>
          </template>
        </el-table-column>
        <el-table-column
          label="Operate"
          align="center"
          width="90"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-tooltip v-hasPermi="['system:operlog:query']" content="View">
              <svg-icon
                icon-class="view"
                class-name="meta-svg"
                @click="handleView(scope.row, scope.index)"
              ></svg-icon>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        @pagination="getList"
      />
    </div>

    <!-- 操作日志详细 -->
    <el-dialog v-model="open" title="Detail" width="1200px" append-to-body>
      <el-form :model="form" label-width="180px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="Operation Module："
              >{{ form.system }} / {{ typeFormat(form) }}
            </el-form-item>
            <el-form-item label="Login Information："
              >{{ form.operName }} / {{ form.operIp }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Request URL："
              >{{ form.operUrl }}
            </el-form-item>
            <el-form-item label="Request Method："
              >{{ form.requestMethod }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Module1">{{ form.module1 }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Module2">{{ form.module2 }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="Module3">{{ form.module3 }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Operation Method："
              >{{ form.method }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="Operation Args："
              style="word-break: break-all; word-wrap: break-word"
              >{{ form.operArgs }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="Operation Parameter："
              style="word-break: break-all; word-wrap: break-word"
              >{{ form.operParam }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="Json Result："
              style="word-break: break-all; word-wrap: break-word"
              >{{ form.jsonResult }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Status：">
              <div v-if="form.status === 0">normal</div>
              <div v-else-if="form.status === 1">fail</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Consume Time："
              >{{ form.costTime }}ms
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Operation Time："
              >{{ parseTime(form.operTime) }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item v-if="form.status === 1" label="Error Message："
              >{{ form.errorMsg }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="text-center">
          <el-button @click="open = false">Close</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Operlog">
  import { cleanOperlog, delOperlog, list } from '@/api/system/operlog';
  import { getCurrentInstance, reactive, ref, toRefs } from 'vue';

  const { proxy } = getCurrentInstance();
  const { sys_oper_type, sys_common_status } = proxy.useDict(
    'sys_oper_type',
    'sys_common_status',
  );

  const operlogList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const multiple = ref(true);
  const total = ref(0);
  const dateRange = ref([]);
  const defaultSort = ref({ prop: 'operTime', order: 'descending' });

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 20,
      operIp: undefined,
      system: undefined,
      module1: undefined,
      module2: undefined,
      module3: undefined,
      operName: undefined,
      businessType: undefined,
      status: undefined,
    },
  });

  const { queryParams, form } = toRefs(data);

  /** 查询登录日志 */
  function getList() {
    loading.value = true;
    list(proxy.addDateRange(queryParams.value, dateRange.value)).then(
      response => {
        operlogList.value = response.rows;
        total.value = response.total;
        loading.value = false;
      },
    );
  }

  function testChange() {
    // console.log(queryParams.value.system);
  }

  /** 操作日志类型字典翻译 */
  function typeFormat(row) {
    return proxy.selectDictLabel(sys_oper_type.value, row.businessType);
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    dateRange.value = [];
    proxy.resetForm('queryRef');
    queryParams.value.pageNum = 1;
    proxy.$refs['operlogRef'].sort(
      defaultSort.value.prop,
      defaultSort.value.order,
    );
  }

  /** 多选框选中数据 */
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.operId);
    multiple.value = !selection.length;
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    queryParams.value.orderByColumn = column.prop;
    queryParams.value.isAsc = column.order;
    getList();
  }

  /** 详细按钮操作 */
  function handleView(row) {
    open.value = true;
    form.value = row;
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const operIds = row.operId || ids.value;
    proxy.$modal
      .confirm(
        'Are you sure to delete the log number as "' +
          operIds +
          '" data items?',
      )
      .then(function () {
        return delOperlog(operIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('Delete successful');
      })
      .catch(() => {});
  }

  /** 清空按钮操作 */
  function handleClean() {
    proxy.$modal
      .confirm('Are you sure to clear all operation log data items?')
      .then(function () {
        return cleanOperlog();
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess('Clear successfully');
      })
      .catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download(
      'system/operlog/export',
      {
        ...queryParams.value,
      },
      `config_${new Date().getTime()}.xlsx`,
    );
  }

  getList();
</script>

<style lang="scss" scoped>
  .meta-svg {
    width: 20px;
    height: 20px;
    margin-right: 0.5rem;
    cursor: pointer;
  }
</style>
