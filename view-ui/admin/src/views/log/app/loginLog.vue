<template>
  <div class="app-container">
    <div class="card list">
      <el-form ref="formRef" :model="queryParams" :inline="true">
        <el-form-item label="Login Time">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            style="width: 220px"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="User Email" prop="userEmail">
          <el-input
            v-model="queryParams.userEmail"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item label="IP" prop="ip">
          <el-input
            v-model="queryParams.ip"
            clearable
            style="width: 220px"
            @keyup.enter="getDataList"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%; margin-bottom: 20px"
        :header-cell-style="{
          backgroundColor: '#f2f2f2',
          color: '#333333',
          fontWeight: 700,
        }"
        border
        :default-sort="defaultSort"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="loginTime" label="Time" width="160">
          <template #default="scope"
            >{{ parseTime(scope.row.loginTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="userId"
          label="User ID"
          show-overflow-tooltip
          width="260"
        />
        <el-table-column prop="userEmail" label="User Email" width="200" />
        <el-table-column prop="ip" label="IP" width="120" />
        <el-table-column prop="country" label="Country" width="120" />
        <el-table-column prop="browser" label="Browser" show-overflow-tooltip />
      </el-table>

      <pagination
        v-show="total > 0"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        :total="total"
        class="mb-1"
        @pagination="getDataList"
      />
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, toRefs } from 'vue';
  import { listMemberLoginInfo } from '@/api/log/member';

  const { proxy } = getCurrentInstance();

  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      userEmail: '',
      ip: '',
      pageNum: 1,
      pageSize: 20,
      orderByColumn: 'loginTime',
      isAsc: 'descending',
    },
    dateRange: [],
    loading: true,
    defaultSort: { prop: 'createDate', order: 'descending' },
  });

  /** 解构 */
  const { tableData, total, queryParams, dateRange, loading, defaultSort } =
    toRefs(data);

  onMounted(() => {
    getDataList();
  });

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listMemberLoginInfo(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  function resetQuery() {
    dateRange.value = [];
    proxy.$refs['formRef'].resetFields();
    getDataList();
  }
</script>

<style lang="scss" scoped></style>
<style lang="scss">
  .el-popper {
    max-width: 350px !important;
  }
</style>
