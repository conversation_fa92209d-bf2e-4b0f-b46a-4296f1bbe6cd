<template>
  <div class="app-container">
    <div class="card">
      <el-form ref="searchForm" :model="queryParams" :inline="true">
        <el-form-item label="Share No" prop="shareNo">
          <el-input
            v-model="queryParams.shareNo"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Share From" prop="sourceEmail">
          <el-input
            v-model="queryParams.sourceEmail"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Share To" prop="targetEmail">
          <el-input
            v-model="queryParams.targetEmail"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Type ID" prop="projNo">
          <el-input
            v-model="queryParams.projNo"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 150px"
          >
            <el-option value="sharing" label="Sharing">Sharing</el-option>
            <el-option value="cancled" label="Canceled">Canceled</el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Year" prop="year">
          <el-select v-model="queryParams.year" clearable style="width: 150px">
            <el-option
              v-for="item in yearOpt"
              :key="`shareYear-${item.value}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>
      <el-divider class="mt-05"></el-divider>

      <div v-if="shareList && shareList.length !== 0" v-loading="loading">
        <div
          v-for="(share, index) in shareList"
          :key="`shareList-${index}`"
          class="mt-1"
        >
          <div class="d-flex align-items-center justify-space-between">
            <div class="d-flex align-items-center">
              <el-tag type="warning" round class="mr-1">SHARE</el-tag>
              <el-tag
                v-if="!share.see"
                type="danger"
                effect="dark"
                round
                size="small"
                class="mr-05"
                >new
              </el-tag>
              <span class="font-600 text-warning mr-1">
                {{ share.shareId }}
              </span>
              <el-tooltip
                placement="right"
                :width="20"
                trigger="hover"
                content="Data List"
              >
                <svg-icon
                  icon-class="dataIdList"
                  class-name="svg-idList"
                  @click="expandDataList(index)"
                ></svg-icon>
              </el-tooltip>
              <el-tag
                v-if="share.status === 'sharing'"
                type="warning"
                round
                class="ml-2"
                effect="plain"
                >Sharing</el-tag
              >
              <el-tag
                v-if="share.status === 'cancled'"
                type="danger"
                round
                class="ml-2"
                effect="plain"
                >Canceled</el-tag
              >
            </div>
            <div class="font-600 text-main-color float-right">
              <el-button
                v-if="share.status === 'sharing'"
                type="danger"
                round
                size="small"
                @click="setCancelShareId(share.id)"
              >
                <span class="font-600">Cancel</span>
              </el-button>

              <!--<el-button
                round
                size="small"
                type="primary"
                @click="exportDataLink(share.shareId)"
              >
                <span class="font-600">Export Data Links</span>
              </el-button>-->
            </div>
          </div>

          <!--Data ID List-->
          <el-collapse-transition>
            <div
              v-if="shareList[index].expand"
              class="radius-12 bg-gray p-15 mt-05 list"
            >
              <el-row v-if="shareList[index]?.expandData?.projNos" :gutter="20">
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    Project Counts
                  </span>
                  <el-tag type="success" class="tag-success" size="small"
                    >{{ shareList[index]?.expandData?.projNos?.length }}
                  </el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in shareList[index]?.expandData?.projNos"
                    :key="'projNos-' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-project">P</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('project', share.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>

              <el-row v-if="shareList[index]?.expandData?.analNos" :gutter="20">
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    Analysis Counts
                  </span>
                  <el-tag type="success" class="tag-success" size="small"
                    >{{ shareList[index]?.expandData?.analNos.length }}
                  </el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in shareList[index]?.expandData?.analNos"
                    :key="'analNos-' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-project">A</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('analysis', share.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>

              <el-row v-if="shareList[index]?.expandData?.expNos" :gutter="20">
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    Experiment Counts
                  </span>
                  <el-tag type="success" class="tag-success" size="small"
                    >{{ shareList[index]?.expandData?.expNos.length }}
                  </el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in shareList[index]?.expandData?.expNos"
                    :key="'expNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-experiment">X</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('experiment', share.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>

              <el-row v-if="shareList[index]?.expandData?.sapNos" :gutter="20">
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    Sample Counts
                  </span>
                  <el-tag type="success" class="tag-success" size="small"
                    >{{ shareList[index]?.expandData?.sapNos.length }}
                  </el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in shareList[index]?.expandData?.sapNos"
                    :key="'sapNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-sample">S</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('sample', share.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>

              <el-row v-if="shareList[index]?.expandData?.runNos" :gutter="20">
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    Run Counts
                  </span>
                  <el-tag type="success" class="tag-success" size="small"
                    >{{ shareList[index]?.expandData?.runNos.length }}
                  </el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in shareList[index]?.expandData?.runNos"
                    :key="'runNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-run">R</span>
                    <a
                      href="javascript:void(0)"
                      @click="showDetail('run', share.creator, id)"
                    >
                      {{ id }}
                    </a>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>

              <el-row v-if="shareList[index]?.expandData?.dataNos" :gutter="20">
                <el-col :span="4">
                  <span class="text-secondary-color count-type font-600 mr-05">
                    Data Counts
                  </span>
                  <el-tag type="success" class="tag-success" size="small"
                    >{{ shareList[index]?.expandData?.dataNos.length }}
                  </el-tag>
                </el-col>
                <el-col :span="20" class="d-flex row-gap-10 flex-wrap">
                  <div
                    v-for="id in shareList[index]?.expandData?.dataNos"
                    :key="'dataNos' + id"
                    class="id-list mr-1"
                  >
                    <span class="btn-data">D</span>
                    <span>{{ id }}</span>
                  </div>
                </el-col>
                <el-divider class="mg-divider"></el-divider>
              </el-row>
            </div>
          </el-collapse-transition>

          <div class="radius-12 bg-gray mt-05 d-flex">
            <div class="d-flex align-items-center mr-2">
              <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05">Share Date:</span>
              <span class="text-other-color">{{ share.shareDate }}</span>
            </div>
            <div class="d-flex align-items-center">
              <template v-if="share.shareFromEmail">
                <svg-icon icon-class="shareEmail" class-name="svg"></svg-icon>
                <span class="text-other-color mr-05 ml-05"
                  >Share From Email:</span
                >
                <span class="text-other-color">{{ share.shareFromEmail }}</span>
              </template>
              <template v-if="share.shareToEmails">
                <svg-icon
                  icon-class="shareEmail"
                  class-name="svg"
                  class="ml-2"
                ></svg-icon>
                <span class="text-other-color mr-05 ml-05"
                  >Share To Email:</span
                >
                <span class="text-other-color">{{
                  share.shareToEmails ? share.shareToEmails.join(', ') : ''
                }}</span>
              </template>
            </div>
          </div>
          <el-divider></el-divider>
        </div>

        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          class="mb-1"
          @pagination="getDataList"
        />
      </div>

      <div v-else v-loading="loading">
        <el-empty></el-empty>
      </div>

      <el-dialog
        v-model="cancelShareDialog"
        title="Are you sure to cancel sharing?"
        width="400"
        center
        class="round-dialog"
      >
        <template #footer>
          <div>
            <el-button size="small" type="primary" @click="doCancelShare">
              Confirm
            </el-button>
            <el-button size="small" @click="cancelShareDialog = false"
              >Cancel
            </el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    cancelShare,
    getMyShareList,
    getShareDataList,
  } from '@/api/share/share';
  import { createAccessToken } from '@/api/login';

  const { proxy } = getCurrentInstance();

  const cancelShareDialog = ref(false);
  const cancelShareId = ref('');
  const sortBtn = reactive([
    {
      label: 'Share Date',
      field: 'share_date',
      highlighted: true,
      sortOrder: 'descending',
    },
    {
      label: 'Share ID',
      field: 'share_id',
      highlighted: false,
      sortOrder: 'descending',
    },
    {
      label: 'Status',
      field: 'status',
      highlighted: false,
      sortOrder: 'descending',
    },
  ]);

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      year: undefined,
      shareNo: undefined,
      projNo: undefined,
      status: undefined,
      sourceEmail: undefined,
      targetEmail: undefined,
    },
  });

  /** 解构 */
  const { total, queryParams } = toRefs(data);

  // 左上角排序
  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
  });

  const loading = ref(false);
  const shareList = ref([]);

  const yearOpt = ref([]);

  const expandDataList = index => {
    let item = shareList.value[index];

    // 已经展开，点击则收起
    if (item?.expand) {
      item.expandData = [];
      item.expand = false;
      return;
    }
    getShareDataList({ id: item.id }).then(response => {
      item.expandData = response.data;
      item.expand = true;
      item.see = true;
    });
  };

  /** 导出页面中data的下载链接 */
  function exportDataLink(id) {
    let type = 'share';
    proxy.download(
      `/download/node/exportDownloadLink/${type}/${id}`,
      null,
      `${type}_${id}_data_download_link.zip`,
    );
  }

  function setCancelShareId(id) {
    cancelShareDialog.value = true;
    cancelShareId.value = id;
  }

  function doCancelShare() {
    cancelShare({ id: cancelShareId.value }).then(() => {
      proxy.$modal.msgSuccess('Cancel successful');
      cancelShareDialog.value = false;
      getDataList();
    });
  }

  function getRecentYears() {
    let currentYear = new Date().getFullYear();

    for (let i = 0; i < 3; i++) {
      yearOpt.value.push({ value: currentYear - i, label: currentYear - i });
    }

    const lastYear = yearOpt.value[yearOpt.value.length - 1].value;
    yearOpt.value.push({
      value: '<' + lastYear,
      label: 'before ' + lastYear,
    });
  }

  function getDataList() {
    const params = {
      sourceEmail: queryParams.value.sourceEmail,
      shareNo: queryParams.value.shareNo,
      projNo: queryParams.value.projNo,
      status: queryParams.value.status,
      year: queryParams.value.year,
      targetEmail: queryParams.value.targetEmail,
      orderByColumn: queryPageAndSort.value.sortKey,
      isAsc: queryPageAndSort.value.sortType,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    loading.value = true;
    getMyShareList(params)
      .then(response => {
        shareList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    proxy.resetForm('searchForm');
    getDataList();
  }

  function showDetail(type, creator, typeNo) {
    // 预先生成access_token
    createAccessToken({ memberId: creator }).then(response => {
      const token = response.data;
      let href = `${
        import.meta.env.VITE_APP_WEB_URL
      }/${type}/detail/${typeNo}?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }

  onMounted(() => {
    getRecentYears();
    getDataList();
  });
</script>

<style lang="scss" scoped>
  .list {
    .count-type {
      display: inline-block;
      width: 129px;
    }
  }

  .bg-primary {
    background-color: #f5f8fe;

    .search {
      padding: 6px 10px;
    }
  }

  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }

  :deep(.el-tag__content) {
    font-weight: 600;
  }

  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }

  .svg {
    width: 16px;
    height: 16px;
  }

  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .bg-gray {
    padding: 6px 15px;
  }

  .cancle-share {
    width: 68px;
    background-color: #f4f4f5 !important;
    color: gray !important;
    border-color: gray !important;
  }

  .btn-round-warning:focus {
    background-color: #feeee4;
    color: #fe7f2b;
    border: 1px solid #fe7f2b;
  }

  .round-dialog {
    border-radius: 24px !important;
  }

  :deep(.round-dialog .el-dialog__body) {
    padding: 0 !important;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }

  .mg-divider {
    margin: 10px;
  }
</style>
