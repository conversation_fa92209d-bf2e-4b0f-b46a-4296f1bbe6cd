<template>
  <div class="app-container">
    <div class="card">
      <el-form ref="searchForm" :model="queryParams" :inline="true">
        <el-form-item label="Owner" prop="sourceEmail">
          <el-input
            v-model="queryParams.sourceEmail"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Requester" prop="targetEmail">
          <el-input
            v-model="queryParams.targetEmail"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Type ID" prop="projNo">
          <el-input
            v-model="queryParams.projNo"
            clearable
            style="width: 150px"
            @keyup.enter="getDataList"
          ></el-input>
        </el-form-item>
        <el-form-item label="Status" prop="status">
          <el-select
            v-model="queryParams.status"
            clearable
            style="width: 150px"
          >
            <el-option value="Authorized" label="Authorized"></el-option>
            <el-option value="Requesting" label="Requesting"></el-option>
            <el-option value="Declined" label="Declined"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="Year" prop="year">
          <el-select v-model="queryParams.year" clearable style="width: 150px">
            <el-option
              v-for="item in yearOpt"
              :key="`shareYear-${item.value}`"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getDataList"
            >Search
          </el-button>
          <el-button icon="Refresh" @click="resetQuery">Reset</el-button>
        </el-form-item>
      </el-form>
      <el-divider class="mt-05"></el-divider>

      <div
        v-if="requestList && requestList.length !== 0"
        v-loading="loading"
        class="mt-1"
      >
        <div v-for="(item, index) in requestList" :key="'requestList-' + index">
          <div class="d-flex align-items-center justify-space-between">
            <div class="d-flex align-items-center">
              <el-tag
                type="warning"
                round
                class="tag-proj tag-warning mr-1"
                :class="computedTagClass(item.typeId)"
                >{{ tagType(computedTagClass(item.typeId)) }}
              </el-tag>
              <el-tag
                v-if="!item.see"
                type="danger"
                effect="dark"
                round
                size="small"
                class="mr-05"
                >new
              </el-tag>
              <span
                class="font-600 text-warning mr-1 cursor-pointer"
                @click="toDetail(item)"
              >
                {{ item.typeId }}
              </span>
              <el-tooltip
                :width="20"
                placement="right"
                trigger="hover"
                content="Data List"
              >
                <svg-icon
                  icon-class="dataIdList"
                  class-name="svg-idList"
                  @click="expandDataList(index)"
                ></svg-icon>
              </el-tooltip>
              <el-tag
                :type="statusClass(item.status)"
                round
                class="ml-2"
                effect="plain"
                >{{ item.status }}</el-tag
              >
            </div>
            <div class="font-600 text-main-color d-flex align-items-center">
              <div v-if="item.status === 'Requesting'">
                <el-button
                  class="btn-round-success ml-05"
                  plain
                  type="success"
                  round
                  size="small"
                  @click="changePass(item.id, true)"
                >
                  <span class="font-600">Authorized</span>
                </el-button>
                <el-button
                  class="btn-round-danger mr-05"
                  plain
                  type="danger"
                  round
                  size="small"
                  @click="changePass(item.id, false)"
                >
                  <span class="font-600">Declined</span>
                </el-button>
              </div>
            </div>
          </div>

          <!--data List-->
          <el-collapse-transition>
            <div v-show="item.expand" class="p-15 mt-05">
              <el-table
                :data="item.dataList"
                style="width: 100%; margin-bottom: 20px"
                :header-cell-style="{
                  backgroundColor: '#f2f2f2',
                  color: '#333333',
                  fontWeight: 700,
                }"
                max-height="200"
                border
                :sort-orders="['ascending', 'descending']"
                tooltip-effect="dark"
                @sort-change="
                  column => {
                    relTableSortChange(column, index);
                  }
                "
              >
                <el-table-column
                  width="120"
                  prop="datNo"
                  label="Data ID"
                  sortable
                >
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="Name"
                  sortable
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  v-if="!item.analysisData"
                  prop="expName"
                  label="Experiment"
                  sort-by="expNo"
                  sortable
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <a
                      class="text-primary"
                      href="javascript:void(0)"
                      @click="
                        showDetail('experiment', item.owner, scope.row.expNo)
                      "
                    >
                      {{ scope.row.expNo }} ({{ scope.row.expName }})
                    </a>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="!item.analysisData"
                  prop="sapName"
                  label="Sample"
                  sort-by="sapNo"
                  sortable
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <a
                      class="text-primary"
                      href="javascript:void(0)"
                      @click="showDetail('sample', item.owner, scope.row.sapNo)"
                    >
                      {{ scope.row.sapNo }} ({{ scope.row.sapName }})
                    </a>
                  </template>
                </el-table-column>
                <el-table-column
                  v-if="!item.analysisData"
                  prop="runName"
                  label="Run"
                  sort-by="runNo"
                  sortable
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <a
                      class="text-primary"
                      href="javascript:void(0)"
                      @click="showDetail('run', item.owner, scope.row.runNo)"
                    >
                      {{ scope.row.runNo }} ({{ scope.row.runName }})
                    </a>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="item.analysisData"
                  label="Analysis"
                  sortable
                  sort-by="analNo"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    <a
                      class="text-primary"
                      href="javascript:void(0)"
                      @click="
                        showDetail('analysis', item.owner, scope.row.analNo)
                      "
                    >
                      {{ scope.row.analNo }} ({{ scope.row.analName }})
                    </a>
                  </template>
                </el-table-column>
              </el-table>

              <pagination
                v-if="item.dataList && item.dataList.length > 100"
                v-model:page="requestList[index].pageNum"
                :limit="100"
                layout="total, prev, pager, next"
                class="mb-1 mt-2 justify-center"
                :total="requestList[index].totalCount"
                @pagination="
                  pageData => {
                    pageDataList(pageData, index);
                  }
                "
              />
            </div>
          </el-collapse-transition>

          <div class="mt-05 d-flex">
            <div
              v-if="item.status === 'Authorized' && item.expireDate"
              class="d-flex align-items-center mr-2"
            >
              <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05">Expiry Date:</span>
              <span class="text-other-color">{{ item.expireDate }}</span>
            </div>

            <div v-if="item.applyDate" class="d-flex align-items-center mr-2">
              <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05">Request Date:</span>
              <span class="text-other-color">{{ item.applyDate }}</span>
            </div>

            <div
              v-if="item.status !== 'Requesting' && item.replyDate"
              class="d-flex align-items-center mr-2"
            >
              <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
              <span class="text-other-color mr-05 ml-05"
                >Authorization Date:</span
              >
              <span class="text-other-color">{{ item.replyDate }}</span>
            </div>

            <div v-if="item.ownerName" class="d-flex align-items-center mr-2">
              <el-icon color="#07BCB4">
                <Avatar />
              </el-icon>
              <span class="text-other-color mr-05 ml-05">Owner:</span>
              <span class="text-other-color">{{ item.ownerName }}</span>
            </div>

            <div v-if="item.requestor" class="d-flex align-items-center mr-2">
              <el-icon color="#07BCB4">
                <Avatar />
              </el-icon>
              <span class="text-other-color mr-05 ml-05">Requestor:</span>
              <span class="text-other-color">{{ item.requestor }}</span>
            </div>
          </div>

          <div class="bg-gray request-list mt-05 list">
            <div>
              <span class="text-secondary-color count-type font-600 mr-05">
                Request Text
              </span>
              <span class="text-secondary-color mr-05">
                {{ item.description }}</span
              >
            </div>
            <el-divider class="mt-05 mb-05"></el-divider>
            <div>
              <span class="text-secondary-color count-type font-600 mr-05">
                Reply Text</span
              >
              <span class="text-secondary-color mr-05">
                {{ item.replyText }}</span
              >
            </div>
          </div>
          <el-divider></el-divider>
        </div>

        <pagination
          v-show="total > 0"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          :total="total"
          class="mb-1"
          @pagination="getDataList"
        />

        <el-dialog
          v-model="declineDialog"
          title="Comment"
          width="600"
          class="radius-14"
        >
          <el-input v-model="passComment" type="textarea" :rows="10"></el-input>
          <template #footer>
            <div class="dialog-footer text-center">
              <el-button type="primary" @click="savePass"> Save</el-button>
              <el-button @click="declineDialog = false">Cancel</el-button>
            </div>
          </template>
        </el-dialog>
      </div>
      <div v-else v-loading="loading">
        <el-empty></el-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    getApplyRequestList,
    getRequestDataList,
    saveRequestStatus,
    updateSee,
  } from '@/api/share/request';
  import { useRouter } from 'vue-router';
  import { createAccessToken } from '@/api/login';

  const { proxy } = getCurrentInstance();
  const router = useRouter();

  const yearOpt = ref([]);

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  /** 解构 */
  const { total, queryParams } = toRefs(data);

  const sortBtn = reactive([
    {
      label: 'Request Date',
      field: 'apply_date',
      highlighted: true,
      sortOrder: 'descending',
    },
    {
      label: 'Main ID',
      field: 'type_id',
      highlighted: false,
      sortOrder: 'ascending',
    },
    {
      label: 'Status',
      field: 'status',
      highlighted: false,
      sortOrder: 'ascending',
    },
  ]);

  // 左上角排序
  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
  });

  // 展开的表格排序
  function relTableSortChange(column, index) {
    let { prop, order } = column;
    if (order) {
      requestList.value[index].sortKey = prop;
      requestList.value[index].sortType =
        order === 'ascending' ? 'ascending' : 'descending';
      getItemRequestDataList(index);
    }
  }

  // 展示data list
  const expandDataList = index => {
    let item = requestList.value[index];

    // 已经展开，点击则收起
    if (item?.expand) {
      item.expand = false;
      return;
    }

    getItemRequestDataList(index);
  };

  // 查询具体data数据的列表
  function getItemRequestDataList(index) {
    let item = requestList.value[index];
    item.analysisData = item.type === 'analysis';

    const pageNum = item.pageNum || 1;

    let params = {
      requestId: item.id,
      sortKey: item.sortKey,
      sortType: item.sortType,
      pageNum: pageNum,
      pageSize: 100, // 循环多分页，不方便做page size的切换，固定limit
    };

    getRequestDataList(params).then(response => {
      item.dataList = response.data?.dataVos || [];
      item.totalCount = response.data?.total || 0;
      item.pageNum = pageNum;
      item.expand = true;
    });
  }

  /** 展开的data list数据分页 */
  function pageDataList(pageData, index) {
    requestList.value[index].pageSize = pageData.limit;
    requestList.value[index].pageNum = pageData.page;
    getItemRequestDataList(index);
  }

  // 生成最近3年内的查询下拉词
  function getRecentYears() {
    let currentYear = new Date().getFullYear();

    for (let i = 0; i < 3; i++) {
      yearOpt.value.push({ value: currentYear - i, label: currentYear - i });
    }

    const lastYear = yearOpt.value[yearOpt.value.length - 1].value;
    yearOpt.value.push({
      value: '<' + lastYear,
      label: 'before ' + lastYear,
    });
  }

  const requestList = ref([]);
  const loading = ref(false);

  // 查询外层的请求列表
  function getDataList() {
    const params = {
      sourceEmail: queryParams.value.sourceEmail,
      projNo: queryParams.value.projNo,
      status: queryParams.value.status,
      year: queryParams.value.year,
      targetEmail: queryParams.value.targetEmail,
      orderByColumn: queryPageAndSort.value.sortKey,
      isAsc: queryPageAndSort.value.sortType,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    loading.value = true;
    getApplyRequestList(params)
      .then(response => {
        requestList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function resetQuery() {
    proxy.resetForm('searchForm');
    getDataList();
  }

  /** 通过或者拒绝请求 */
  const declineDialog = ref(false);
  const requestId = ref('');
  const pass = ref(false);
  const passComment = ref('');

  /** 弹出通过或者拒绝请求的弹框  */
  function changePass(id, passStatus) {
    declineDialog.value = true;
    requestId.value = id;
    pass.value = passStatus;
  }

  function savePass() {
    const params = {
      id: requestId.value,
      status: pass.value,
      comment: passComment.value,
    };

    proxy.$modal.loading('Saving');
    saveRequestStatus(params)
      .then(response => {
        requestList.value = response.data;
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        declineDialog.value = false;

        getDataList();
      });
  }

  // 跳转到数据详情，并且将数据更新为已读
  function toDetail(item) {
    updateSee(item.id).then(() => {
      router.push(`/${item.type}/detail/${item.typeId}`);
    });
  }

  const computedTagClass = id => {
    if (id.includes('OEP')) {
      return 'tag-proj';
    } else if (id.includes('OES')) {
      return 'tag-samp';
    } else if (id.includes('OEX')) {
      return 'tag-expr';
    } else {
      return 'tag-anal';
    }
  };

  const statusClass = status => {
    if (status === 'Authorized') {
      return 'success';
    } else if (status === 'Requesting') {
      return 'primary';
    } else if (status === 'Declined') {
      return 'danger';
    }
  };

  const tagType = tag => {
    const parts = tag.split('-');
    return parts[parts.length - 1].toUpperCase();
  };

  function showDetail(type, creator, typeNo) {
    // 预先生成access_token
    createAccessToken({ memberId: creator }).then(response => {
      const token = response.data;
      let href = `${
        import.meta.env.VITE_APP_WEB_URL
      }/${type}/detail/${typeNo}?access-token=${token}`;
      // 打开一个新页面
      window.open(href);
    });
  }

  onMounted(() => {
    getRecentYears();
    getDataList();
  });
</script>

<style lang="scss" scoped>
  .list {
    .count-type {
      display: inline-block;
      width: 100px;
    }
  }

  .request-list {
    border-left: 2px solid #79ce78;

    .reply-text {
      border-radius: 3px;
      border: 1px solid #eee;
      background-color: #f3f3f3;
    }
  }

  .bg-primary {
    background-color: #f5f8fe;

    .search {
      padding: 6px 10px;
    }
  }

  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }

  :deep(.el-tag__content) {
    font-weight: 600;
  }

  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }

  .svg {
    width: 14px;
    height: 14px;
  }

  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .bg-gray {
    padding: 6px 15px;
  }

  .qr-code {
    width: 15px;
    height: 15px;
    cursor: pointer;
  }

  .sort {
    :deep(.el-input-group__append) {
      padding: 0;
      border-radius: 0 12px 12px 0;
    }
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
