import { createI18n } from 'vue-i18n';
import ZH from './zh.js';
import EN from './en.js';

const messages = {
  zh: { ...ZH },
  en: { ...EN },
};

// 从本地存储中获取语言设置，如果没有则默认使用英文
export const getLocale = () => {
  const cachedLocale = localStorage.getItem('locale');
  if (cachedLocale) {
    return cachedLocale;
  }
  return 'en'; // 默认语言为英文
};

const i18n = createI18n({
  legacy: false,
  globalInjection: true,
  locale: getLocale(),
  messages,
});

// Element Plus 语言更新回调函数
let elementPlusLocaleUpdater = null;

// 注册 Element Plus 语言更新函数
export const registerElementPlusLocaleUpdater = updater => {
  elementPlusLocaleUpdater = updater;
};

// 导出一个切换语言的函数
export const setLocale = locale => {
  // 更新 vue-i18n 语言
  i18n.global.locale.value = locale;

  // 更新 Element Plus 语言
  if (elementPlusLocaleUpdater) {
    elementPlusLocaleUpdater(locale);
  }

  // 保存到本地存储
  localStorage.setItem('locale', locale);

  return locale;
};

export default i18n;
