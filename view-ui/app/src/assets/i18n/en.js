export default {
  dataStatistic: {
    types: {
      project: 'Project',
      sample: 'Sample',
      analysis: 'Analysis',
      experiment: 'Experiment',
      run: 'Run',
      data: 'Data',
    },
    status: {
      accessible: 'Accessible',
      unaccessible: 'UnAccessible',
    },
    security: {
      private: 'Private',
      restricted: 'Restricted',
      public: 'Public',
    },
  },
  barChart: {
    loading: 'loading',
    legend: {
      projects: 'Projects',
      experiments: 'Experiments',
      samples: 'Samples',
      analysis: 'Analysis',
      data: 'Data',
      totalData: 'Total data',
    },
  },
  dataVolume: {
    charts: {
      public: 'Public',
      restricted: 'Restricted',
      private: 'Private',
      totalData: 'Total data',
      download: 'Download',
      uploadData: 'Upload Data',
      submission: 'Submission',
    },
  },
  popularItem: {
    units: {
      visits: 'visits',
      downloads: 'downloads',
      requested: 'requested',
    },
  },
  htTable: {
    selectColumn: {
      title: 'Select Column',
    },
    columnVisibility: {
      title: 'Column visibility',
      allColumns: 'All columns',
      requiredColumns: 'Required columns',
      recommendedColumns: 'Recommended columns',
      optionalColumns: 'Optional columns',
    },
    search: {
      placeholder: 'Search column',
    },
    buttons: {
      confirm: 'Confirm',
      rename: 'Rename',
      cancel: 'Cancel',
    },
    selectExisting: 'Select existing {0}',
    samples: 'Samples',
    colorKey: {
      title: 'Color key',
      requiredField: 'Required filed',
      recommendedField: 'Recommended filed',
      optionalField: 'Optional filed',
      invalidCell: 'Invalid cell',
      invalidCellTooltip:
        '<span class="font-16">This color indicates that the following errors occurred in the filled content:<br>1. Contains Chinese characters<br>2. Type error, such as a string filled in for a numeric type<br>3. Value range error</span>',
    },
    dialog: {
      title: 'Column rename',
    },
    form: {
      name: 'Name',
      description: 'Description',
      nameTooltip:
        'Can only contain numbers, letters, spaces, "(", ")", "/", "-" and "_"',
      descriptionTooltip: 'Attribute field description',
    },
    validation: {
      invalidColumnNameFormat: 'Invalid column name format',
      invalidDescriptionFormat: 'Invalid description format',
    },
    contextMenu: {
      addNewColumn: 'Add new column',
      renameColumn: 'Rename this column',
      removeColumn: 'Remove column',
    },
    messages: {
      cannotDeleteRequiredColumn: 'Cannot delete required column',
      cannotRenameRequiredColumn: 'Cannot rename required column',
      columnNameAlreadyExists: 'Column name already exists',
    },
  },
  modal: {
    alert: {
      tip: 'Tip',
      error: 'Error',
      warning: 'Warning',
    },
    confirm: {
      title: 'Warning',
      confirmButton: 'Confirm',
      cancelButton: 'Cancel',
    },
    prompt: {
      title: 'Warning',
      confirmButton: 'Confirm',
      cancelButton: 'Cancel',
    },
  },
  person: {
    name: 'zhangsan',
    hobby: 'Singing and dancing, rap, basketball',
  },
  header: {
    menu: {
      home: 'Home',
      submit: 'Submit',
      browse: 'Browse',
      statistic: 'Statistic',
      download: 'Download',
      help: 'Help',
    },
    auth: {
      login: 'Login',
      register: 'Register',
    },
    search: {
      placeholder: 'Search for',
    },
    language: {
      english: 'English',
      chinese: '中文',
    },
    userMenu: {
      userCenter: 'User Center',
      mySubmission: 'My Submission',
      setting: 'Setting',
      logout: 'Log Out',
    },
  },
  help: {
    breadcrumb: 'Help',
    overview: {
      title: 'Overview',
      description:
        'NODE is a biological big data collection platform, including the experimental sample information collection, the file upload of the sequences, and the analysis, share and download of the results. NODE platform consists of six main modules: project, sample, experiment, run, data, and analysis. Project and Sample are independent of each other, but can be linked through Run. In this way, the metadata and sequence information can be integrated.',
      experimentType: {
        title: '1.1 Supported Experiment Type',
        description:
          'The number of NODE experiment types is fourteen, including:',
        types: {
          genomic: 'Use for DNA sequencing.',
          transcriptomic: 'Use for RNA sequencing.',
          metagenomic:
            'Use for DNA sequencing, which is extracted from all microorganisms in environmental samples.',
          metatranscriptomic:
            'Use for RNA sequencing, which is extracted from all microorganisms in environmental samples.',
          genomicSingleCell: 'Use for single cell DNA sequencing.',
          transcriptomicSingleCell: 'Use for single cell RNA sequencing.',
          proteomic: 'Use for protein sequencing.',
          metabolomic: 'Use for metabolites in living organisms.',
          metabolomicNMR:
            'Use for metabolites in living organisms by NMR detection.',
          electronMicroscopy:
            'Use for electron microscopy imaging of biological sample.',
          microarray: 'Use for microarray data.',
          synthetic: 'Use for synthetic biology sequencing data.',
          viralRNA: 'Use for viral RNA sequencing.',
          flowCytometry:
            'Use for determination of the biological properties of cells or organelles.',
        },
      },
      sampleType: {
        title: '1.2 Supported Sample Type',
        description: 'The number of NODE sample types is eight, including:',
        types: {
          human: 'Use for human samples that have no privacy concerns.',
          animalia:
            'Use for multicellular samples derived from common laboratory animals, e.g., mouse, rat, Zebrafish, etc.',
          plant: 'Use for any plant sample.',
          cellLine: 'Use for cell lines derived from common laboratory.',
          microbe:
            'Use for any microbe sample or microbial strains, e.g. Yeast.',
          environmentHost:
            'Use for metagenomic and host-associated samples, for example human gut metagenome, human oral metagenome, fish gut metagenome, etc.',
          environmentNonHost:
            'Use for metagenomic and non-host-associated samples, for example marine metagenome, fresh water metagenome, soil metagenome, etc.',
          pathogen:
            'Clinical or host-associated, environmental, food or the other pathogen.',
        },
      },
    },
    registerLogin: {
      title: 'Register and Login',
      signUp: {
        title: '2.1 Sign up for NODE',
        step1: {
          title: '1.Register for NODE',
          description:
            'Enter your email address, password and confirm password, enter the user information including first name, last name, organization and country/region to register to NODE, while the other information only has to be provided according to user’s interests. Select "I have read and agreed to the Privacy Policy.". Click "Create account"',
        },
        step2: {
          title: '2.Registration is done',
          description: 'Your registration is done in NODE.',
        },
        step3: {
          title: '3.Activate your account',
          description:
            'Then you will receive a registration activation link in your registered email box from NODE. Click the link within 48 hours to activate your account.',
        },
        step4: {
          title: '4.Activation is done',
          description:
            'After the activation, you will be reminded that you have done all steps of your registration. You can start to login NODE.',
        },
        tips: {
          title: 'Some Tips:',
          description:
            'Please use a new and valid email address and enter email address properly for registration. Or you will get the following reminders:',
          emailRegistered: '(1) Email registered',
          invalidEmail: '(2) Not a valid email',
        },
      },
      login: {
        title: '2.2 Login to NODE account',
        description:
          'After the registration, user can login to NODE by inputting proper email address and password in the login page. NODE provides "Remember Password" function so that user doesn\'t have to input email address and password every time using NODE. We suggest that you ONLY activate this function in your personal computer.',
      },
      resetPassword: {
        title: '2.3 Forget password/Reset password',
        step1:
          'If you forget your password, you can simply click "Forget Password" and input your email address in the following page and click send to send password reset to your email.',
        step2:
          'Here is the email example received from NODE for the reset of the password.',
        step3:
          'Click "Password Reset", user can reach the password reset page in NODE. To keep your account safe, we strongly encourage you NOT to share your password.',
      },
      modifyInfo: {
        title: '2.4 Modifies registration information',
        description:
          'In User Center, registered users can modify registration information by Edit.',
      },
    },
    userCenter: {
      title: 'User Center',
      overview: {
        title: '3.1 User center overview',
        description1:
          'User center is a convenient tool for user to have an overview of his/her own NODE data as well as management of data requests.',
        description2:
          'In the following part of user center, user can have an overview of NODE data, the detail information of the user and highly accessed publications related to the research with high access rate in NODE, as well as other convenient function for the share and use of the data, such as "My shares", "Share From Others" , "My Requests", "Requests From Others", and "My Reviews".',
      },
      dataList: {
        title: '3.2 My data list',
        description1:
          '"My data list" show all the data of the user, including the lists of projects, experiments, samples, runs, analysis, data, publishes and submissions. Besides, the data name, status, uploaded date and operate are displayed. In experiments, samples, runs and analysis, there is the batch modify link in the right icon "operate" button.',
        description2:
          'Furthermore, you can input the filter words and period time to search your data, and then the filtered data list is shown in the red box on the left.',
      },
      dataStatistics: {
        title: '3.3 My Data Statistics',
        description:
          '"My Data Statistics" show the data statistics of project, sample, analysis, experiment, run and data. Users can see the total amount of data, the ratio of accessible or unaccessible data, and the total amount, accessible amount or unaccessible amount of files in project, sample, analysis, experiment, run, and data.',
      },
      dataActivity: {
        title: '3.4 My Data Activity',
        description:
          '"My Data Activity" present the numbers of projects, experiments, samples, analysis and total data of the user according to three security status respectively. "Views of Data" display the numbers of user\'s data with graphs in every month. "Download of Data" display the download times of user\'s data with graphs in every month.',
      },
    },
    detailPage: {
      title: 'Detail Page',
      projectDetail: {
        title: '4.1 Project Detail',
        description:
          'In project details page, project information includes project ID, project name, project description, total data amount, total file amount, total sample amount, experiment ID, experiment type, experiment name, experiment information, sample type, sample ID, sample name, sample information, file type, file amount, file security status, data list, data quality control information and download link. In data quality control information, users can see FASTQC report and download FASTQC report in "operate" column.',
      },
      experimentDetail: {
        title: '4.2 Experiment Detail',
        description:
          'In experiment details page, experiment information includes experiment ID, experiment type, experiment name, attributes, sample type, file type, file amount, file security status, data list, data quality control information and download link. In data quality control information, users can see FASTQC report and download FASTQC report in "operate" column.',
      },
      sampleDetail: {
        title: '4.3 Sample Detail',
        description:
          'In sample details page, sample information includes sample ID, sample type, sample name, organism, tissue, attributes, experiment type, file type, file amount, file security status, data list, data quality control information and download link. In data quality control information, users can see FASTQC report and download FASTQC report in "operate" column.',
      },
      analysisDetail: {
        title: '4.4 Analysis Detail',
        description:
          'In analysis details page, analysis information includes analysis ID, analysis name, analysis type, total data amount, total file amount, pipeline information, target information, data list and dowload link.',
      },
    },
    rawDataUpload: {
      title: 'Raw Data Upload',
      description1:
        'NODE (The National Omics Data Encyclopedia) provides an integrated, compatible, comparable, and scalable multi-omics resource platform that supports flexible data management and effective data release. NODE uses a hierarchical data architecture to support storage of muti-omics data including sequencing data, MS based proteomics data, MS or NMR based metabolomics data, and fluorescence imaging data.',
      description2:
        'The data submission process in NODE consists of three steps, including "Submit raw data", "Submit metadata" and "Archiving". The arrow bar on the following page indicates each step of the submission process by highlighting the specific step.',
      description3:
        'NODE provides three different ways for data uploading, including, "Http upload", "FTP upload" and "Express hard drive".',
      httpUpload: {
        title: '5.1 HTTP Upload',
        description:
          'Uploading data via website for files less than 200MB. This method of uploading data is very suitable for someone who has a small amount of data to transfer to NODE. Click "Select your file", then select your file and see the file in Unarchived Data.',
      },
      sftpUpload: {
        title: '5.2 SFTP Upload',
        description:
          "If data are too large to upload to NODE using http, users can apply for the Sftp upload. The Sftp account and password are the user's email and password for logging into NODE.",
      },
      ftpTools: {
        title: '5.2.1 FTP tools',
        description:
          'We recommend using FTP client software to upload files, e.g.,{fileZilla}',
        host: 'Host: sftp://fms.biosino.org',
        username: 'Username:NODE user name (email)',
        password: 'Password:NODE password',
        port: 'Port:44397',
      },
      commandLine: {
        title: '5.2.2 Command line',
        sftpTitle: 'SFTP',
        sftpCommand: 'sftp -oPort=44397',
        password: 'Password: your-node-password',
        navigate: 'Navigate to the target folder you need',
        putExample: 'put example.fastq.gz',
        lftpTitle: 'LFTP',
        lftpCommand1: 'lftp',
        lftpCommand2: 'lftp :~> connect',
        mputDescription: 'You can use mput to upload data',
        mputCommand: 'mput *.gz',
        uncheckedDescription:
          'Once the data is uploaded, you can click the "Unchecked" button to find the data in your account.',
      },
      dataIntegrityCheck: {
        title: '5.2.3 Data Integrity Check',
        description1:
          'The data uploaded through FTP needs to undergo MD5 verification and integrity verification to ensure the integrity of the data before it can officially enter the Node.',
        description2:
          'Click on the homepage "Submit" to enter the submission system.',
        description3:
          'Once the data is uploaded, you can click the "Unchecked" button to find the data in your account. Select the files or folders that require integrity verification, and then click the "Data Integrity Check" button.',
        description4:
          'After data submission integrity verification, the progress of integrity verification can be viewed in the "Status" column in the "Checking". And the failed reasons showed in the "Failed Cause" column.',
        description5:
          'The data that has completed integrity verification can be viewed in \'Unarchived data\'. If you want to delete the data, you can delete the data one by one in the "Operate", or can select all the data you want to delete, and then click the "Delete" button.',
        tips: {
          title: 'Some Tips:',
          tip1: '1. When uploading data to FTP, create folders based on the experiment type and date of the data, place the same experiment type in the same folder, and upload the entire folder. During integrity verification, the data in the entire folder can be checked together.',
          tip2: '2. The files in each folder should not exceed 4000.',
        },
      },
      expressHardDrive: {
        title: '5.3 Express hard drive',
        description:
          'Due to network condition, massive scale of data may not be safely uploaded. Users can post data to NODE by using hard drive. Before posting, please inform us in advance (+86 021-********). After posting, please provide us with the details of your post. Our contact information is as below:',
        email: 'Email address:',
        address:
          'Address: Bio-Med Big Data Center, 320 Yue Yang Road, Shanghai, 200031 P.R.China',
      },
    },
    metadataArchiving: {
      title: 'Metadata & Archiving',
      description1:
        'After successfully uploaded, data would be in unarchived status. We STRONGLY encourage users to fill metadata as completed as possible after unloading data. This information is crucial if you want your data to be searched and visited, and share your data with other researchers. In NODE, users could use the "MetaData" function to arrange the metadata information, including Submitter, Project，Experiment，Sample，Run.',
      description2:
        'There are two types of data archiving forms: raw data and analysis data archiving. Raw data archiving is mainly used to archive metadata, and analysis data archiving is applied to archive data analyzed by different kinds of software or tools.',
      rawDataArchiving: {
        title: '6.1 Raw data archiving',
        newSubmission: {
          title: '6.1.1 New submission',
          description1:
            'When clicking the Raw Data button and the Continue button, the user will enter the metadata archiving interface.',
          description2:
            'Here is some guidance for filling detail metadata information of each part.',
          submitter: {
            title: 'Submitter:',
            firstName: 'First name: the given name of the submitter',
            lastName: "Last name: the submitter's family name",
            organization:
              'Organization: the name of the organization where the submitter worked, eg Shanghai Institute of Nutrition and Health, CAS',
            email: 'Email: the valid email address of the submitter',
            countryRegion:
              'Country/Region: the country or the region where the submitter from.',
          },
          project: {
            title: 'Project:',
            name: 'Project name: NODE allows users to add data to existed project or create new project name.',
            description: 'Description: the description of the project',
            relatedLinks:
              'Related links: links to resources related to this project (publication, datasets, online databases). User could use the plus button to add more links based on the project.',
          },
          experiment: {
            title: 'Experiment:',
            name: 'Experiment name: NODE allows users to add the data to existed experiment or create new experiment name.',
            description:
              'Experiment description: the description of the experiment.',
            platform:
              'Platform: the platform that the user used to do the experiment, NODE provides six different options, including Illumina Hiseq 2000, Illumina Hiseq 2500, Illumina Hiseq 3000, ABI SOLiD 3, ABI SOLiD 4 and Illumina Miseq. Please select the correct platform according to your experimental design.',
            plannedReadLength:
              'Planned read length: the user could input the planned read length according to his/her experiment',
            matePair:
              'Mate pair: the user could choose Y/N base on his/her data.',
            libraryName:
              'Library name: the user can define a name for his/her library',
            libraryStrategy:
              'Library_strategy: sequencing technique intended for this library. NODE provides five options, including WGS, EST, RNA-Seq, FINISHING, Chip-Seq. Please select the correct library strategy according to your experimental design.',
            librarySource:
              'Library_source: specifies the type of source material that is being sequenced. NODE provides three options for selection, including genomic, transcriptomic, metagenomic. Please select the correct library source according to your experimental design.',
            librarySelection:
              'Library_selection: method used to enrich the target in the sequence library preparation. NOED provides three options including PCR, RT-PCR and RANDOM. Please select the correct library source according to your experimental design.',
            libraryLayout:
              'Library_layout: the way how the sequence is amplified. NODE provides three options including single, paired, targeted_loci. Please select the correct library source according to your experimental design.',
            libraryConstruction:
              'Library_construction: briefly describe the way how the user constructed the library.',
            relatedLinks:
              'Related_links: related links for your experiment, user could use the plus button to add more links based on your experiment.',
          },
          sample: {
            title: 'Sample:',
            name: 'Sample_name: NODE allows users to add the data to existed sample or create new sample name.',
            organism:
              'Sample Organism: the organism the user used to prepare the sample, eg : liver, means this sample is get from liver. or in general from human.',
            description: 'Sample description: the description of the sample',
            attributes:
              'Sample attributes: if there is any other information related to the sample that the user can not fill in any part of the sample table, please put in the sample attributes part. The user could user plus or minus button to add or delete attributes, respectively.',
            provider:
              'Sample provider: the information of the people who provided the sample for the experimental analysis.',
            relatedLinks:
              'Related links: links to resources related to this sample or sample set (publication, datasets, online databases). User could use the plus button to add more links based on your sample.',
          },
          run: {
            title: 'Run:',
            name: 'Run name: NODE allows users to add the data to existed run or create new run name.',
            description: 'Description: the description of the run.',
          },
          archives: {
            title: 'Archives:',
            description:
              'The new archive fills experiment_name, sample_name, run_name, and data_id. Click the "Check & Save" button and then click the "submit" button.',
            runDescription: 'Description: the description of the run.',
          },
          submission: {
            title: 'Submission:',
            description:
              'After archive, in my submission, users can see the submission id, and users can revoke the submission to re-edit the metadata.',
            runDescription: 'Description: the description of the run.',
          },
        },
        reArchive: {
          title: '6.1.2 Re-archive',
          description:
            'After review pass, users can re-edit the metadata, or batch modify the metadata, or add new data to existing projects. If users want to re-edit the metadata, users re-edit the project, experiment, sample, and analysis one by one in my data list of user center; or users batch modify experiment, sample and analysis in Metadata.',
          experimentBatch:
            'If users want to batch modify the experiments, users fill the experiment id and their complete information, which need to batch modify, in experiment multiple. If the original field value is changed to a null value, and the system defaults to deleting and changing the field value. Click the "Check & Save" button and then submit in Archiving Multiple.',
          sampleBatch:
            'If users want to batch modify the samples, users fill the sample id and their complete information, which need to batch modify, in sample multiple. If the original field value is changed to a null value, and the system defaults to deleting and changing the field value. Click the "Check & Save" button and then submit in Archiving Multiple.',
          scenarios: {
            modifyRunName:
              'If users want to batch modify the run name in the existing project, users fill run id, new run name and data id, which need to batch modify, in archiving multiple.',
            addNewData:
              'If users want to add new data in the existing project, users fill the experiment id, sample id, new run name and new data id in archiving multiple.',
            modifyDataNewRun:
              'If users want to batch modify the data for new run name in the existing project, users fill the experiment id, sample id, new run name and data id, which need to batch modify, in archiving multiple.',
            addNewExperiment:
              'If users want to add new experiment, new sample and new data in the existing project, users fill the experiment name, project id and experiment information in experiment multiple, fill the sample name and sample information in sample multiple, and fill experiment name, sample name, run name, and data id.',
          },
          finalStep:
            'Click the "Check & Save" button and then submit in Archiving Multiple.',
        },
      },
      analysisDataArchiving: {
        title: '6.2 Analysis data archiving',
        newSubmission: {
          title: '6.2.1. New submission',
          enterInterface:
            'When clicking the Analysis Data button, the user will enter the analysis data archiving interface.',
          guidance:
            'Here is some guidance for filling analysis data information.',
          analysisName:
            'Analysis name: NODE allows users to add the data to existed analysis or create new analysis name.',
          analysisDescription:
            'Analysis description: the description of the analysis.',
          program:
            'Program: the name of software or tools to analyze the data.',
          link: 'Link: links to resources related to the analysis step.',
          version: 'Version: the version of the program.',
          output:
            'Output: select the corresponding data ID of the analysis data',
          target:
            'Target: associate data with projects, experiments, samples, runs, data and analysis by clicking each ID number.',
          archives: {
            title: 'Archives:',
            description:
              'The new archive fills analysis_name and data_id. Click the "Check & Save" button and then click the "submit" button.',
          },
          submission: {
            title: 'Submission:',
            description:
              'After archive, in my submission, users can see the submission id, and users can revoke the submission to re-edit the metadata. And users can receive e-mail of submission after review pass.',
          },
        },
        reArchive: {
          title: '6.2.1 Re-archive',
          description:
            'Users re-edit the analysis one by one in my data list of user center; or users batch modify analysis in Metadata.',
          batchModify:
            'If users want to batch modify the analysis, users fill the analysis id and their complete information, which need to batch modify, in analysis multiple. If the original field value is changed to a null value, and the system defaults to deleting and changing the field value. Click the "Check & Save" button and then submit in Archiving Multiple.',
          addNewData:
            'If users want to add new data in the existing analysis, users fill the analysis id, and new data id in archiving multiple. Click the "Check & Save" button and then submit in Archiving Multiple.',
        },
      },

      reArchive: {
        title: '6.1.2 Re-archive',
        description:
          'After review pass, users can re-edit the metadata, or batch modify the metadata, or add new data to existing projects. If users want to re-edit the metadata, users re-edit the project, experiment, sample, and analysis one by one in my data list of user center; or users batch modify experiment, sample and analysis in Metadata.',
        experimentBatch:
          'If users want to batch modify the experiments, users fill the experiment id and their complete information, which need to batch modify, in experiment multiple. If the original field value is changed to a null value, and the system defaults to deleting and changing the field value. Click the "Check & Save" button and then submit in Archiving Multiple.',
        sampleBatch:
          'If users want to batch modify the samples, users fill the sample id and their complete information, which need to batch modify, in sample multiple. If the original field value is changed to a null value, and the system defaults to deleting and changing the field value. Click the "Check & Save" button and then submit in Archiving Multiple.',
        scenarios: {
          modifyRunName:
            'If users want to batch modify the run name in the existing project, users fill run id, new run name and data id, which need to batch modify, in archiving multiple.',
          addNewData:
            'If users want to add new data in the existing project, users fill the experiment id, sample id, new run name and new data id in archiving multiple.',
          modifyDataNewRun:
            'If users want to batch modify the data for new run name in the existing project, users fill the experiment id, sample id, new run name and data id, which need to batch modify, in archiving multiple.',
          addNewExperiment:
            'If users want to add new experiment, new sample and new data in the existing project, users fill the experiment name, project id and experiment information in experiment multiple, fill the sample name and sample information in sample multiple, and fill experiment name, sample name, run name, and data id.',
        },
        finalStep:
          'Click the "Check & Save" button and then submit in Archiving Multiple.',
      },
    },
    shareReviewRequest: {
      title: 'Share, Review and Request',
      dataShare: {
        title: '10.1 Data Share',
        description1:
          'Sharing your data with other researchers is crucial to make your research known to other scientists and to help other scientists with their research. On the Project Details page, you can use the Share button, when the data are private or restricted and the shared user is NODE user, as shown in the image below. A similar Share button can also be found on the Sample, Experiment and Analysis detail pages.',
        description2:
          'After clicking on the Share button, you can select which part of the data to share by clicking on the corresponding box in front of Project, Experiment, or Sample and enter the email addresses of the person(s) with whom you wish to share this part of the data. The user can see all the data that has been shared on the following "My Share" page. Persons being shared needs to be a registered NODE user.',
        description3:
          'On the My Shares page you can see all your shared data. The Cancel button in red can be used to unshare the data.',
        description4:
          'Users that are shared will receive an email from NODE to inform the successful share of data.',
        description5:
          'They can then review the shared data under "Share From Others" and download the data they are interested in by clicking the download button in blue.',
      },
      dataReview: {
        title: '10.2 Data Review',
        description1:
          'If data owner wants to publish data, this feature is very useful to make data visible to reviewers. On the detail page of the project, the user can use the review button surrounded by a red ellipse as shown in the following picture, when the data are restricted. The similar review button can also be found in the sample, experiment and analysis detail pages.',
        description2:
          "The user can use this feature to select the specific person to view the data by entering the person's name, email address and the period of time they wish to view the data. The user can also select which part of the data he/she wishes to review by clicking on the appropriate box in front of Project, Experiment, or Sample.",
        description3:
          'Once you have confirmed the data to be reviewed, the "My Review" page is displayed. You can see all the data that has been checked.',
        description4:
          'Users that are reviewed will receive an email from NODE to download the successful review of data by clicking Here button.',
      },
      requestRestrictedData: {
        title: '10.3 Request for Restricted Data',
        description1:
          'You have to apply for the authorization from author to use restricted data.',
        example: 'For example:',
        userADescription:
          'User A has restricted data in NODE called OEP00000775, shown as below:',
        userBDescription1:
          'User B can search this restricted data called OEP000775, and visit it, but cannot download it:',
        userBDescription2:
          "If user B want to download the data, user B has to apply for owner's authorization by sending a request. User B can select which part of the data is required and wait for user A's permission. The detail request text could be beneficial for passing.",
        userBDescription3: 'User B can check my requests in user center.',
        emailNotification:
          'Then user A will receive an email from NODE shown as below:',
        checkRequests:
          'User A can check the requests from others in user center.',
        permitDecline:
          'User A can login to NODE and find the data request in user center to permit or decline this request.',
        authorization:
          'If user A authorize this request, user B will receive a verification code which lasts for one month. User B just simply clicks ID to jump to the download page, and clicks Export Data Links to get the download address.',
      },
    },
    dataSecurity: {
      title: 'Data Security',
      overview: {
        title: '8.1 Overview',
        description1:
          'NODE offers three levels of data security, including public data, restricted data and private data. Public data can be searched, viewed and downloaded by anyone using NODE. Restricted data can only be searched using the search function in NODE. If you want to visit or download this part of the data, you need to get permission from the data owner. Private data can only be viewed by the data owner.',
        description2:
          'Once you have uploaded your data, it is automatically considered private unless you change its security level. Please note that the NODE only allows data to be converted from a high security level to a low security level. For example, you can only change private data to restricted data or public data, otherwise it is not allowed. You can also convert restricted data to public data, but not public data to restricted data.',
        imageCaption: 'Change of data security in NODE',
        description3:
          'NODE encourages users to share your data so that other scientists can find out about your research and help other scientists with their research.',
        note: 'Note: Only the data owner can make the following changes',
      },
      privateToRestricted: {
        title: '8.2 Private->Restricted',
        description1:
          'As the owner of the data, you can go to the Project, Experiment, Sample, or Analysis detail page and use the Security function to change the security level of the data.',
        description2:
          'Once you change the security level of the data from Private to Restricted, you have taken the step of sharing the data. The data will be available for browsing on the NODE. However, other users will still not be able to view or download the data. On the other hand, you can use the "until" function to choose how long you want to keep the data restricted, one year or two years. At the end of this period, the data will automatically be released. On the other hand, if you activate the "require request" function, other users will have to apply for authorisation to request access to the data.',
        description3:
          'Once you have decided how you want to keep your data, you can click "Confirm" and the NODE will display another confirmation of your data status change, as shown below:',
      },
      privateToPublic: {
        title: '8.3 Private->Public',
        description:
          'Once you change the security level of your data from restricted to public, which means anyone logging to NODE can search, visit and download your data, and the quality control information of the data will also be disclosed synchronously. Also, NODE will show up a confirmation for you to reconfirm the security status of your data.',
      },
      restrictedToPublic: {
        title: '8.4 Restricted->Public',
        description1:
          'Changing the security status from restricted to public is the same as changing the security status from private to public.',
        description2:
          'If the security level of the data is changed from Private to Restricted or Public, the visibility of Projects, Experiments, Samples and Runs will change from Unaccessible to Accessible, i.e. other NODE users will be able to view the relevant information.',
      },
      tips: {
        title: 'Some Tips:',
        description:
          'The following three ways of changing the security status of data are NOT allowed by NODE. If you have to do the following changes, please contact',
        restriction1: '1. Public-------->Private',
        restriction2: '2. Public-------->Restricted',
        restriction3: '3. Restricted -------->Private',
      },
    },
    nodeSearch: {
      title: 'NODE Search',
      fullTextSearch: {
        title: '9.1 Full Text Search',
        description:
          'NODE provides full text search. Although results searched by NODE will not be very precise, user can get all information related to the keyword inputted for search. User can use the information in left panel to further reach the detail information which he/she is interested in. For example, if the user searches "DNA" in NODE, he/she can get totally 51576 results. On the right panel of the result page, user can find the calculation of results number according to data access, NODE Level, and Multiple Omics, etc. for further filter of search results.',
      },
      advancedSearch: {
        title: '9.2 Advanced Search',
        description:
          'In advanced search, users can select ID, name, description, etc. and combine ID, name, description, etc. by "AND", "OR", "NOT" to search the data which you want to search.',
      },
    },
    dataManagement: {
      title: 'Data Management',
      rawDataManagement: {
        title: '7.1 Raw data management',
        description:
          'Unchecked data files can be deleted in the "Unchecked" table in the submit page.',
      },
      metaDataManagement: {
        title: '7.2 Meta data management',
        description:
          "In User Center, Project section of My Data List displays the basic information of the user's uploaded project. In Project page, the user can view all the uploaded project list pages, and the user can click each project id and view the details of metadata information of different projects. If project is unaccessible, the user can delect the project.",
      },
    },
    humanGeneticResources: {
      title: 'Human Genetic Resources Data Submission',
      description:
        'We expect you to comply with applicable laws or regulations if you plan to submit the data that contains human genetic information to us. Please also make sure your submitted data is set to RESTRICTED status by using our data submission tool. In addition, your submission should not breach participating individuals’ rights and comprise their original signed consents. For specific management regulations and rules, please refer to',
      and: 'and',
    },
    dataDownload: {
      title: 'Data Download',
      httpDownload: {
        title: '11.1 HTTP download',
        bulkDownload: {
          title: '1.Bulk Download',
          description:
            'Download all links related to one project by using the "Download" function in NODE. This function is highly recommended for the download of large amounts of data.',
          example:
            'Here is the example of all links for data related to the project "OEP000035".',
        },
        specificDownload: {
          title: '2.Download specific data',
          description:
            'Download files one by one by clicking the download button. This function is more likely to be used to download the specific data which is interested by the researcher.',
        },
      },
      ftpDownload: {
        title: '11.2 FTP download',
        tips: 'Tips:',
        directoryStructure:
          'The data download directory is arranged according to the Run id, in the form of /Public/byRun/OER/OER/OER.',
        byRun: '"byRun" is for rawdata download',
        byAnalysis: '"byAnalysis" is for analysis data download',
        runListQuestion: 'Where can I get the run list?',
        runListAnswer: 'Using "Export data links" in project detail page.',
        windows: {
          title: '(1)Windows',
          description:
            'We recommend using FTP client software to upload files, e.g., FileZilla',
          host: 'Host: sftp://fms.biosino.org',
          username: 'Username: NODE username (email)',
          password: 'Password: NODE password',
          port: 'Port: 44398',
          quickConnect: 'Click on the "Quickconnect" for data download.',
        },
        linux: {
          title: '(2)Linux',
          sftp: {
            title: 'SFTP',
            password: 'Password: your-node-password',
            navigate: 'Navigate to the target folder you need',
            command:
              'cd /Public/byRun/OER**/OER****/OER****** get example.fastq.gz',
          },
          lftp: {
            title: 'LFTP',
            password: 'Password: your-node-password',
            description: 'You can use glob pget or mget to download data',
            command1: 'glob pget /Public/byRun/OER**/OER****/OER******/*.gz',
            command2: 'mget /Public/byRun/OER**/OER****/OER******/*.gz',
          },
        },
      },
    },
    faq: {
      title: 'FAQ',
      activationFailed: {
        title: '13.1 "Activation failed" during login',
        description:
          'The error message "Activation failed" during login process may be caused by link timeout or users repeatedly click the link. Users can try to login, and if they can not login, users can contact',
        additionalInfo: '. Or users copy and paste the incomplete link.',
      },
      usernamePasswordError: {
        title: '13.2 "Username or Password error" during login',
        description:
          'If the error message "Username or Password error" is shown during login process, users can confirm whether "Username" (email) or "Password" is correct. And if users forget the password, the users can click the "Forget password" button to find the password.',
      },
      setDataSecurity: {
        title: '13.3 Set data security level during contribution',
        description:
          'The status of data may be restricted during the contribution process. How to set the security level sees',
        linkText: 'Change of data security in NODE.',
      },
      citeData: {
        title: '13.4 Cite data in contribution',
        description:
          'After successfully submitting your data to NODE, we recommend using the following wording to describe the data deposition in your manuscript:',
        accessText: 'All data are accessible in NODE (',
        accessionText:
          ') with the accession number XXX (e.g., OEP00000073) or through the URL:',
        citationPrompt: 'Please cite the following publication:',
        publicationText:
          'Advances in multi-omics big data sharing platform research. Chinese Bulletin of Life Sciences. 2023, 35(12): 1553-1560. DOI:',
      },
      useTemplate: {
        title: '13.5 Use template example file',
        description1:
          'During the metadata submission process, users encounter confusion about how to fill in the field content.',
        description2:
          'Users can download the excel example to see the example of the field content.',
      },
      integrityVerification: {
        title: '13.6 Integrity verification not completed',
        description:
          'After submitting the raw data, the integrity verification is not completed for a long time. Users can see the Failed Cause, and contact Email address:',
      },
      submissionReview: {
        title: '13.7 Submission review not completed',
        description:
          'After submitting metadata, the review result has not been received for a long time. Users can contact',
      },
      splitMd5: {
        title: '13.8 How to split md5 files',
        description1:
          'Users often obtain the following md5 documents from sequencing companies, for example "md5_result-1.txt":',
        description2: 'Prepare the following split script file',
        description3: 'Run the script file with the following command：',
      },
      dataIntegrityCheck: {
        title: '13.9 How to do data integrity check',
        description: 'How to do data integrity check sees',
        linkText: '5.2.3 Data Integrity Check.',
      },
      transferData: {
        title: '13.10 How to transfer my data to another user',
        description:
          'In the NODE system, you have the flexibility to transfer the ownership of your archived and audited metadata and raw data, encompassing Projects, Experiments, Samples, and Analyses, to other registered users within the NODE ecosystem. To begin this process, please follow these simple steps:',
        step1:
          '(1) Compose an email using the email address that is linked to your NODE account.',
        step2: '(2) Send this email to',
        step2Additional:
          ", ensuring that you also include the recipient's email address in the CC field.",
        step3:
          '(3) In the body of your email, clearly outline your request to transfer the specific data (referred to as XXXX) currently under your NODE account (designated as XXX) to another designated NODE account (also referred to as XXX). Please ensure that both the sender and recipient accounts are registered within the NODE system and that you are the rightful owner of the data.',
        step4:
          '(4) Upon receiving your email, the NODE support team will take over, guiding you through the remainder of the data transfer procedure.',
        conclusion:
          "We are here to ensure a smooth transition, so if you have any questions or need further assistance, don't hesitate to reach out to our support team.",
      },
      groupSubmissions: {
        title:
          '13.11 Data submissions for the same research group by multiple individuals',
        description:
          'Users often inquire about how to facilitate data submissions for the same research group by multiple individuals. We recommend the following strategies:',
        recommendation:
          "First of all, each research group should have a dedicated account, typically the principal investigator's (PI) commonly used email address, which serves as the owner of the group's data.",
        strategy1:
          "Strategy 1: Different members within the group, such as students and staff, register and submit data using their individual accounts (email addresses). After the data submission and archiving are complete, they could actively email {email} from the submission email to request transferring the data to the group's fixed account(refer to section",
        strategy1Additional: 'for details).',
        strategy2:
          "Strategy 2: Different members within the group, such as students and staff, register and submit data using their individual accounts (email addresses). During data submission, they should fully provide the PI's email information (refer to section",
        strategy2Additional:
          'Submitter for details). Subsequently, the PI can request, via this email, to transfer the data to their own email account through node-support.',
        strategy3:
          'Strategy 3: The account information within the research group is maintained internally. When there is a need for data submission, the account information is shared with the person responsible for the data submission.',
        conclusion:
          'These strategies ensure a structured and efficient approach to data submission, maintaining clarity and accountability within the research group.',
      },
    },
    toc: {
      updated: 'Updated',
      overview: {
        title: 'Overview',
        experimentType: 'Supported Experiment Type',
        sampleType: 'Supported Sample Type',
      },
      registerLogin: {
        title: 'Register and Login',
        signUp: 'Sign up for NODE',
        login: 'Login to NODE account',
        resetPassword: 'Forget password/Reset password',
        modifyInfo: 'Modifies registration information',
      },
      userCenter: {
        title: 'User Center',
        overview: 'User center overview',
        dataList: 'My data list',
        dataStatistics: 'My Data Statistics',
        dataActivity: 'My Data Activity',
      },
      detailPage: {
        title: 'Detail Page',
        project: 'Project Detail',
        experiment: 'Experiment Detail',
        sample: 'Sample Detail',
        analysis: 'Analysis Detail',
      },
      rawDataUpload: {
        title: 'Raw Data Upload',
        httpUpload: 'HTTP Upload',
        sftpUpload: 'SFTP Upload',
        ftpTools: 'FTP tools',
        commandLine: 'Command line',
        dataIntegrityCheck: 'Data Integrity Check',
        expressHardDrive: 'Express hard drive',
      },
      metadataArchiving: {
        title: 'Metadata & Archiving',
        rawDataArchiving: 'Raw data archiving',
        analysisDataArchiving: 'Analysis data archiving',
        newSubmission: 'New submission',
        reArchive: 'Re-archive',
      },
      dataManagement: {
        title: 'Data Management',
        rawData: 'Raw data management',
        metaData: 'Meta data management',
      },
      dataSecurity: {
        title: 'Data Security',
        overview: 'Overview',
        privateToRestricted: 'Private->Restricted',
        privateToPublic: 'Private->Public',
        restrictedToPublic: 'Restricted->Public',
      },
      nodeSearch: {
        title: 'NODE Search',
        fullTextSearch: 'Full Text Search',
        advancedSearch: 'Advanced Search',
      },
      shareReviewRequest: {
        title: 'Share, Review and Request',
        dataShare: 'Data Share',
        dataReview: 'Data Review',
        requestRestrictedData: 'Request for Restricted Data',
      },
      dataDownload: {
        title: 'Data download',
        httpDownload: 'HTTP download',
        ftpDownload: 'FTP download',
      },
      humanGeneticResources: 'Human Genetic Resources Data Submission',
      faq: {
        title: 'FAQ',
        activationFailed: '"Activation failed" during login',
        usernamePasswordError: '"Username or Password error" during login',
        setDataSecurity: 'Set data security level during contribution',
        citeData: 'Cite data in contribution',
        useTemplate: 'Use template example file',
        integrityVerification: 'Integrity verification not completed',
        submissionReview: 'Submission review not completed',
        splitMd5: 'How to split md5 files',
        dataIntegrityCheck: 'How to do data integrity check',
        transferData: 'How to transfer my data to another user',
        groupSubmissions:
          'Data submissions for the same research group by multiple individuals',
      },
    },
  },
  requestTo: {
    dialog: {
      title: 'Request',
    },
    alert: {
      restrictedDataInfo:
        '1. This request list only lists restricted Data which could be visited after authorization.',
      directSendInfo:
        '2. This request will directly send to the author of the objects.',
      tooManyObjectsWarning:
        '3. The author may not accept the request if you choose too many objects.',
    },
    form: {
      experimentId: 'Experiment ID',
      sampleId: 'Sample ID',
      runId: 'Run ID',
      dataId: 'Data ID',
      placeholder: 'Please enter a keyword',
      requestText: 'Request Text',
    },
    buttons: {
      search: 'Search',
      reset: 'Reset',
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
    table: {
      dataId: 'Data ID',
      dataName: 'Data Name',
      experiment: 'Experiment',
      sample: 'Sample',
      run: 'Run',
      analysis: 'Analysis',
    },
    messages: {
      applicationSubmitted:
        'You have submitted your application. Please wait patiently for the data owner to authorize.',
      pleaseSelectData: 'Please select the data',
      requesting: 'Requesting',
      applySuccess: 'Apply success',
    },
  },
  reviewTo: {
    dialog: {
      title: 'Review To',
    },
    alert: {
      urlInvalidInfo: '1. The review URL will invalid after the valid period.',
      securitySuggestion:
        '2. We suggest setting the security as public after valid period.',
      periodSuggestion:
        '3. We suggest setting the valid period more than 3 months.',
    },
    form: {
      name: 'Name',
      email: 'Email',
      validPeriod: 'Valid Period',
      pickDate: 'Pick a date',
    },
    dataTypes: {
      project: 'Project',
      analysis: 'Analysis',
      experiment: 'Experiment',
      sample: 'Sample',
    },
    buttons: {
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
    validation: {
      emailFormat: 'Please input correct email address',
      dateRequired: 'Please select an expiration date',
    },
    messages: {
      selectAtLeastOne: 'Please select at least one data',
      shortPeriodWarning:
        'The valid period is short! We suggest setting the valid period more than 3 months. Are you sure to continue?',
      sending: 'Sending',
      reviewSuccess: 'Review Success',
      linkIs: 'Link is:',
      saveCarefully:
        'Please save the link carefully! Anyone can access the data via this link.',
    },
  },
  security: {
    dialog: {
      title: 'Security status change confirmation',
    },
    filter: {
      instruction:
        '1.Filter the data that you want to modify the security status：',
    },
    form: {
      security: 'Security',
      experimentId: 'Experiment ID',
      sample: 'Sample',
      placeholder: 'Please enter a keyword',
      select: 'Select',
    },
    securityLevels: {
      private: 'Private',
      restricted: 'Restricted',
      public: 'Public',
    },
    sampleOptions: {
      sampleId: 'Sample ID',
      sampleName: 'Sample Name',
      sampleType: 'Sample Type',
      sampleOrganism: 'Sample Organism',
    },
    buttons: {
      filter: 'Filter',
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
    tabs: {
      data: 'Data',
      analysisData: 'Analysis Data',
      relatedAnalysisData: 'Related Analysis Data',
    },
    table: {
      dataId: 'Data ID',
      dataName: 'Data Name',
      experiment: 'Experiment',
      sample: 'Sample',
      run: 'Run',
      analysis: 'Analysis',
      dataType: 'Data Type',
      uploadTime: 'Upload Time',
      nameSecurity: 'Name/Security',
      analysisId: 'Analysis ID',
      analysisName: 'Analysis Name',
    },
    change: {
      instruction:
        '2.Change the data and analysis data of the above query from',
      to: 'to:',
      qualityControlInfo:
        'The quality control information of the data will also be disclosed synchronously.',
    },
    restrictedOptions: {
      until: 'Until',
      requireRequest: 'Require request',
    },
    humanGenetic: {
      regulationText:
        "According to the Regulations of the People's Republic of China on the Management of Human Genetic Resources and the Rules for the Implementation of the Regulations on the Management of Human Genetic Resources, the disclosure of data on human genetic resources is subject to review and filing through the Human Genetic Resources Review. Please fill in the corresponding filing number truthfully. If you have any questions, please contact",
      phoneNumber: 'or 010-88225151',
      biologicalSamples:
        'Biological samples such as urine, feces, serum, and plasma that may contain very small amounts of shed, residual, or free cells or genes',
      otherSamples: 'Other samples',
      nonHuman: 'non-human',
      human: 'human',
      humanCommercialCellLines: 'human-commercial cell lines',
      humanNonCommercialCellLines: 'human non-commercial cell lines',
      filingNumberPlaceholder: 'Please enter the filing number',
    },
    messages: {
      loading: 'Loading...',
      securityModificationError:
        "The security modification can't be performed now, because: <br>{reasons}",
      auditStatusWarning:
        "<p>Your account status is not 'audit pass', can not update data security !</p><span class=\"text-danger\">*</span>If you receive 'audit pass' email recently, but the User Center's Account status is not 'audit pass', please log out the website and log in again!",
      selectSecurityLevel: 'Please select the security level to be modified!',
      selectDataToUpdate:
        'Please select the data for which you want to update the security level',
      multipleSampleTypes:
        'There is {types} in Sample Type. Please select Sample Type one by one to change the security of data.',
      submitting: 'Submitting',
      modifiedSuccessfully: 'Modified successfully',
      modifiedSuccessfullyWait:
        'Modified successfully! If you find that the Security of Data has not changed, please wait patiently for a few minutes and refresh the browser again',
      tip: 'Tip',
      ok: 'OK',
      fillFilingNumber: 'Please fill in the filing number',
      invalidFormat: 'Invalid format, only supports numbers、letters、-',
      dataLimitExceeded:
        'The number of selected data exceeds the limit of 1000',
      publicSecurityConfirm:
        'After the data security level is changed to "Public", the "Data QC info" will also be visible.',
    },
  },
  shareTo: {
    dialog: {
      title: 'Share To',
    },
    form: {
      shareToEmail: 'Share To Email ({count}):',
      emailPlaceholder: 'Email',
    },
    dataTypes: {
      project: 'Project',
      experiment: 'Experiment',
      sample: 'Sample',
      analysis: 'Analysis',
    },
    empty: {
      noData: 'No Data',
    },
    buttons: {
      confirm: 'Confirm',
      cancel: 'Cancel',
    },
    messages: {
      emailFormatError: 'Email format error: [{emails}]',
      pleaseSelectEmail: 'Please select the email',
      selectAtLeastOne: 'Please select at least one item',
      sharing: 'Sharing',
      shareSuccess: 'Share success',
    },
  },
  submit: {
    submission: {
      list: {
        breadcrumb: 'My Submission',
        startNewSubmission: 'Start New Submission',
        submissionList: 'Submission List',
        steps: {
          uploadData: 'UploadData',
          metadata: 'Metadata',
          archivingData: 'Archiving Data',
        },
        descriptions: {
          uploadData:
            'The NODE accepts a variety of omics data and corresponding analysis data, such as genomics transcriptomic, metagenomics, proteomics data and so on. A personal account is required before you upload raw data. There are three ways to accomplish the raw data uploading: HTTP, FTP, Express. An MD5 checksum file for all uploaded data is also need.',
          metadata:
            'The NODE metadata specifies the relevant information about raw data, which helps in identifying the feature of the data. For example, the publication information of a rawdata dataset is typical described in a project, sequencing methods details are showed in experiments, source material is illustrated in samples.',
          archiving:
            'In this part, rawdata have been uploaded in a storage location and metadata have been created. Rawdata is represented in the database as run objects, which can associate rawdata with metadata. Thus, the function of archiving is to link metadata with rawdata by building run objects. After archiving, the whole data publishing process is completed.',
        },
      },
      detail: {
        common: {
          publication: {
            journal: 'Journal',
            doi: 'DOI',
            pmid: 'PMID',
            title: 'Title',
            reference: 'Reference',
          },
        },
      },
    },
    data: {
      rawdata: 'RawData',
      metadata: 'MetaData',
      archiving: 'Archiving',
    },
    common: {
      dataUpload: 'Data Upload',
      route: 'Route',
      upload: 'upload',
      expressHardDrive: 'Express hard drive',
      continueSubmitMetadata: 'Continue Submit MetaData',
    },
    messages: {
      uploadSuccessful: 'Upload successful',
      fileSizeExceeded: 'The upload file size cannot exceed 200 MB!',
      expressNameRequired: 'Express Name is required',
      trackingNumberRequired: 'Tracking Number is required',
      filePathRequired: 'File Path is required',
      submittedSuccessfully: 'Submitted successfully',
      tooMuchUncheckedData:
        'You have too much data that has not been integrity checked. Currently, only the most recent 10,000 records are displayed. If you need to view other historical data, please narrow the time range and filter',
      onlyComputerSupported:
        'The data submission is only supported on computers.',
    },
    downloadTemplate: {
      title:
        'Using built-in table editor or Uploading a excel file to provide your {title} information?',
      templateDescription:
        'Template for {title} package {dataType}; version 2.0',
      downloadExcel: 'Download Excel',
      downloadExample: 'Download Example',
      attributesPagePrefix:
        'For column explanations and examples, please see the',
      attributesPageSuffix: 'attributes page.',
      uploadDescription:
        'Upload a fle using Excel that includes the attributes for each of your {titles}',
      dragUploadText: 'Drag The File Here Or',
      clickUploadText: 'Click On Upload',
      types: {
        experiment: 'experiment',
        experiments: 'experiments',
        sample: 'sample',
        samples: 'samples',
        analysis: 'analysis',
        archiving: 'archiving',
        rawData: 'raw data',
        analysisData: 'analysis data',
      },
    },
    previewPublish: {
      publications: 'Publications',
      pmidPrefix: '( PMID: ',
      doiPrefix: '( DOI: ',
    },
    recommendIcon: {
      message: 'Recommend filling in this field',
    },
    recommendTip: {
      prefix: 'recommend information：at least',
      one: 'one',
      suffix: 'of those fields is mandatory.',
    },
    resultLog: {
      title: 'Error message',
      export: 'Export',
      columns: {
        row: 'Row',
        column: 'Column',
        value: 'Value',
        errorMessage: 'Error Message',
      },
    },
    rawdata: {
      title: 'Is the data that needs to be archived in the datalist?',
      rule1:
        'Files can be compressed using {compress1} or {compress2}, and may be submitted in a tar archive but archiving and/or compressing your files is not required.',
      rule2:
        'All file names must be unique, and not contain any sensitive information. Each file must be listed in the archiving table. For sequencing data, a run contains two files (forward and reverse) in paired sequencing and one file in single sequencing.',
      rule3:
        'All files for a new submission had better put in a single directory.',
      rule4:
        'You need provide the {md5} for your files. Be sure it contains only the 32-digit MD5 value for a single file, and that its name matches the name of that file.',
      rule5:
        'You will upload files to your private ftp-home using either FTP or Express protocol and to ‘My Data->Unarchived Data’ using HTTP protocol.',
      tabs: {
        unchecked: 'Unchecked ({count})',
        checking: 'Checking ({count})',
        unarchivedData: 'Unarchived Data ({count})',
      },
      unarchivedTable: {
        search: {
          placeholder: 'Search for [Data ID, File Name, MD5]',
          period: 'Period',
          rangeSeparator: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          searchBtn: 'Search',
        },
        table: {
          dataId: 'Data ID',
          fileName: 'File Name',
          editFileName: 'Edit File Name',
          sourcePath: 'Source Path',
          dataType: 'Data Type',
          uploadType: 'Upload Type',
          fileSize: 'File Size',
          uploadDate: 'Upload Date',
          operate: 'Operate',
          delete: 'Delete',
        },
        actions: {
          export: 'Export',
          delete: 'Delete',
        },
        dialog: {
          title: 'Edit File Name',
          notice:
            'Notice: Only the file name can be edited, but the file suffix cannot be edited.',
          cancel: 'Cancel',
          confirm: 'Confirm',
        },
        messages: {
          selectFilesToDelete: 'Please select the files that need to delete',
          confirmDelete:
            'The following unarchived data will be deleted. Do you want to continue?<br>{dataIds}',
          exportAll: 'All unarchived table data will be exported',
          exportSelected: 'Selected unarchived table data will be exported',
          saving: 'saving...',
        },
      },
      uncheckedTable: {
        search: {
          placeholder: 'Search for [File Name]',
          period: 'Period',
          rangeSeparator: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          searchBtn: 'Search',
        },
        table: {
          fileName: 'File Name',
          uploadDate: 'Upload Date',
          uploadType: 'Upload Type',
          size: 'Size',
          md5File: 'MD5 File',
        },
        md5Status: {
          provided: 'Provided',
          notprovided: 'Not Provided',
          invalidFormat: 'Md5 Invalid Format',
        },
        md5Tooltip: {
          content:
            'You need provide the MD5 file for your files.<br>1) Be sure the MD5 file name matches the name of that file. e.g. sample1.fastq.gz and sample1.fastq.gz.md5.<br>2) Be sure the MD5 file contains only the 32-digit MD5 value for a single file. eg. 9ef6aeca276489b64bd777d0ca2b1e9a sample1.fastq.gz<br>3) The md5 file and its corresponding raw data file should be placed in the same file folder.',
        },
        actions: {
          dataIntegrityCheck: 'Data Integrity Check',
          export: 'Export',
          delete: 'Delete',
        },
        dialog: {
          title: 'Data Integrity Check',
          note: 'Note:',
          rule1:
            '1: You need provide the MD5 file for your files. Be sure it contains only the 32-digit MD5 value for a single file, and that its name matches the name of that file. e.g.',
          and: 'and',
          rule2:
            '2. In Linux system,{md5sum} command can be used to calculate MD5. For more information, please contact {email}',
          rule3:
            '3: After the integrity check is completed, the data is automatically moved to the {unarchivedData} of {myData}. You can view the data through data integrity checking in the "Unarchived Data" list.',
          rule4:
            '4: Please ensure that file paths and names do not include special characters such as spaces, ampersands (&), percent signs (%), asterisks (*), or Greek letters. It is recommended to use a combination of uppercase letters (A-Z), lowercase letters (a-z), numbers (0-9), underscores (_), and hyphens (-) to construct your file names.',
          rule5: '5: Integrity check takes a while, please be patient.',
          unarchivedData: 'Unarchived Data',
          myData: 'My Data',
          confirmMessage:
            'The following files will undergo data integrity verification. Do you want to continue?',
          filterWarning:
            'Files that do not provide an MD5 file will be filtered out',
          confirm: 'Confirm',
          cancel: 'Cancel',
        },
        messages: {
          noValidFileSelected:
            'No valid file selected. Only files that have provided MD5 file can be checked.',
          requestProcessing: 'Request Processing!',
          selectFilesToCheck: 'Please check the files that need to be checked',
          confirmDeleteFiles:
            'The following files will be deleted. Do you want to continue?<br>{files}',
          exportAll: 'All unchecked table data will be exported',
          exportSelected: 'Selected unchecked table data will be exported',
        },
      },
      preArchivedTable: {
        search: {
          placeholder: 'Search for [File Name]',
          period: 'Period',
          rangeSeparator: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          searchBtn: 'Search',
          exportTooltip:
            'If you do not select row, all data you search will be exported by default.',
        },
        table: {
          dataId: 'Data ID',
          fileName: 'File Name',
          analysis: 'Analysis',
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          run: 'Run',
          uploadType: 'Upload Type',
          fileSize: 'File Size',
          uploadDate: 'Upload Date',
        },
        actions: {
          export: 'Export',
          cancelArchive: 'Cancel Archive',
        },
        messages: {
          exportAll: 'All table data will be exported',
          exportSelected: 'Selected table data will be exported',
        },
        fileNames: {
          archiving: 'Archiving',
          unarchived: 'Unarchived',
        },
      },
      checkTable: {
        search: {
          placeholder: 'Search for [File Name]',
          status: 'Status',
          period: 'Period',
          rangeSeparator: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          searchBtn: 'Search',
        },
        filter: {
          status: 'Status',
          all: 'All',
          queuing: 'Queuing',
          checking: 'Checking',
          failed: 'Failed',
        },
        table: {
          fileName: 'File Name',
          uploadDate: 'Upload Date',
          status: 'Status',
          failedCause: 'Failed Cause',
        },
        status: {
          queuing: 'Queuing',
          checking: 'Checking',
          failed: 'Failed',
          checkSuccess: 'Check Success',
        },
        actions: {
          dataIntegrityCheck: 'Data Integrity Check',
          export: 'Export',
          delete: 'Delete',
        },
        messages: {
          selectFailedFiles:
            'Please select the file that failed verification and verify again',
          confirmReVerify:
            'These files will be re verified. Do you want to continue?<br>{files}',
          selectFilesToDelete: 'Please select the files that need to delete',
          confirmDelete:
            'The following files will be deleted. Do you want to continue?<br>{files}',
          exportAll: 'All checking table data will be exported',
          exportSelected: 'Selected checking table data will be exported',
        },
      },
      archivedRawDataTable: {
        search: {
          placeholder: 'Search for [File Name]',
          period: 'Period',
          rangeSeparator: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          searchBtn: 'Search',
          exportTooltip:
            'If you do not select row, all data you search will be exported by default.',
        },
        table: {
          dataId: 'Data ID',
          fileName: 'File Name',
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          run: 'Run',
          fileSize: 'File Size',
          uploadDate: 'Upload Date',
        },
        actions: {
          export: 'Export',
        },
        messages: {
          exportAll: 'All table data will be exported',
          exportSelected: 'Selected table data will be exported',
        },
        fileNames: {
          archivedRawData: 'Archived_RawData',
        },
      },
      archivedAnalysisDataTable: {
        search: {
          placeholder: 'Search for [File Name or File Path]',
          period: 'Period',
          rangeSeparator: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          searchBtn: 'Search',
          exportTooltip:
            'If you do not select row, all data you search will be exported by default.',
        },
        table: {
          dataId: 'Data ID',
          fileName: 'File Name',
          analysis: 'Analysis',
          fileSize: 'File Size',
          uploadDate: 'Upload Date',
        },
        actions: {
          export: 'Export',
        },
        messages: {
          exportAll: 'All table data will be exported',
          exportSelected: 'Selected table data will be exported',
        },
        fileNames: {
          archivedAnalysisData: 'Archived_AnalysisData',
        },
      },
    },
    metadata: {
      selectMetaData: {
        breadcrumb: 'Metadata',
        title: 'Select the option that best describes your submission',
        rawData: {
          title: 'Raw data',
          description:
            'Choose this step to archive a variety of omics data such as genomics,transcriptomic, metagenomics fastq data, proteomics raw data and so on.',
        },
        analysisData: {
          title: 'Analysis data',
          description:
            'Choose this step to archive analysis data related to omics data, such as vcf, bam, tsy, txt files and so on.',
        },
        continueBtn: 'Continue',
      },
      rawData: {
        breadcrumb: 'Metadata',
        navigation: {
          submitter: 'Submitter',
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          archiving: 'Archiving',
          multiple: 'Multiple',
          single: 'Single',
        },
        submitter: {
          title: 'Submitter',
          form: {
            firstName: 'First name',
            middleName: 'Middle name',
            lastName: 'Last name',
            organization: 'Organization',
            department: 'Department',
            piName: 'PI Name',
            email: 'Email',
            phone: 'Phone',
            fax: 'Fax',
            street: 'Street',
            city: 'City',
            stateProvince: 'State/Province',
            postalCode: 'Postal code',
            countryRegion: 'Country/Region',
            countryPlaceholder: 'Please select a country',
          },
          buttons: {
            continue: 'Continue',
            previewSave: 'Preview & Save',
            reset: 'Reset',
          },
          preview: {
            title: 'Preview',
            submitterTitle: 'Submitter',
            firstName: 'First Name',
            middleName: 'Middle Name',
            lastName: 'Last Name',
            organization: 'Organization',
            department: 'Department',
            piName: 'PI Name',
            email: 'Email',
            phone: 'Phone',
            fax: 'Fax',
            street: 'Street',
            city: 'City',
            stateProvince: 'State/Province',
            postalCode: 'Postal Code',
            countryRegion: 'Country/Region',
            save: 'Save',
            backEdit: 'Back Edit',
          },
          validation: {
            firstNameRequired: 'Please input First name',
            lastNameRequired: 'Please input Last name',
            nameLength: 'Length should be 1 to 20',
            organizationRequired: 'Please input Organization',
            emailRequired: 'Please input email address',
            emailFormat: 'Please input correct email address',
            countryRequired: 'Please select a Country/Region',
          },
          messages: {
            loading: 'Loading, please wait...',
            saveSuccess:
              'Submitter has been saved successfully, you can continue next step now!',
            saveError:
              'Failed to save "Submitter" information. Please check if the information is filled in correctly',
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
            mobileWarning:
              'The data submission is only supported on computers.',
          },
        },
        project: {
          title: 'General Information',
          form: {
            projectId: 'Project ID',
            projectIdPlaceholder:
              'Will be allocated automatically after check.',
            projectName: 'Project Name',
            projectDescription: 'Project Description',
          },
          buttons: {
            continue: 'Continue',
            previewSave: 'Preview & Save',
            reset: 'Reset',
            delete: 'Delete',
          },
          preview: {
            title: 'Preview',
            projectTitle: 'Project',
            projectId: 'Project ID',
            projectIdContent: 'Will be allocated automatically after check.',
            projectName: 'Project Name',
            description: 'Description',
            relatedLinks: 'Related Links',
            save: 'Save',
            backEdit: 'Back Edit',
          },
          deleteLog: {
            type: 'Project',
          },
          validation: {
            projectNameRequired: 'Please input Project Name',
          },
          messages: {
            saving: 'Saving data, please wait...',
            saveSuccess:
              'Project has been saved successfully, you can continue next step now!',
            deleteConfirm: 'Are you sure to delete this project ?',
            deleteSuccess: 'Delete successful',
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
          },
        },
        common: {
          publications: {
            title: 'Publications',
            form: {
              journal: 'Journal',
              doi: 'DOI',
              pmid: 'PMID',
              title: 'Title',
              reference: 'Reference',
              autoFillTooltip: 'Click to auto-fill.',
            },
            preview: {
              title: 'Preview',
              journal: 'Journal',
              doi: 'DOI',
              pmid: 'PMID',
              articleTitle: 'Title',
              reference: 'Reference',
              fillIn: 'Fill In',
              cancel: 'Cancel',
            },
            validation: {
              doiFormat: 'DOI format is incorrect',
              pmidFormat: 'PMID format is incorrect',
            },
            messages: {
              doiRequired: 'Please enter correct DOI!',
              loading: 'Loading...',
              notFound: 'Publication information not found!',
              fillSuccess: 'Publication filled successfully!',
            },
          },
          relatedLinks: {
            label: 'Related Links',
          },
          deleteLog: {
            title:
              'Delete failed. Target deletion data is already in use in the following resources.',
            export: 'Export',
            filePrefix: 'delete-fail-log',
            columns: {
              targetDeletionData: 'Target Deletion Data',
              submissionNo: 'Submission No',
              type: 'Type',
              no: 'No',
              name: 'Name',
            },
          },
        },
        experiment: {
          expSingle: {
            generalInfo: {
              title: 'General Information',
              experimentId: 'Experiment ID',
              experimentIdPlaceholder:
                'Will be allocated automatically after check.',
              experimentName: 'Experiment Name',
              projectId: 'Project ID',
              projectPlaceholder: 'Please Choose Project',
              sampleDescription: 'Sample Description',
              experimentDescription: 'Experiment Description',
              experimentProtocol: 'Experiment Protocol',
            },
            experimentInfo: {
              title: 'Experiment Information',
              description:
                'The following lists common omics experiment types. If you need additional omics types for your submission, please e-mail us {email} with a brief description of the type of data you are trying to submit, and one of our curators will quickly get back to you.',
              experimentType: 'Experiment Type',
            },
            buttons: {
              continue: 'Continue',
              previewSave: 'Preview & Save',
              reset: 'Reset',
              delete: 'Delete',
            },
            preview: {
              title: 'Preview',
              experimentTitle: 'Experiment',
              experimentId: 'Experiment ID',
              experimentIdContent:
                'Will be allocated automatically after check.',
              experimentType: 'Experiment Type',
              experimentName: 'Experiment Name',
              projectId: 'Project ID',
              description: 'Description',
              experimentProtocol: 'Experiment Protocol',
              relatedLinks: 'Related Links',
              save: 'Save',
              backEdit: 'Back Edit',
            },
            deleteLog: {
              type: 'Experiment',
            },
            validation: {
              experimentNameRequired: 'Please input Experiment Name',
              projectRequired: 'Please Choose Project',
            },
            messages: {
              insufficientData:
                'Insufficient data for recommended attributes, please continue to fill in',
              saving: 'Saving data, please wait...',
              deleteConfirm: 'Are you sure to delete this Experiment?',
              deleteSuccess: 'Delete successful',
              saveSuccess:
                'Experiment has been saved successfully, you can continue next step now!',
              antibodyInfo:
                'need to upload the antibody information table related to the experiment, put it in the created analysis, and associate it with the project.',
              unsavedConfirm:
                'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
            },
          },
          expMultiple: {
            title: 'Experimental Type Information',
            description:
              'You can submit different types of experiment information via single and multiple respectively. So you can submit up to 2 different types of experiments in a submission. If you need to submit more kinds of experiment types, you can make multiple submissions.',
            experimentType: 'Experiment Type',
            buttons: {
              continue: 'Continue',
              checkSave: 'Check & Save',
              reset: 'Reset',
              delete: 'Delete',
            },
            deleteLog: {
              type: 'Multiple Experiment',
            },
            messages: {
              antibodyInfo:
                'need to upload the antibody information table related to the experiment, put it in the created analysis, and associate it with the project.',
              unsavedConfirm:
                'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
              saving: 'Saving data, please wait...',
              deleteConfirm: 'Are you sure to delete all Multiple Experiments?',
              deleteSuccess: 'Delete successful',
              invalidCells: 'Please correct all invalid cells.',
              dataEmpty: 'The submitted data cannot be empty',
              saveSuccess:
                'Experiment has been saved successfully, you can continue next step now!',
            },
          },
        },
        sample: {
          sampleSingle: {
            sampleType: {
              title: 'Sample Type',
              collapse: 'Collapse',
              more: 'More>>',
            },
            generalInfo: {
              title: 'General Information',
              sampleName: 'Sample Name',
              organism: 'Organism',
              organismPlaceholder: 'Please input search taxonomy',
              tissue: 'Tissue',
              sampleDescription: 'Sample Description',
              sampleProcessingProtocol: 'Sample Processing Protocol',
            },
            sampleInfo: {
              title: 'Sample Information',
            },
            customAttributes: {
              title: 'The following can be filled in with custom attributes:',
              attributeLabel:
                'Attributes (Suggest starting with "attributes_")',
              attributeDescription: 'Attributes Description',
              value: 'Value',
            },
            buttons: {
              continue: 'Continue',
              previewSave: 'Preview & Save',
              reset: 'Reset',
              delete: 'Delete',
            },
            preview: {
              title: 'Preview',
              sampleTitle: 'Sample',
              sampleId: 'Sample ID',
              sampleIdContent: 'Will be allocated automatically after check.',
              sampleType: 'Sample Type',
              sampleName: 'Sample Name',
              description: 'Description',
              processingProtocol: 'Processing Protocol',
              relatedLinks: 'Related Links',
              save: 'Save',
              backEdit: 'Back Edit',
            },
            deleteLog: {
              type: 'Sample',
            },
            validation: {
              sampleNameRequired: 'Please input Sample Name',
            },
            messages: {
              insufficientData:
                'Insufficient data for recommended attributes, please continue to fill in',
              saving: 'Saving data, please wait...',
              saveSuccess:
                'Sample has been saved successfully, you can continue next step now!',
              deleteConfirm: 'Are you sure to delete this Sample?',
              deleteSuccess: 'Delete successful',
              unsavedConfirm:
                'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
            },
          },
          sampleMultiple: {
            title: 'Sample Type and Information',
            description:
              'After filling out a sample type and clicking save, you can choose a new type to continue uploading',
            buttons: {
              continue: 'Continue',
              checkSave: 'Check & Save',
              reset: 'Reset',
              delete: 'Delete',
            },
            deleteLog: {
              type: 'Multiple Sample',
            },
            messages: {
              addDataConfirm:
                'Are you sure to add the selected data to the table?',
              unsavedConfirm:
                'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
              deleteConfirm: 'Are you sure to delete all Multiple Samples?',
              deleteSuccess: 'Delete successful',
              saving: 'Saving data, please wait...',
              invalidCells: 'Please correct all invalid cells.',
              dataEmpty: 'The submitted data cannot be empty',
              saveSuccess:
                'Sample has been saved successfully, you can continue next step now!',
            },
          },
          loadSampleData: {
            dialog: {
              title: 'Select existing {sampleType} Samples',
            },
            form: {
              sampleId: 'Sample ID',
              sampleName: 'Sample Name',
              organism: 'Organism',
              tissue: 'Tissue',
              attribute: 'Attribute',
              createDate: 'Create Date',
              selectNamePlaceholder: 'Select name',
              attributeValuePlaceholder: 'attribute value',
              rangeSeparator: 'To',
              startDatePlaceholder: 'Start date',
              endDatePlaceholder: 'End date',
            },
            buttons: {
              search: 'Search',
              reset: 'Reset',
              loadToExcel: 'Load to Excel',
              cancel: 'Cancel',
            },
            table: {
              sampleId: 'Sample ID',
              sampleName: 'Sample Name',
              organism: 'Organism',
              tissue: 'Tissue',
              description: 'Description',
              createDate: 'Create Date',
            },
            selection: {
              itemsSelected: 'items are selected',
              clear: 'Clear',
            },
            messages: {
              maxSelectionExceeded: 'The selected data cannot exceed {max}',
            },
          },
        },
        archivingSingle: {
          selectData: {
            title: 'Select Data',
            unarchivedData: 'Unarchived/Archiving Data',
            archivedRawData: 'Archived Raw Data',
            archivedAnalysisData: 'Archived Analysis Data',
          },
          archiving: {
            title: 'Archiving',
            itemsSelected: 'items are selected',
            clear: 'Clear',
          },
          form: {
            title: 'Select Archiving Raw Data',
            project: 'Project',
            projectPlaceholder: 'Please select project',
            experiment: 'Experiment',
            experimentPlaceholder: 'Please select Experiment',
            sample: 'Sample',
            samplePlaceholder: 'Please select sample',
            run: 'Run',
            selectRun: 'Select Run',
            runPlaceholder: 'Select an existing Run',
            createNewRun: 'Create New Run',
            customRunName: 'Custom Run Name',
            description: 'Description',
          },
          buttons: {
            checkSave: 'Check & Save',
            submit: 'Submit',
            reset: 'Reset',
          },
          validation: {
            runNameRequired: 'Please input Run Name',
          },
          messages: {
            selectDataRequired:
              'Please select the data that needs to be archived',
            projectRequired: 'Please select Project',
            experimentRequired: 'Please select Experiment',
            sampleRequired: 'Please select Sample',
            runNameRequired: 'Please input Run Name',
            runRequired: 'Please select Run',
            saving: 'Saving data, please wait...',
            submissionIncomplete:
              "The submission information has not been completed. We recommend that you follow the navigation on the left sidebar of the page, starting with the submitter's details, and fill in the information step by step, saving as you go. ",
          },
        },
        archivingMultiple: {
          buttons: {
            checkSave: 'Check & Save',
            reset: 'Reset',
            delete: 'Delete',
          },
          tabs: {
            unarchivedData: 'Unarchived Data',
            archivingData: 'Archiving Data',
            archivedRawData: 'Archived Raw Data',
            archivedAnalysisData: 'Archived Analysis Data',
          },
          columns: {
            experimentId: {
              title: 'experiment_id',
              description:
                'For newly created experiments , the experiment ID can be left blank. To archive data for a successfully created experiment which has experiment ID already, fill in the experiment ID here. ',
            },
            experimentName: {
              title: 'experiment_name',
              description:
                'Fill in the unique name of the experiment you plan to create or the name of a experiment that has been successfully created and assigned a experiment ID. If the experiment ID and experiment name do not match, the experiment ID shall prevail and the experiment name shall be ignored.',
            },
            sampleId: {
              title: 'sample_id',
              description:
                'For newly created samples, the sample ID can be left blank. To archive data for a successfully created sample which has sample ID already, fill in the sample ID here. ',
            },
            sampleName: {
              title: 'sample_name',
              description:
                'Fill in the unique name of the sample you plan to create or the name of a sample that has been successfully created and assigned a sample ID. If the sample ID and sample name do not match, the sample ID shall prevail and the sample name shall be ignored.',
            },
            runId: {
              title: 'run_id',
              description:
                'For newly created runs, the run ID can be left blank. To archive raw data for a successfully created run which has an run ID already, fill in the run ID here. If the experiment ID and experiment name do not match, the experiment ID shall prevail and the experiment name shall be ignored.',
            },
            runName: {
              title: 'run_name',
              description:
                'Fill in the unique name of the run you plan to create or the name of a run that has been successfully created and assigned an run ID. If the run ID and run name do not match, it is considered an update to the run name.',
            },
            runDescription: {
              title: 'run_description',
              description: 'Description of the run.',
            },
            dataId: {
              title: 'data_id',
              description:
                'Fill in the data ID to be archived for raw data. For pair end sequencing data, two files (forward and reverse) are a run. For single end sequencing data, a file is a run. The data ID filled in the data_id column cannot be duplicated.',
            },
            fileName: {
              title: 'file_name',
              description:
                'If fill the file name, it will update the data`s file name.',
            },
            dataRemark: {
              title: 'data_remark',
              description: 'Remark of the data.',
            },
          },
          messages: {
            saving: 'Saving data, please wait...',
            invalidCells: 'Please correct all invalid cells.',
            deleteConfirm: 'Are you sure to delete all Multiple Archived Data?',
            deleting: 'Deleting, please wait',
            deleteSuccess: 'Cancel archived successful',
          },
        },
      },
      analysis: {
        analysisSingle: {
          generalInfo: {
            title: 'General Information',
            analysisId: 'Analysis ID',
            analysisIdPlaceholder: 'Will be generated automatically.',
            analysisName: 'Analysis Name',
            analysisDescription: 'Analysis Description',
          },
          analysisType: {
            title: 'Analysis Type',
            other: 'Other',
          },
          target: {
            title: 'Target',
          },
          pipeline: {
            title: 'Pipeline',
          },
          buttons: {
            continue: 'Continue',
            previewSave: 'Preview & Save',
            reset: 'Reset',
            delete: 'Delete',
          },
          preview: {
            title: 'Preview',
            analysisTitle: 'Analysis',
            analysisId: 'Analysis ID',
            analysisIdContent: 'Will be allocated automatically after check.',
            analysisName: 'Analysis Name',
            description: 'Description',
            analysisType: 'Analysis Type',
            targetTitle: 'Target',
            target: 'Target',
            otherTarget: 'Other Target',
            name: 'Name',
            link: 'Link',
            pipelineTitle: 'Pipeline',
            program: 'Program',
            version: 'Version',
            output: 'Output',
            notes: 'Notes',
            save: 'Save',
            backEdit: 'Back Edit',
          },
          deleteLog: {
            type: 'Analysis',
          },
          validation: {
            analysisNameRequired: 'Please input Analysis Name',
          },
          messages: {
            analysisTypeRequired: 'Please choose Analysis Type',
            otherAnalysisTypeRequired: 'Please fill Other Analysis Type',
            otherAnalysisTypeNoChinese:
              'Other Analysis Type cannot contain Chinese characters',
            targetRequired:
              'Please fill Target or Other Target , Please do not leave empty card',
            targetNoChinese: 'Target cannot contain Chinese characters',
            otherTargetRequired: 'Please fill Other Target name and link',
            otherTargetNoChinese:
              'Other Target Source cannot contain Chinese characters',
            pipelineRequired: 'Please fill Pipeline, do not leave empty card',
            pipelineNameRequired: 'Please fill Pipeline Name, name is required',
            pipelineNoChinese: 'Pipeline cannot contain Chinese characters',
            saving: 'Saving data, please wait...',
            saveSuccess:
              'Analysis has been saved successfully, you can continue next step now!',
            deleteConfirm: 'Are you sure you want to delete this Analysis?',
            deleteSuccess: 'Delete successful',
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
          },
        },
        analysisMultiple: {
          buttons: {
            continue: 'Continue',
            checkSave: 'Check & Save',
            reset: 'Reset',
            delete: 'Delete',
          },
          deleteLog: {
            type: 'Multiple Analysis',
          },
          resultLog: {
            type: 'Analysis',
          },
          columns: {
            analysisId: {
              title: 'analysis_id',
              description:
                'For newly created analyses, the analysis ID can be left blank. When modifying an analysis that has already been assigned an analysis ID, the analysis ID must be filled in. Once the analysis ID is assigned, it cannot be changed.',
            },
            analysisName: {
              title: 'analysis_name',
              description:
                'Fill in the unique name of the analysis you plan to create or the name of an analysis that has been successfully created and assigned an analysis ID. If the analysis ID and analysis name do not match, it is considered an update to the analysis name.',
            },
            description: {
              title: 'description',
              description: 'Description of the analysis.',
            },
            analysisType: {
              title: 'analysis_type',
              description:
                'The type of analysis. If the analysis type you are using is not in the selection list, you can select "Other" and fill in the analysis type in other_analysis_type column.',
              other: 'Other',
            },
            otherAnalysisType: {
              title: 'other_analysis_type',
              description:
                'Fill in the analysis type name in this column if the analysis_type is "Other".',
            },
            index: {
              title: 'index',
              description:
                'Fill in the serial number of the analysis steps. e.g. If the analysis process has three steps, the index should be 1, 2, 3.',
            },
            program: {
              title: 'program',
              description: 'Tools used in each analysis step.',
            },
            link: {
              title: 'link',
              description:
                'The related link of the tool that is used in each analysis step.',
            },
            version: {
              title: 'version',
              description:
                'The version of the tool that is used in each analysis step.',
            },
            note: {
              title: 'note',
              description: 'Notes for the analysis step.',
            },
            outputFile: {
              title: 'output_file',
              description: 'Output files generated by the analysis step.',
            },
            targetProject: {
              title: 'target_project',
              description:
                'Filling the existing project id, if the analysis is related to a project. Separate multiple associations with semicolons, e.g. OEP000001;OEP000002;OEP000003.',
            },
            targetExperiment: {
              title: 'target_experiment',
              description:
                'Filling the existing experiment id, if the analysis is related to an experiment. Separate multiple associations with semicolons, e.g. OEX000001;OEX000002;OEX000003.',
            },
            targetSample: {
              title: 'target_sample',
              description:
                'Filling the existing sample id, if the analysis is related to a sample. Separate multiple associations with semicolons, e.g. OES000001;OES000002;OES000003.',
            },
            targetAnalysis: {
              title: 'target_analysis',
              description:
                'Filling the associated analysis id, if the analysis is related to another analysis. Separate multiple associations with semicolons, e.g. OEZ000001;OEZ000002;OEZ000003.',
            },
            targetRun: {
              title: 'target_run',
              description:
                'Filling the existing run id, if the analysis is related to a run. Separate multiple associations with semicolons, e.g. OER000001;OER000002;OER000003.',
            },
            targetData: {
              title: 'target_data',
              description:
                'Filling the existing data id, if the analysis is related to a data. Separate multiple associations with semicolons, e.g. OED000001;OED000002;OED000003.',
            },
            targetOtherName: {
              title: 'target_other_name',
              description:
                'Filling the name of target, if the analysis is related to a target from other sources. Separate multiple associations with semicolons, e.g. SRA;GSA.',
            },
            targetOtherLink: {
              title: 'target_other_link',
              description:
                'Filling the link of target, if the analysis is related to a target from other sources. Separate multiple associations with semicolons.',
            },
          },
          messages: {
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
            saving: 'Saving data, please wait...',
            invalidCells: 'Please correct all invalid cells.',
            saveSuccess:
              'Analysis has been saved successfully, you can continue next step now!',
            deleteConfirm:
              'Are you sure you want to delete all Multiple Analyses?',
            deleteSuccess: 'Delete successful',
          },
        },
        archivingSingle: {
          selectData: {
            title: 'Select Data',
            unarchivedData: 'Unarchived/Archiving Data',
            archivedAnalysisData: 'Archived Analysis Data',
            archivedRawData: 'Archived Raw Data',
          },
          archiving: {
            title: 'Archiving',
            itemsSelected: 'items are selected',
            clear: 'Clear',
          },
          form: {
            title: 'Select Archiving Analysis',
            analysis: 'Analysis',
            analysisPlaceholder: 'Please select analysis',
          },
          buttons: {
            checkSave: 'Check & Save',
            submit: 'Submit',
            reset: 'Reset',
          },
          messages: {
            selectDataRequired:
              'Please select the data that needs to be archived',
            analysisRequired: 'Please select Analysis',
            saving: 'Saving data, please wait...',
            submissionIncomplete:
              "The submission information has not been completed. We recommend that you follow the navigation on the left sidebar of the page, starting with the submitter's details, and fill in the information step by step, saving as you go. ",
          },
        },
        archivingMultiple: {
          buttons: {
            checkSave: 'Check & Save',
            reset: 'Reset',
            delete: 'Delete',
          },
          tabs: {
            unarchivedData: 'Unarchived Data',
            archivingData: 'Archiving Data',
            archivedAnalysisData: 'Archived Analysis Data',
            archivedRawData: 'Archived Raw Data',
          },
          resultLog: {
            type: 'Archiving-Analysis',
          },
          columns: {
            analysisId: {
              title: 'analysis_id',
              description:
                'For newly created analysis, the analysis ID can be left blank. To archive data for a successfully created analysis which has an analysis ID already, fill in the analysis ID here.',
            },
            analysisName: {
              title: 'analysis_name',
              description:
                'Fill in the unique name of the analysis you plan to create or the name of an analysis that has been successfully created and assigned an analysis ID. If the analysis ID and analysis name do not match, the analysis ID shall prevail and the analysis name shall be ignored.',
            },
            dataId: {
              title: 'data_id',
              description:
                'Fill in the data ID to be archived for analysis. The data ID filled in the data_id column cannot be duplicated.',
            },
            fileName: {
              title: 'file_name',
              description:
                'If fill the file name, it will update the data`s file name.',
            },
            dataRemark: {
              title: 'data_remark',
              description: 'Remark of the data.',
            },
          },
          messages: {
            saving: 'Saving data, please wait...',
            invalidCells: 'Please correct all invalid cells.',
            deleteConfirm: 'Are you sure to delete all Multiple Archived Data?',
            deleting: 'Deleting, please wait',
            deleteSuccess: 'Cancel archived successful',
          },
        },
        index: {
          breadcrumb: 'Metadata',
          submitter: 'Submitter',
          analysis: 'Analysis',
          multiple: 'Multiple',
          single: 'Single',
          archiving: 'Archiving',
        },
      },
    },
    components: {
      fillTip: {
        required: 'required information',
        recommend: 'recommend information',
      },
      archivingDialog: {
        defaultMessage: 'The archive is completed.',
        submitInstruction:
          'You can click the <strong>Submit</strong> button to submit your data',
        viewResultsPrefix: 'You can also view the submission results in',
        submissionPageLink: 'my submission page',
        submit: 'Submit',
        backEdit: 'Back Edit',
        messages: {
          submissionNoEmpty: 'Submission NO cannot be empty',
          confirmSubmit: 'Confirm submitting data for {subNo}?',
          submitting: 'Submitting, please wait',
          error: 'Error',
        },
      },
      chooseData: {
        uploadData: 'UploadData',
        metaData: 'MetaData',
        archiving: 'Archiving',
      },
      mySubmissionList: {
        searchPlaceholder: 'Search for [ Submission ID ]',
        createTime: 'Create Time',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        status: 'Status',
        statusOptions: {
          all: 'All',
          editing: 'Editing',
          waitingReview: 'Waiting Review',
          reviewing: 'Reviewing',
          complete: 'Complete',
          rejected: 'Rejected',
          deleted: 'Deleted',
        },
        statusText: {
          revoke: 'revoke',
          editing: 'Editing',
          reviewing: 'Reviewing',
          waitingReview: 'Waiting review',
          processing: 'Processing',
          deleted: 'Deleted',
          complete: 'Complete',
          rejected: 'Rejected',
        },
        columns: {
          submissionId: 'Submission ID',
          dataEntries: 'Number of data entries',
          submitter: 'Submitter',
          status: 'Status',
          createTime: 'Create Time',
          lastModified: 'Last Modified',
          operate: 'Operate',
        },
        operations: {
          edit: 'Edit',
          submit: 'Submit',
          delete: 'Delete',
          revoke: 'Revoke',
        },
        rejectReason: {
          title: 'Reason for rejection',
          reasonType: 'Reason Type',
          details: 'Details',
        },
        noData: 'No Data',
        contactMessage:
          'If you need to query historical submissions data, please contact',
        messages: {
          deleteConfirm:
            'Confirm deleting the Submission: {subNo}?<br>Data cannot be retrieved after deletion, please operate with caution.<br><span class="text-danger">Please wait patiently until the deletion is fully completed. Interrupting the process may cause errors in subsequent operations.</span>',
          deleting: 'Deleting, please wait',
          deleteSuccess: 'Delete successful',
          submitConfirm: 'Confirm submitting data for {subNo}?',
          submitting: 'Submitting, please wait',
          submitSuccess: 'Submit successful',
          revokeConfirm: 'Confirm revoke the Submission: {subNo}?',
          revoking: 'Revoking, please wait',
          revokeSuccess: 'Revoke successful',
          error: 'Error',
        },
      },
    },
    route1: {
      title1: 'FTP tools upload',
      step1: {
        part1: '1. Run FTP tools e.g.',
        tools: 'Filezilla, WinsCP',
        filezilla: 'Filezilla',
        winscp: 'WinsCP',
      },
      step2:
        '2. Select "SFTP - SSH File Transfer Protocol" in the encryption options.',
      step3: {
        part1: '3. Enter: host name',
        part2: 'port',
      },
      step4: '4. Enter: user name (NODE user name), password (NODE password).',
      step5:
        '5, Put the local file to the remote FTP directory for transmission.',
      title2: 'Command-Line upload',
      popoverTitle: 'Establish an FTP connection using the credentials below:',
      command: {
        ftpEnter: 'Enter:',
        password: 'Password: [your NODE password]',
        mkdir: 'Create a subfolder(required) with a meaningful name',
        cd: 'Navigate to the target folder you just created',
        put: 'Copy your files into the target folder',
        ls: 'View the data in the current directory',
      },
      note1:
        'Make a new subdirectory for each new submission. Your submission subfolder is a temporary holding area and it will be removed once the whole submission is complete. Do not upload complex directory structures or files that do not contain sequence data.',
      note2: 'Ftp uploading method does not limit the file size.',
      note3:
        'Please use the address/port|username|password above to connect your own ftp directory.',
      note4:
        'Please ensure that file paths and names do not include special characters such as spaces, ampersands (&), percent signs (%), asterisks (*), or Greek letters. It is recommended to use a combination of uppercase letters (A-Z), lowercase letters (a-z), numbers (0-9), underscores (_), and hyphens (-) to construct your file names.',
    },
    route2: {
      selectFile: 'Select your file',
      sizeLimit: 'Size limit of 200MB',
      acceptFile: 'This page accepts direct uploading of data file.',
      forbiddenTypes: 'Prohibited file types for uploading include {blacklist}',
      sizeLimitNote: 'The size limitation of the file is 200 MB',
    },
    route3: {
      expressName: 'Express Name',
      trackingNum: 'Tracking Number',
      filePath: 'File Path',
      filePathPlaceholder: 'Path to upload on hard disk',
      note1:
        'Due to network condition, massive scale of data may not be safely uploaded. So we provide an alternative choice here.',
      note2:
        'You can express a hard drive, memory card, flash disk, anything containing the data using any reliable express.',
      note3:
        'We will return the device to you after uploading your data manually using physical interfaces.',
      contact: 'Contact us:',
      submitButton: 'Submit',
    },
  },
  home: {
    title: {
      node: 'NODE:',
      subtitle: 'National Omics Data Encyclopedia',
    },
    search: {
      placeholder: 'Search for',
      advancedSearch: 'Advanced Search',
      example: 'e.g',
    },
    cards: {
      dataSubmission: 'Data Submission',
      dataBrowsing: 'Data Browsing',
      dataStatistics: 'Data Statistics',
      dataDownload: 'Data Download',
      helpDocuments: 'Help Documents',
    },
    contact: {
      title: 'Contact Us',
      phone: '+86-21-********',
      wechatText: 'Feel free to contact {email} to join the NODE WeChat group.',
      wechatAccount: 'WeChat official account',
      address: {
        center: 'Bio-Med Big Data Center',
        institute: 'Shanghai Institute of Nutrition and Health (SINH)',
        academy: 'Chinese Academy of Sciences',
        street: '320 Yueyang Rd.',
        city: 'Shanghai 200031, China',
      },
    },
    featuredData: {
      title: 'Featured Data',
      humanResource: 'Human Resource',
      microbeResource: 'Microbe Resource',
      omicsResource: 'Omics Resource',
      more: 'More>>',
      projects: 'projects',
      experiments: 'experiments',
    },
    cite: {
      title: 'How to Cite',
      description:
        'After successfully submitting your data to NODE, we recommend using the following wording to describe the data deposition in your manuscript:',
      content:
        'All data are accessible in NODE ({originUrl}) with the accession number XXX (e.g., OEP00000073) or through the URL: {hrefUrl}',
      publicationText: 'Please cite the following publication:',
      publicationTitle:
        'Advances in multi-omics big data sharing platform research.',
      publicationJournal: 'Chinese Bulletin of Life Sciences.',
      publicationDetails: '2023, 35(12): 1553-1560. DOI:',
    },
    tools: {
      title: 'Tools',
    },
    latestData: {
      title: 'Latest Data',
      more: 'More>>',
    },
    publications: {
      title: 'Publications',
      totalNumber: 'Total Number: {count}',
    },
    hydrosphere: 'Hydrosphere',
    copySuccess: 'Copy successfully',
  },
  unauthorized: {
    title: 'No access permission',
  },
  download: {
    title: 'Data Download',
    publicDownloadServer: 'Public Download Server',
    downloadAboutToStart: 'Download is about to start',
    rawData: 'Raw Data',
    analysisData: 'Analysis Data',
    searchPlaceholder: 'Search for [ID] or [Name]',
    search: 'Search',
    operate: 'Operate',
    exportDataLinks: 'Export Data Links',
    pleaseSelectDataFirst: 'Please select Data first',
    tooltips: {
      htmlDownload: 'html download',
      sftpDownload: 'sftp download',
    },
    columns: {
      projectId: 'Project ID',
      projectName: 'Project Name',
      experimentId: 'Experiment ID',
      experimentName: 'Experiment Name',
      experimentType: 'Experiment Type',
      experimentDescription: 'Experiment Description',
      sampleId: 'Sample ID',
      sampleName: 'Sample Name',
      sampleType: 'Sample Type',
      sampleDescription: 'Sample Description',
      runId: 'Run ID',
      runName: 'Run Name',
      dataId: 'Data ID',
      dataName: 'Data Name',
      dataSize: 'Data Size',
      dataUploadTime: 'Data Upload Time',
      analysisId: 'Analysis ID',
      analysisName: 'Analysis Name',
      analysisType: 'Analysis Type',
      dataType: 'Data Type',
    },
    dialogs: {
      ftpDownload: {
        title: 'Ftp Download',
        tips: 'Tips:',
        accountTip:
          'You can also use your own NODE account and password to download data',
        ftpAddress: 'via FTP address:',
        ftpFilepath: 'via FTP filepath:',
        copyPath: 'Copy Path',
        copySuccess: 'Copy successfully',
      },
      httpDownload: {
        title: 'Http Download',
        downloadLink: 'Download link:',
        localDownload: 'Local download',
        fileSizeExceedsLimit:
          'The file size exceeds 200MB and downloading via HTTP is not allowed',
      },
    },
  },
  statistic: {
    title: 'Data Statistics',
    navigation: {
      dataVolume: 'Data Volume',
      popularData: 'Popular Data',
      publication: 'Publication',
    },
    dataVolume: {
      metadata: 'Metadata',
      project: 'Project',
      experiment: 'Experiment',
      sample: 'Sample',
      analysis: 'Analysis',
      rawData: 'Raw Data',
      analysisData: 'Analysis Data',
      dataFlow: 'Data Flow',
      accessible: 'Accessible:',
      unaccessible: 'Unaccessible:',
      items: 'items',
      number: 'Number:',
      size: 'Size:',
    },
    popularData: {
      title: 'Popular Data',
      visits: 'Visits',
      download: 'Download',
      requested: 'Requested',
    },
    publication: {
      title: 'Publication',
    },
  },
  common: {
    home: 'Home',
    submit: 'Submit',
    columnVisibility: 'Column Visibility',
    search: 'Search',
    selectAll: 'Select All',
    confirm: 'Confirm',
    default: 'Default',
    total: 'Total',
  },
  userCenter: {
    index: {
      breadcrumb: 'User Center',
      notice: {
        title: 'Notice',
        message:
          'Your password has been modified for more than 90 days. For your account security, please click',
        here: 'here',
        suffix: 'to update your password in time',
      },
      tabs: {
        myDataList: 'My Data List',
        myDataStatistics: 'My Data Statistics',
        myDataActivity: 'My Data Activity',
      },
      dataList: {
        projects: 'Projects',
        experiments: 'Experiments',
        samples: 'Samples',
        runs: 'Runs',
        analysis: 'Analysis',
        data: 'Data',
        publishes: 'Publishes',
        submissions: 'Submissions',
      },
    },
    userLeft: {
      tooltips: {
        edit: 'Edit',
        changePassword: 'Change Password',
      },
      accountStatus: {
        approved: 'Account status: Approved',
        awaitingReview: 'Account status: Awaiting Review',
        notApproved: 'Account status: Not Approved',
      },
      userInfo: {
        name: 'Name',
        organization: 'Organization',
      },
      sections: {
        share: 'Share',
        requests: 'Requests',
        reviews: 'Reviews',
      },
      links: {
        myShares: 'My Shares',
        sharesFromOthers: 'Shares from others',
        myRequests: 'My Requests',
        requestsFromOthers: 'Requests from others',
        myReviews: 'My Reviews',
      },
    },
    data: {
      analysis: {
        searchPlaceholder: 'Search for [ID or Name]',
        period: 'Period',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        other: 'Other',
        columns: {
          id: 'ID',
          name: 'Name',
          analysisType: 'Analysis Type',
          dataNumber: 'Data Number',
          description: 'Description',
          uploadDate: 'Upload Date',
          status: 'Status',
          operate: 'Operate',
        },
        status: {
          accessible: 'Accessible',
          unaccessible: 'Unaccessible',
        },
        operations: {
          edit: 'Edit',
          delete: 'Delete',
        },
        batchModifyTooltip:
          "Batch Modify: <a href='{url}/submit/metadata/analysisData'>{url}/submit/metadata/analysisData</a>",
        deleteLog: {
          type: 'Analysis',
        },
        messages: {
          openingDeleteDialog: 'Opening Delete Dialog, please wait',
          verifyingAndDeleting: 'Verifying password and deleting, please wait',
        },
      },
      data: {
        tabs: {
          unarchivedData: 'Unarchived Data',
          archivedRawData: 'Archived Raw Data',
          archivedAnalysisData: 'Archived Analysis Data',
        },
      },
      experiment: {
        searchPlaceholder: 'Search for [ID or Name]',
        period: 'Period',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        experimentType: 'Experiment Type:',
        columns: {
          id: 'ID',
          name: 'Name',
          experimentType: 'Experiment Type',
          sampleType: 'Sample Type',
          description: 'Description',
          samples: 'Samples',
          uploadDate: 'Upload Date',
          status: 'Status',
          operate: 'Operate',
        },
        status: {
          accessible: 'Accessible',
          unaccessible: 'Unaccessible',
        },
        operations: {
          edit: 'Edit',
          delete: 'Delete',
        },
        batchModifyTooltip:
          "Batch Modify: <a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
        deleteLog: {
          type: 'Experiment',
        },
        messages: {
          openingDeleteDialog: 'Opening Delete Dialog, please wait',
          verifyingAndDeleting: 'Verifying password and deleting, please wait',
        },
      },
      project: {
        searchPlaceholder: 'Search for [ID or Name]',
        period: 'Period',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        columns: {
          id: 'ID',
          name: 'Name',
          experimentType: 'Experiment Type',
          sampleType: 'Sample Type',
          description: 'Description',
          uploadDate: 'Upload Date',
          status: 'Status',
          operate: 'Operate',
        },
        status: {
          accessible: 'Accessible',
          unaccessible: 'Unaccessible',
        },
        operations: {
          edit: 'Edit',
          delete: 'Delete',
        },
        deleteLog: {
          type: 'Project',
        },
        messages: {
          openingDeleteDialog: 'Opening Delete Dialog, please wait',
          verifyingAndDeleting: 'Verifying password and deleting, please wait',
        },
      },
      publish: {
        searchPlaceholder:
          'Search for [Title, Journal, DOI, PMID or Related Items]',
        period: 'Period',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        columns: {
          title: 'Title',
          journal: 'Journal',
          doi: 'DOI',
          pmid: 'PMID',
          relatedItems: 'Related Items',
          reference: 'Reference',
          createDate: 'Create Date',
          operate: 'Operate',
        },
        operations: {
          edit: 'Edit',
          delete: 'Delete',
        },
        add: 'Add',
        messages: {
          deleteConfirm: 'Are you sure to delete this Publish?',
          deleting: 'deleting',
        },
      },
      run: {
        searchPlaceholder: 'Search for [ID or Name]',
        period: 'Period',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        columns: {
          id: 'ID',
          name: 'Name',
          experimentId: 'Experiment ID',
          sampleId: 'Sample ID',
          dataNumber: 'Data Number',
          description: 'Description',
          uploadDate: 'Upload Date',
          status: 'Status',
          operate: 'Operate',
        },
        status: {
          accessible: 'Accessible',
          unaccessible: 'Unaccessible',
        },
        operations: {
          delete: 'Delete',
        },
        batchModifyTooltip:
          "Batch Modify: <a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
        deleteLog: {
          type: 'Run',
        },
        messages: {
          openingDeleteDialog: 'Opening Delete Dialog, please wait',
          verifyingAndDeleting: 'Verifying password and deleting, please wait',
        },
      },
      sample: {
        searchPlaceholder: 'Search for [ID or Name]',
        organism: 'Organism',
        select: 'Select',
        period: 'Period',
        dateTo: 'To',
        startDate: 'Start date',
        endDate: 'End date',
        search: 'Search',
        sampleType: 'Sample Type:',
        columns: {
          id: 'ID',
          name: 'Name',
          sampleType: 'Sample Type',
          organism: 'Organism',
          experimentType: 'Experiment Type',
          description: 'Description',
          uploadDate: 'Upload Date',
          status: 'Status',
          operate: 'Operate',
        },
        status: {
          accessible: 'Accessible',
          unaccessible: 'Unaccessible',
        },
        operations: {
          edit: 'Edit',
          delete: 'Delete',
        },
        batchModifyTooltip:
          "Batch Modify: <a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
        deleteLog: {
          type: 'Sample',
        },
        messages: {
          openingDeleteDialog: 'Opening Delete Dialog, please wait',
          verifyingAndDeleting: 'Verifying password and deleting, please wait',
        },
      },
      components: {
        archivedAnalysisData: {
          searchPlaceholder: 'Search for [ID or File Name]',
          period: 'Period',
          dateTo: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          search: 'Search',
          columns: {
            dataId: 'Data ID',
            analysisId: 'Analysis ID',
            analysisName: 'Analysis Name',
            analysisType: 'Analysis Type',
            fileName: 'File Name',
            security: 'Security',
            size: 'Size',
            dataType: 'Data Type',
            uploadDate: 'Upload Date',
            operate: 'Operate',
          },
          operations: {
            htmlDownload: 'html download',
            sftpDownload: 'sftp download',
            delete: 'Delete',
          },
          batchModifyTooltip:
            "Batch Modify: <a href='{url}/submit/metadata/analysisData'>{url}/submit/metadata/analysisData</a>",
          deleteLog: {
            type: 'Data',
          },
          messages: {
            openingDeleteDialog: 'Opening Delete Dialog, please wait',
            verifyingAndDeleting:
              'Verifying password and deleting, please wait',
          },
        },
        archivedRawData: {
          searchPlaceholder: 'Search for [ID or File Name]',
          period: 'Period',
          dateTo: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          search: 'Search',
          columns: {
            dataId: 'Data ID',
            fileName: 'File Name',
            project: 'Project',
            experiment: 'Experiment',
            sample: 'Sample',
            security: 'Security',
            size: 'Size',
            dataType: 'Data Type',
            uploadDate: 'Upload Date',
            operate: 'Operate',
          },
          operations: {
            htmlDownload: 'html download',
            sftpDownload: 'sftp download',
            delete: 'Delete',
          },
          batchModifyTooltip:
            "Batch Modify: <a href='{url}/submit/metadata/rawData'>{url}/submit/metadata/rawData</a>",
          deleteLog: {
            type: 'Data',
          },
          messages: {
            openingDeleteDialog: 'Opening Delete Dialog, please wait',
            verifyingAndDeleting:
              'Verifying password and deleting, please wait',
          },
        },
        deleteConfirm: {
          title: 'Delete',
          noticeTitle: 'Notice:The following items will be deleted',
          shareWarning:
            'The following data is being shared. After deletion, the related sharing will become invalid',
          shareLabel: 'SHARE',
          passwordPlaceholder: 'Please verify your password',
          confirm: 'Confirm',
        },
        unarchivedData: {
          searchPlaceholder: 'Search for [ID or File Name]',
          period: 'Period',
          dateTo: 'To',
          startDate: 'Start date',
          endDate: 'End date',
          search: 'Search',
          columns: {
            dataId: 'Data ID',
            fileName: 'File Name',
            dataType: 'Data Type',
            uploadType: 'Upload Type',
            uploadDate: 'Upload Date',
            size: 'Size',
            operate: 'Operate',
          },
          operations: {
            delete: 'Delete',
          },
          export: 'Export',
          deleteLog: {
            type: 'Data',
          },
          messages: {
            openingDeleteDialog: 'Opening Delete Dialog, please wait',
            verifyingAndDeleting:
              'Verifying password and deleting, please wait',
            exportAllConfirm: 'All unarchived table data will be exported',
            exportSelectedConfirm:
              'Selected unarchived table data will be exported',
          },
        },
      },
    },
    dataActivity: {
      tabs: {
        viewsOfData: 'Views of Data',
        downloadOfData: 'Download of Data',
      },
      country: 'Country:',
      select: 'Select',
      chart: {
        loading: 'loading',
        legend: {
          projects: 'Projects',
          experiments: 'Experiments',
          samples: 'Samples',
          analysis: 'Analysis',
          data: 'Data',
          totalData: 'Total data',
        },
      },
    },
    edit: {
      analysis: {
        analysisSingle: {
          generalInfo: {
            title: 'General Information',
            analysisId: 'Analysis ID',
            analysisIdPlaceholder: 'Will be generated automatically.',
            analysisName: 'Analysis Name',
            analysisDescription: 'Analysis Description',
          },
          analysisType: {
            title: 'Analysis Type',
            other: 'Other',
          },
          target: {
            title: 'Target',
          },
          pipeline: {
            title: 'Pipeline',
          },
          buttons: {
            previewSave: 'Preview & Save',
            reset: 'Reset',
          },
          preview: {
            title: 'Preview',
            analysisTitle: 'Analysis',
            analysisId: 'Analysis ID',
            analysisIdContent: 'Will be allocated automatically after check.',
            analysisName: 'Analysis Name',
            description: 'Description',
            analysisType: 'Analysis Type',
            targetTitle: 'Target',
            target: 'Target',
            otherTarget: 'Other Target',
            name: 'Name:',
            link: 'Link:',
            pipelineTitle: 'Pipeline',
            program: 'Program',
            version: 'Version',
            output: 'Output',
            notes: 'Notes',
            save: 'Save',
            backEdit: 'Back Edit',
          },
          validation: {
            analysisNameRequired: 'Please input Analysis Name',
          },
          messages: {
            editingCompleted: 'Analysis Editing completed',
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
            analysisTypeRequired: 'Please choose Analysis Type',
            otherAnalysisTypeRequired: 'Please fill Other Analysis Type',
            otherAnalysisTypeNoChinese:
              'Other Analysis Type cannot contain Chinese characters',
            targetRequired:
              'Please fill Target or Other Target , Please do not leave empty card',
            targetNoChinese: 'Target cannot contain Chinese characters',
            otherTargetRequired: 'Please fill Other Target name and link',
            otherTargetNoChinese:
              'Other Target Source cannot contain Chinese characters',
            pipelineRequired: 'Please fill Pipeline, do not leave empty card',
            pipelineNameRequired: 'Please fill Pipeline Name, name is required',
            pipelineNoChinese: 'Pipeline cannot contain Chinese characters',
            saving: 'Saving data, please wait...',
            saveSuccess: 'Save successful',
            deleteConfirm: 'Are you sure you want to delete this Analysis?',
            deleteSuccess: 'Delete successful',
          },
        },
      },
      experiment: {
        expSingle: {
          generalInfo: {
            title: 'General Information',
            experimentId: 'Experiment ID',
            experimentIdPlaceholder:
              'Will be allocated automatically after check.',
            experimentName: 'Experiment Name',
            projectId: 'Project ID',
            projectPlaceholder: 'Please Choose Project',
            sampleDescription: 'Sample Description',
            experimentDescription: 'Experiment Description',
            experimentProtocol: 'Experiment Protocol',
          },
          experimentInfo: {
            title: 'Experiment Information',
            description:
              'The following lists common omics experiment types. If you need additional omics types for your submission, please e-mail us <a class="text-primary" href="mailto:{email}">{email}</a> with a brief description of the type of data you are trying to submit, and one of our curators will quickly get back to you.',
            experimentType: 'Experiment Type',
          },
          buttons: {
            previewSave: 'Preview & Save',
            reset: 'Reset',
          },
          preview: {
            title: 'Preview',
            experimentTitle: 'Experiment',
            experimentId: 'Experiment ID',
            experimentType: 'Experiment Type',
            experimentName: 'Experiment Name',
            projectId: 'Project ID',
            description: 'Description',
            experimentProtocol: 'Experiment Protocol',
            relatedLinks: 'Related Links',
            save: 'Save',
            backEdit: 'Back Edit',
          },
          validation: {
            experimentNameRequired: 'Please input Experiment Name',
            projectRequired: 'Please Choose Project',
          },
          messages: {
            editingCompleted: 'Experiment Editing completed',
            insufficientData:
              'Insufficient data for recommended attributes, please continue to fill in',
            saving: 'Saving data, please wait...',
            deleteConfirm: 'Are you sure to delete this Experiment?',
            deleteSuccess: 'Delete successful',
            saveSuccess: 'Save successful',
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
          },
        },
      },
      project: {
        generalInfo: {
          title: 'General Information',
          projectId: 'Project ID',
          projectIdPlaceholder: 'Will be allocated automatically after check.',
          projectName: 'Project Name',
          projectDescription: 'Project Description',
        },
        buttons: {
          previewSave: 'Preview & Save',
          reset: 'Reset',
        },
        preview: {
          title: 'Preview',
          projectTitle: 'Project',
          projectId: 'Project ID',
          projectName: 'Project Name',
          description: 'Description',
          relatedLinks: 'Related Links',
          save: 'Save',
          backEdit: 'Back Edit',
        },
        validation: {
          projectNameRequired: 'Please input Project Name',
        },
        messages: {
          editingCompleted: 'Project Editing completed',
          saving: 'Saving data, please wait...',
          saveSuccess: 'Save successful',
          deleteConfirm: 'Are you sure to delete this project ?',
          deleteSuccess: 'Delete successful',
          unsavedConfirm:
            'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
        },
      },
      sample: {
        sampleSingle: {
          sampleType: {
            title: 'Sample Type',
            collapse: 'Collapse',
            more: 'More>>',
          },
          generalInfo: {
            title: 'General Information',
            sampleId: 'Sample ID',
            sampleIdPlaceholder: 'Will be allocated automatically after check.',
            sampleName: 'Sample Name',
            organism: 'Organism',
            organismPlaceholder: 'Please input search taxonomy',
            tissue: 'Tissue',
            sampleDescription: 'Sample Description',
            sampleProcessingProtocol: 'Sample Processing Protocol',
          },
          sampleInfo: {
            title: 'Sample Information',
          },
          buttons: {
            previewSave: 'Preview & Save',
            reset: 'Reset',
          },
          preview: {
            title: 'Preview',
            sampleTitle: 'Sample',
            sampleId: 'Sample ID',
            sampleType: 'Sample Type',
            sampleName: 'Sample Name',
            description: 'Description',
            processingProtocol: 'Processing Protocol',
            relatedLinks: 'Related Links',
            save: 'Save',
            backEdit: 'Back Edit',
          },
          validation: {
            sampleNameRequired: 'Please input Sample Name',
          },
          messages: {
            editingCompleted: 'Sample Editing completed',
            insufficientData:
              'Insufficient data for recommended attributes, please continue to fill in',
            saving: 'Saving data, please wait...',
            saveSuccess: 'Save successful',
            deleteConfirm: 'Are you sure to delete this Sample?',
            deleteSuccess: 'Delete successful',
            unsavedConfirm:
              'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
          },
        },
      },
    },
    personal: {
      myRequest: {
        title: {
          myRequests: 'My Requests',
          requestFromOthers: 'Request From Others',
        },
        filters: {
          sort: 'Sort',
          resourceType: 'Resource Type',
          year: 'Year',
          select: 'Select',
          before: 'before',
        },
        sort: {
          requestDate: 'Request Date',
          mainId: 'Main ID',
          status: 'Status',
        },
        resourceTypes: {
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          analysis: 'Analysis',
        },
        status: {
          new: 'new',
          authorized: 'Authorized',
          requesting: 'Requesting',
          declined: 'Declined',
        },
        actions: {
          dataList: 'Data List',
          authorized: 'Authorized',
          declined: 'Declined',
        },
        table: {
          dataId: 'Data ID',
          name: 'Name',
          experiment: 'Experiment',
          sample: 'Sample',
          run: 'Run',
          analysis: 'Analysis',
        },
        dates: {
          expiryDate: 'Expiry Date',
          requestDate: 'Request Date',
          authorizationDate: 'Authorization Date',
        },
        info: {
          owner: 'Owner',
          requestor: 'Requestor',
        },
        content: {
          requestText: 'Request Text',
          replyText: 'Reply Text',
        },
        dialog: {
          comment: 'Comment',
          save: 'Save',
          cancel: 'Cancel',
        },
        messages: {
          saving: 'Saving',
        },
      },
      myReview: {
        title: 'My Reviews',
        filters: {
          sort: 'Sort',
          resourceType: 'Resource Type',
          year: 'Year',
          select: 'Select',
          before: 'before',
        },
        sort: {
          reviewDate: 'Review Date',
          reviewId: 'Review ID',
          status: 'Status',
        },
        resourceTypes: {
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          analysis: 'Analysis',
        },
        status: {
          review: 'REVIEW',
          new: 'new',
          reviewing: 'Reviewing',
          canceled: 'Canceled',
        },
        actions: {
          cancel: 'Cancel',
          extension: 'Extension',
          exportDataLinks: 'Export Data Links',
          dataIdList: 'Data ID List',
          copy: 'Copy',
        },
        counts: {
          projectCounts: 'Project Counts',
          analysisCounts: 'Analysis Counts',
          experimentCounts: 'Experiment Counts',
          sampleCounts: 'Sample Counts',
          runCounts: 'Run Counts',
          dataCounts: 'Data Counts',
        },
        info: {
          entrance: 'Entrance',
          reviewToEmail: 'Review To Email',
        },
        dates: {
          reviewDate: 'Review Date',
          expiredDate: 'Expired Date',
        },
        dialog: {
          cancelConfirm: 'Are you sure to cancel reviewing?',
          extension: 'Extension',
          extensionTips: {
            tip1: '1. The review URL will invalid after the valid period.',
            tip2: '2. We suggest setting the security as public after valid period.',
            tip3: '3. We suggest setting the valid period more than 3 months.',
          },
          validPeriod: 'Valid Period',
          pickDay: 'Pick a day',
          confirm: 'Confirm',
          cancel: 'Cancel',
        },
        messages: {
          cancelSuccess: 'Cancel successful',
          extensionSuccess: 'Extension successful',
          pickDayRequired: 'Please pick a day',
          copySuccess: 'Copy successfully',
        },
      },
      myShare: {
        title: 'My Shares',
        filters: {
          sort: 'Sort',
          resourceType: 'Resource Type',
          year: 'Year',
          select: 'Select',
          before: 'before',
        },
        sort: {
          shareDate: 'Share Date',
          shareId: 'Share ID',
          status: 'Status',
        },
        resourceTypes: {
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          analysis: 'Analysis',
        },
        status: {
          share: 'SHARE',
          new: 'new',
          sharing: 'Sharing',
          canceled: 'Canceled',
        },
        actions: {
          cancel: 'Cancel',
          exportDataLinks: 'Export Data Links',
          dataList: 'Data List',
        },
        counts: {
          projectCounts: 'Project Counts',
          analysisCounts: 'Analysis Counts',
          experimentCounts: 'Experiment Counts',
          sampleCounts: 'Sample Counts',
          runCounts: 'Run Counts',
          dataCounts: 'Data Counts',
        },
        dates: {
          shareDate: 'Share Date',
        },
        info: {
          shareToEmail: 'Share To Email',
          shareFromEmail: 'Share From Email',
        },
        dialog: {
          cancelConfirm: 'Are you sure to cancel sharing?',
          confirm: 'Confirm',
          cancel: 'Cancel',
        },
        messages: {
          cancelSuccess: 'Cancel successful',
        },
      },
      reviewDetail: {
        breadcrumb: 'Review Detail',
        title: 'Review Detail',
        error: {
          title: 'Error',
        },
        actions: {
          collapse: 'Collapse',
          expand: 'Expand',
        },
        labels: {
          type: 'Type',
          protocol: 'Protocol',
          description: 'Description',
          relatedLinks: 'Related Links',
          dataIdList: 'Data ID List',
        },
        popover: {
          experiment: 'Experiment',
          sample: 'Sample',
          run: 'Run',
          analysis: 'Analysis',
          data: 'Data',
          fileName: 'File Name',
          dataLinks: 'Data Links',
        },
      },
      shareFromOther: {
        title: 'Shares From Others',
        filters: {
          sort: 'Sort',
          resourceType: 'Resource Type',
          year: 'Year',
          select: 'Select',
        },
        sort: {
          shareId: 'Share ID',
          shareDate: 'Share Date',
          status: 'Status',
        },
        resourceTypes: {
          project: 'Project',
          experiment: 'Experiment',
          sample: 'Sample',
          analysis: 'Analysis',
        },
        status: {
          share: 'SHARE',
          sharing: 'Sharing',
        },
        actions: {
          exportDataLinks: 'Export Data Links',
          dataIdList: 'Data ID List',
        },
        dates: {
          shareDate: 'Share Date',
        },
        info: {
          shareEmail: 'Share Email',
        },
      },
    },
  },
  browse: {
    sortButtons: {
      modifiedDate: 'Modified Date',
      name: 'Name',
      submitter: 'Submitter',
      id: 'ID',
      type: 'Type',
      description: 'Description',
    },
    builderOptions: {
      general: {
        label: ' ',
        id: 'ID',
        name: 'Name',
        description: 'Description',
        usedIds: 'Used Ids',
        modifiedDate: 'Modified Date',
      },
      experiment: {
        label: 'Experiment',
        protocol: 'Protocol',
        experimentType: 'Experiment Type',
        libraryStrategy: 'Library Strategy',
        libraryLayout: 'Library Layout',
        librarySelection: 'Library Selection',
        libraryName: 'Library Name',
        platform: 'Platform',
      },
      sample: {
        label: 'Sample',
        sampleType: 'Sample Type',
        organism: 'Organism',
        tissue: 'Tissue',
        subjectId: 'Subject Id',
        biomaterialProvider: 'Biomaterial Provider',
        disease: 'Disease',
        disPhenotype: 'Dis Phenotype',
        mutationType: 'Mutation Type',
        sampleLoc: 'Sample Loc',
        gender: 'Gender',
        extractedMolType: 'Extracted Mol Type',
        devStage: 'Dev Stage',
        biome: 'Biome',
        envBiome: 'Env Biome',
        envMaterial: 'Env Material',
        envFeature: 'Env Feature',
      },
      analysis: {
        label: 'Analysis',
        analysisType: 'Analysis Type',
        pipelineProgram: 'Pipeline Program',
      },
      data: {
        label: 'Data',
        fileType: 'File Type',
      },
      publish: {
        label: 'Publish',
        doi: 'DOI',
        pmid: 'PMID',
        articleName: 'Article Name',
      },
      submitter: {
        label: 'Submitter',
        submitter: 'Submitter',
        organization: 'Organization',
        country: 'Country',
      },
      fastQc: {
        label: 'Fast QC',
        numSeqs: 'num_seqs',
        bases: 'bases',
        q20: 'Q20(%)',
        q30: 'Q30(%)',
      },
    },
    detail: {
      components: {
        association: {
          title: 'Association',
          noData: 'No Data',
          relatedExperiments: 'Related Experiments',
        },
        attributes: {
          title: 'Attributes',
          noData: 'No Data',
        },
        authorInfo: {
          title: 'Author Information',
          submission: 'Submission',
          affiliatedUnit: 'Affiliated Unit',
          createDate: 'Create Date',
          lastModified: 'Last Modified',
        },
        expSapSingleTable: {
          searchPlaceholder: 'Search for [ID]',
          search: 'Search',
          export: 'Export',
          experiment: 'Experiment',
          sample: 'Sample',
          experimentId: 'Experiment ID',
          sampleId: 'Sample ID',
          experimentName: 'Experiment Name',
          sampleName: 'Sample Name',
        },
        expSapTable: {
          experimentList: 'Experiment List',
          sampleList: 'Sample List',
        },
        generalInfo: {
          title: 'General Information',
          tooltips: {
            exportDataLinks: 'Export Data Links',
            gsaDataExport: 'Gsa Data Export',
            sraDataExport: 'Sra Data Export',
            edit: 'Edit',
            security: 'Security',
            shareTo: 'Share To',
            reviewTo: 'Review To',
            requestRestrictedDatas: 'Request Restricted Datas',
          },
          labels: {
            projectId: 'Project ID',
            relatedLinks: 'Related Links',
            publications: 'Publications',
          },
          dialog: {
            title: 'How to Cite?',
            citationIntro:
              'After successfully submitting your data to NODE, we recommend using the following wording to describe the data deposition in your manuscript:',
            citationText:
              'All data are accessible in NODE ({originUrl}) with the accession number {no} or through the URL: {hrefUrl}',
            publicationPrompt: 'Please cite the following publication:',
            unaccessibleTip: '* Tip: This {type} is Unaccessible',
            copy: 'Copy',
          },
          messages: {
            copySuccess: 'Copy successfully',
          },
        },
        gsaExport: {
          title: 'Export GSA data',
          labels: {
            security: 'Security',
            experimentType: 'Experiment type',
            experimentId: 'Experiment ID',
            subjectType: 'Subject type',
            gsaTemplate: 'GSA Template',
          },
          searchPlaceholder: 'Search for experiment ID or name',
          table: {
            experimentId: 'Experiment ID',
            experimentName: 'Experiment Name',
          },
          messages: {
            noSequencingDataSupport:
              'GSA currently does not support non sequencing data submission.',
            selectExperimentType: 'Please select at least one experiment type.',
            noExportableSubjectType:
              'There is no exportable Subject Type under the current project.',
          },
          buttons: {
            export: 'Export',
            cancel: 'Cancel',
          },
          validation: {
            selectSecurity: 'Please select security',
            selectExperimentType: 'Please select experiment type',
            selectExperiment: 'Please select experiment',
            selectSubjectType: 'Please select subject type',
          },
        },
        relatedAnalysisList: {
          title: 'Related Analysis Information',
          table: {
            analysisId: 'Analysis ID',
            analysisName: 'Analysis Name',
            analysisType: 'Analysis Type',
            data: 'Data',
            submissionDate: 'Submission Date',
          },
          tooltip: {
            clickDisplayDataInfo: 'Click display Data Information',
          },
          dialog: {
            title: 'Data Information',
            table: {
              dataId: 'Data ID',
              dataName: 'Data Name',
              dataSecurity: 'Data Security',
              dataSize: 'Data Size',
              dataUploadTime: 'Data Upload Time',
            },
          },
          messages: {
            noDataFound: 'No data is found!',
          },
        },
        relatedDataList: {
          title: 'Data List',
          export: 'Export',
          operate: 'Operate',
          publicDate: 'Public Date: ',
          tooltips: {
            htmlDownload: 'html download',
            sftpDownload: 'sftp download',
          },
          messages: {
            restrictedDataMessage:
              'This data is restricted and requires prior approval for download. Please log in to check if you have the necessary permissions.',
            tip: 'Tip',
            login: 'Login',
            cancel: 'Cancel',
          },
        },
        relatedDataQcInfoList: {
          title: 'Data QC info',
          table: {
            dataId: 'Data ID',
            dataName: 'Data Name',
            dataSecurity: 'Data Security',
            format: 'format',
            type: 'type',
            numSeqs: 'num_seqs',
            bases: 'bases',
            minLen: 'min_len',
            avgLen: 'avg_len',
            maxLen: 'max_len',
            q20: 'Q20(%)',
            q30: 'Q30(%)',
            avgQual: 'AvgQual',
            gc: 'GC(%)',
            operate: 'Operate',
          },
          tooltips: {
            viewFastqcReport: 'View FastQC Report',
            downloadFastqcReport: 'Download FastQC Report',
            requestAccessFirst:
              'To view the QC information, please request data access first.',
          },
        },
        sraExport: {
          title: 'Export SRA data',
          labels: {
            security: 'Security',
            experimentType: 'Experiment type',
            experimentId: 'Experiment ID',
            subjectType: 'Subject type',
            sraTemplate: 'SRA Template',
          },
          searchPlaceholder: 'Search for experiment ID or name',
          table: {
            experimentId: 'Experiment ID',
            experimentName: 'Experiment Name',
          },
          messages: {
            noSequencingDataSupport:
              'SRA currently does not support non sequencing data submission.',
            selectExperimentType: 'Please select at least one experiment type.',
            noExportableSubjectType:
              'There is no exportable Subject Type under the current project.',
          },
          buttons: {
            export: 'Export',
            cancel: 'Cancel',
          },
          validation: {
            selectSecurity: 'Please select security',
            selectExperimentType: 'Please select experiment type',
            selectExperiment: 'Please select experiment',
            selectSubjectType: 'Please select subject type',
          },
        },
        statDetail: {
          title: 'Statistical Details',
          labels: {
            counts: 'Counts',
            sample: 'Sample',
            run: 'Run',
            files: 'Files',
            public: 'Public',
            restricted: 'Restricted',
            private: 'Private',
          },
        },
        total: {
          title: 'Total',
          tip: '(Accessible / Total)',
        },
      },
      project: {
        breadcrumb: {
          my: 'My',
          title: 'Project Details',
        },
        error: {
          title: 'Error',
        },
        generalInfo: {
          projectId: 'Project ID',
          projectName: 'Project Name',
          description: 'Description',
          usedIds: 'Used Ids',
        },
        statistics: {
          volume: 'VOLUME',
          run: 'RUN',
          files: 'FILES',
          sample: 'SAMPLE',
        },
        statDetail: {
          experimentalType: 'Cases and File Counts by Experimental Type',
          organism: 'Cases and File Counts by Organism',
          fileTypes: 'File types and access restrictions',
        },
        columns: {
          experimentId: 'Experiment ID',
          experimentName: 'Experiment Name',
          experimentType: 'Experiment Type',
          experimentDescription: 'Experiment Description',
          sampleId: 'Sample ID',
          sampleName: 'Sample Name',
          sampleType: 'Sample Type',
          sampleDescription: 'Sample Description',
          runId: 'Run ID',
          runName: 'Run Name',
          dataId: 'Data ID',
          dataName: 'Data Name',
          dataSecurity: 'Data Security',
          dataSize: 'Data Size',
          dataUploadTime: 'Data Upload Time',
        },
      },
      experiment: {
        breadcrumb: {
          my: 'My',
          title: 'Experiment Details',
        },
        error: {
          pageTitle: 'Experiment Detail',
          title: 'Error',
        },
        generalInfo: {
          experimentId: 'Experiment ID',
          experimentType: 'Experiment Type',
          experimentName: 'Experiment Name',
          projectNo: 'Project No',
          description: 'Description',
          usedIds: 'Used Ids',
          protocol: 'Protocol',
        },
        statDetail: {
          organism: 'Cases and File Counts by Organism',
          fileTypes: 'File types and access restrictions',
        },
        columns: {
          projectId: 'Project ID',
          projectName: 'Project Name',
          projectDescription: 'Project Description',
          sampleId: 'Sample ID',
          sampleName: 'Sample Name',
          sampleType: 'Sample Type',
          sampleDescription: 'Sample Description',
          runId: 'Run ID',
          runName: 'Run Name',
          dataId: 'Data ID',
          dataName: 'Data Name',
          dataSecurity: 'Data Security',
          dataSize: 'Data Size',
          dataUploadTime: 'Data Upload Time',
        },
      },
      run: {
        breadcrumb: {
          my: 'My',
          title: 'Run Details',
        },
        content: {
          accessPrompt:
            'Do you want to access the experiment or sample associated with this run?',
        },
        error: {
          title: 'Error',
        },
      },
      sample: {
        breadcrumb: {
          my: 'My',
          title: 'Sample Details',
        },
        association: {
          title: 'Association',
          noData: 'No Data',
          relatedProject: 'Related Project',
        },
        error: {
          title: 'Error',
        },
        generalInfo: {
          sampleId: 'Sample ID',
          sampleType: 'Sample Type',
          sampleName: 'Sample Name',
          organism: 'Organism',
          tissue: 'Tissue',
          subjectId: 'Subject Id',
          description: 'Description',
          usedIds: 'Used Ids',
          protocol: 'Protocol',
        },
        statDetail: {
          experimentalType: 'Cases and File Counts by Experimental Type',
          fileTypes: 'File types and access restrictions',
        },
        columns: {
          projectId: 'Project ID',
          projectName: 'Project Name',
          projectDescription: 'Project Description',
          experimentId: 'Experiment ID',
          experimentName: 'Experiment Name',
          experimentType: 'Experiment Type',
          experimentDescription: 'Experiment Description',
          runId: 'Run ID',
          runName: 'Run Name',
          dataId: 'Data ID',
          dataName: 'Data Name',
          dataSecurity: 'Data Security',
          dataSize: 'Data Size',
          dataUploadTime: 'Data Upload Time',
        },
      },
      analysis: {
        breadcrumb: {
          my: 'My',
          title: 'Analysis Details',
        },
        error: {
          title: 'Error',
        },
        generalInfo: {
          analysisId: 'Analysis ID',
          analysisName: 'Analysis Name',
          analysisType: 'Analysis Type',
          description: 'Description',
          usedIds: 'Used Ids',
        },
        statistics: {
          volume: 'VOLUME',
          files: 'FILES',
        },
        pipeline: {
          title: 'Pipeline',
          label: 'Pipeline',
          columns: {
            program: 'Program',
            version: 'Version',
            notes: 'Notes',
            link: 'Link',
          },
          output: {
            title: 'Output',
            columns: {
              dataId: 'Data ID',
              dataName: 'Data Name',
              dataType: 'Data Type',
              uploadTime: 'Upload Time',
              security: 'Security',
              md5: 'MD5',
              operate: 'Operate',
            },
            tooltips: {
              htmlDownload: 'html download',
              sftpDownload: 'sftp download',
            },
          },
        },
        target: {
          title: 'Target',
          label: 'Target',
          otherTarget: 'Other Target',
        },
        columns: {
          dataId: 'Data ID',
          dataName: 'Data Name',
          dataType: 'Data Type',
          dataSecurity: 'Data Security',
          dataSize: 'Data Size',
          dataUploadTime: 'Data Upload Time',
        },
      },
    },
    index: {
      breadcrumb: {
        browse: 'Browse',
        advancedSearch: 'Advanced Search',
      },
      search: {
        placeholder: 'Search for',
        advancedSearch: 'Advanced Search',
        example: 'e.g',
      },
      advancedSearch: {
        title: 'NODE Advanced Search Builder',
        builderPlaceholder: 'Use the builder below to create your search',
        cancel: 'Cancel',
        edit: 'Edit',
        clear: 'Clear',
        builder: 'Builder',
        pleaseSelect: 'Please select',
        datePicker: {
          to: 'To',
          startDate: 'Start date',
          endDate: 'End date',
        },
        boolOptions: {
          yes: 'Yes',
          no: 'No',
        },
        search: 'Search',
        close: 'Close',
      },
      filters: {
        access: 'Access',
        more: 'More>',
        less: 'Less',
        showAdditionalFilters: 'Show Additional Filters',
        additionalFilters: 'Additional Filters',
        confirm: 'Confirm',
      },
      tabs: {
        verboseMode: 'Verbose Mode',
        conciseList: 'Concise List',
      },
      content: {
        usedId: 'Used ID:',
        experiments: 'Experiments',
        samples: 'Samples',
        files: 'Files',
        experimentType: 'Experiment type',
        sampleType: 'Sample type',
        analysisType: 'Analysis type',
        organism: 'Organism',
        tissue: 'Tissue',
        description: 'Description',
        noDataMessage:
          'Data is non-existent, not publicly available, or retracted.',
      },
      table: {
        columns: {
          id: 'ID',
          type: 'Type',
          name: 'Name',
          description: 'Description',
          submitter: 'Submitter',
          modifiedDate: 'Modified Date',
        },
      },
      actions: {
        exportDataLinks: 'Export Data Links',
      },
    },
  },
  featureData: {
    index: {
      breadcrumb: 'Feature Data',
      navigation: {
        humanResource: 'Human Resource',
        microbeResource: 'Microbe Resource',
        omicsResource: 'Omics Resource',
        hydrosphere: 'Hydrosphere',
      },
    },
    hmdsNsfcDataSet: {
      title: 'Hydrosphere',
      units: {
        tbase: 'Tbase',
        samples: 'Samples',
      },
      table: {
        title: 'Accessible Sample List',
        columns: {
          sampleId: 'Sample ID',
          sampleName: 'Sample Name',
          description: 'Description',
          sampleType: 'Sample Type',
          organism: 'Organism',
          curatedBiome: 'Curated Biome',
        },
        notProvided: 'Not provided',
      },
    },
    humanRes: {
      titles: {
        humanResource: 'Human Resource',
        microbeResource: 'Microbe Resource',
      },
      statistics: {
        project: 'project',
        samples: 'samples',
        sampleTypes: 'sample types',
        experiments: 'experiments',
        experimentTypes: 'experiment types',
      },
      tables: {
        projectList: 'Project List',
        sampleList: 'Sample List',
        columns: {
          projectId: 'Project ID',
          projectName: 'Project Name',
          description: 'Description',
          experimentType: 'Experiment Type',
          sampleId: 'Sample ID',
          sampleName: 'Sample Name',
          sampleType: 'Sample Type',
          organism: 'Organism',
          tissue: 'Tissue',
        },
      },
    },
    omics: {
      index: {
        tabs: {
          multipleOmicsResource: 'Multiple Omics Resource',
          multipleSampleResource: 'Multiple Sample Resource',
          singleSampleMultiOmics: 'Single sample multi-omics',
        },
      },
      omicsRes: {
        searchPlaceholder: 'Search for ID',
        searchButton: 'Search',
        columns: {
          projectId: 'Project ID',
          sampleId: 'Sample ID',
        },
        filterTypes: {
          sampleType: 'Sample Type',
          experimentType: 'Experiment Type',
        },
      },
      sampleOmics: {
        searchPlaceholder: 'Search for ID',
        searchButton: 'Search',
        filterType: 'Experiment Type',
        columns: {
          sampleId: 'Sample ID',
          sampleType: 'Sample Type',
          genomic: 'Genomic',
          transcriptomic: 'Transcriptomic',
          transcriptomicSingleCell: 'Transcriptomic single cell',
          microarray: 'Microarray',
          proteomic: 'Proteomic',
          metagenomic: 'Metagenomic',
          metatranscriptomic: 'Metatranscriptomic',
          metabolomic: 'Metabolomic',
          other: 'Other',
        },
      },
      sampleRes: {
        searchPlaceholder: 'Search for ID',
        searchButton: 'Search',
        filterType: 'Sample Type',
        columns: {
          projectId: 'Project ID',
          human: 'Human',
          animalia: 'Animalia',
          plantae: 'Plantae',
          pathogenAffectingPublicHealth: 'Pathogen affecting public health',
          cellLine: 'Cell Line',
          environmentHost: 'Environment Host',
          environmentNonHost: 'Environment non-host',
          microbe: 'Microbe',
        },
      },
    },
  },
  submissionDetail: {
    status: {
      dataProcessing: 'Data Processing',
      waitingReview: 'Waiting Review',
      rejected: 'Rejected',
    },
    info: {
      lastModify: 'Last modify',
      auditor: 'Auditor',
    },
    rejection: {
      title: 'Reason for rejection',
    },
    sections: {
      project: 'Project',
      experiment: 'Experiment',
      sample: 'Sample',
      archiving: 'Archiving',
      analysis: 'Analysis',
      publications: 'Publications',
      submitter: 'Submitter',
    },
    project: {
      projectId: 'Project ID',
      projectName: 'Project Name',
      projectDescription: 'Project Description',
      relatedLinks: 'Related Links {index}',
      autoAllocated: 'Will be allocated automatically after check.',
    },
    publications: {
      relatedId: 'Related ID',
      relatedName: 'Related Name',
      publishTitle: 'Publish Title',
      creator: 'Creator',
      publishCreateDate: 'Publish Create Date',
    },
    submitter: {
      firstName: 'First Name',
      middleName: 'Middle Name',
      lastName: 'Last Name',
      organization: 'Organization',
      department: 'Department',
      piName: 'PI Name',
      email: 'Email',
      phone: 'Phone',
      fax: 'Fax',
      street: 'Street',
      city: 'City',
      stateProvince: 'State/Province',
      postalCode: 'Postal code',
      countryRegion: 'Country/Region',
    },
    buttons: {
      submit: 'Submit',
      edit: 'Edit',
      delete: 'Deleted',
      back: 'Back',
    },
    messages: {
      confirmDelete: 'Confirm deleting the Submission: {subNo}?',
      deleteWarning:
        'Data cannot be retrieved after deletion, please operate with caution',
      deleting: 'Deleting, please wait',
      deleteSuccess: 'Delete successful',
      submissionNoEmpty: 'Submission NO cannot be empty',
      confirmSubmit: 'Confirm submitting data for {subNo}?',
      submitting: 'Submitting, please wait',
      error: 'Error',
    },
  },
  hotTable: {
    labels: {
      type: 'Type',
    },
    buttons: {
      columnVisibility: 'Column Visibility',
      selectAll: 'Select All',
      confirm: 'Confirm',
      default: 'Default',
      export: 'Export',
    },
    placeholders: {
      searchColumn: 'Search column',
    },
  },
  expSapAttrDialog: {
    title: '{baseType} Attributes ({currType})',
    labels: {
      attributeName: 'Attribute Name',
    },
    table: {
      attributeName: 'Attribute Name',
      description: 'Description',
      valueFormat: 'Value Format',
    },
    placeholders: {
      searchTaxonomy: 'Please input search taxonomy',
      search: 'Please input search',
      searchDisease: 'Please input search disease',
      select: 'Select',
    },
  },
  pipeline: {
    step: 'Step-{step}',
    form: {
      program: 'Program',
      link: 'Link',
      version: 'Version',
      output: 'Output',
      notes: 'Notes',
    },
    placeholders: {
      selectOutput: 'Please select a output',
    },
    options: {
      noData: 'No data',
    },
  },
  target: {
    title: 'Target-{index}',
    types: {
      projectId: 'Project ID',
      experimentId: 'Experiment ID',
      sampleId: 'Sample ID',
      runId: 'Run ID',
      dataId: 'Data ID',
      analysisId: 'Analysis ID',
    },
    form: {
      id: 'ID',
    },
    placeholders: {
      selectId: 'Please select {type}',
      textareaExample:
        'Enter identifiers, separated by spaces or new lines, into the form field, for example: \n{example}',
    },
    options: {
      noData: 'No data',
    },
  },
  otherTarget: {
    title: 'Other Target Source',
    labels: {
      name: 'Name',
      link: 'Link',
    },
  },
};
