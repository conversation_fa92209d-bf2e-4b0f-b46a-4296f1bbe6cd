<template>
  <div v-if="showPublish">
    <el-divider v-if="showTitle" content-position="left"
      ><h3 class="preview-title">Publications</h3></el-divider
    >

    <div
      v-for="(it, index) in publishData"
      :key="'publish-preview-' + index"
      class="content bg-gray plr-20 radius-8 p-10-15"
      :class="index !== 0 ? 'mt-1' : ''"
    >
      <p
        class="text-secondary-color font-600 align-items-center"
        :class="publishData.length === 1 ? 'ml-1' : ''"
      >
        <template
          v-for="(item, idx) in it.relatedId"
          :key="'home-publish-' + idx"
        >
          <router-link
            v-if="item.startsWith('OEP')"
            :to="`/project/detail/${item}`"
            target="_blank"
          >
            <el-tag class="ml-05 tag-samp mr-05" round style="width: 90px">{{
              item
            }}</el-tag>
          </router-link>

          <router-link
            v-if="item.startsWith('OEZ')"
            :to="`/analysis/detail/${item}`"
            target="_blank"
          >
            <el-tag class="ml-05 tag-samp mr-05" round style="width: 90px">{{
              item
            }}</el-tag>
          </router-link>

          <router-link
            v-if="item.startsWith('OEX')"
            :to="`/experiment/detail/${item}`"
            target="_blank"
          >
            <el-tag class="ml-05 tag-samp mr-05" round style="width: 90px">{{
              item
            }}</el-tag>
          </router-link>

          <router-link
            v-if="item.startsWith('OES')"
            :to="`/sample/detail/${item}`"
            target="_blank"
          >
            <el-tag class="ml-05 tag-samp mr-05" round style="width: 90px">{{
              item
            }}</el-tag>
          </router-link>
        </template>
        <span class="font-600 pub-title" v-html="it.articleName"></span>
      </p>
      <p class="text-other-color reference ml-1 pub-title" v-html="it.reference"></p>
      <p class="ml-05 pub-title">
        <span v-if="it.pmid" class="mr-1">
          <el-tag
            v-if="showJournal && it.publication"
            round
            type="success"
            effect="light"
            class="mr-1"
            v-html="it.publication"
          >
          </el-tag>
          <span>( PMID: </span>
          <svg-icon icon-class="link" class-name="svg svg-link"></svg-icon>
          <a
            target="_blank"
            :href="`https://pubmed.ncbi.nlm.nih.gov/${it.pmid}/`"
            class="text-success cursor-pointer"
          >
            &nbsp;{{ `${it.pmid}` }}</a
          >
          )
        </span>
        <span v-if="it.doi">
          <span>( DOI: </span>
          <svg-icon icon-class="link" class-name="svg svg-link"></svg-icon>
          <a
            target="_blank"
            :href="`https://doi.org/${it.doi}`"
            class="text-success cursor-pointer"
            >&nbsp;{{ it.doi }}</a
          >
          )
        </span>
      </p>
    </div>
  </div>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue';

  const props = defineProps({
    showTitle: {
      type: Boolean,
      required: false,
      default: true,
    },
    showJournal: {
      type: Boolean,
      required: false,
      default: true,
    },
    publishData: {
      type: Array,
    },
  });

  const tagClass = id => {
    if (id.includes('project')) {
      return 'tag-proj';
    } else if (id.includes('experiment')) {
      return 'tag-expr';
    } else if (id.includes('sample')) {
      return 'tag-samp';
    } else {
      return 'tag-anal';
    }
  };

  const showPublish = ref(false);

  onMounted(() => {
    updatePublish(props.publishData);
  });

  function updatePublish(newValue) {
    showPublish.value = false;

    if (newValue && newValue.length > 0) {
      newValue.forEach(obj => {
        for (const key in obj) {
          if (
            key !== 'id' &&
            key !== 'relatedId' &&
            key !== 'sort' &&
            obj[key] &&
            obj[key].trim() !== ''
          ) {
            showPublish.value = true;
          }
        }
      });
    }
  }

  watch(props.publishData, newValue => {
    updatePublish(newValue);
  });
</script>

<style scoped>
  .svg-link {
    width: 12px;
    height: 12px;
  }
  @media (max-width: 767px) {
    .pub-title{
      word-break: break-all;
      text-align: justify;
      margin-left: 0;
      padding-bottom: .5rem;
    }
    .el-tag{
      margin-left: 0!important;
    }

  }
</style>
