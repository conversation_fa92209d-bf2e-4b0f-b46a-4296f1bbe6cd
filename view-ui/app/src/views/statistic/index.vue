<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="Statistic" />
      <el-row :gutter="15" class="mt-1">
         <el-col :span="3" :xs="24" :md="3" class="mb-1">
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'DataVolume' }"
            @click="isActive = 'DataVolume'"
          >
            Data Volume
          </div>
          <el-divider />
          <!--          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive == 'ExperimentType' }"
            @click="isActive = 'ExperimentType'"
          >
            Experiment Type
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive == 'SampleType' }"
            @click="isActive = 'SampleType'"
          >
            Sample Type
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive == 'Multiomics' }"
            @click="isActive = 'Multiomics'"
          >
            Multiomics
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive == 'DataType' }"
            @click="isActive = 'DataType'"
          >
            Data Type
          </div>
          <el-divider />-->
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'PopularData' }"
            @click="isActive = 'PopularData'"
          >
            Popular Data
          </div>
          <el-divider />
          <!--          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive == 'DataShare' }"
            @click="isActive = 'DataShare'"
          >
            Data Share
          </div>
          <el-divider />-->
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'Literature' }"
            @click="isActive = 'Literature'"
          >
            Publication
          </div>
        </el-col>
        <el-col :span="21" :xs="24" :md="21"  class="h-100">
          <keep-alive>
            <component :is="tabs[isActive]"></component>
          </keep-alive>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import DataVolume from '@/views/statistic/dataVolume.vue';
  import ExperimentType from '@/views/statistic/experimentType.vue';
  import SampleType from '@/views/statistic/sampleType.vue';
  import Multiomics from '@/views/statistic/multiomics.vue';
  import PopularData from '@/views/statistic/popularData.vue';
  import DataShare from '@/views/statistic/dataShare.vue';
  import Literature from '@/views/statistic/literature.vue';
  import DataType from '@/views/statistic/dataType.vue';

  import { ref } from 'vue';

  const tabs = {
    DataVolume,
    ExperimentType,
    SampleType,
    Multiomics,
    PopularData,
    DataShare,
    Literature,
    DataType,
  };

  const isActive = ref('DataVolume');
</script>

<style lang="scss" scoped>
  .submitData {
    .before-circle {
      &.active {
        background-color: #fff;
        border-radius: 14px;
        color: #3a78e8;
        /* padding: 0 20px; */
        border: 1px solid #3a78e8;
        padding: 0 15px;
        text-align: center;

        &:before {
          display: none;
          background-color: #3a78e8 !important;
        }
      }

      &:before {
        background-color: #999999 !important;
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;
      }
    }
  }
</style>
