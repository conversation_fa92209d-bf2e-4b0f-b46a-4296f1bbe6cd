<template>
  <div class="metadata-item-container">
    <div :id="id" class="chart-container"></div>

    <div class="info-container">
      <div class="info-row">
        <span class="before-circle font-600 accessible">Accessible:</span>
        <span class="font-600 ml-05"
          >{{
            ((accessible / (accessible + unAccessible)) * 100).toFixed(2)
          }}%</span
        >
        <span class="mr-05 font-16 ml-05">{{ formatNumber(accessible) }}</span>
        <span class="font-14 text-other-color">items</span>
      </div>
      <div class="info-row">
        <span class="before-circle font-600 unaccessible">Unaccessible:</span>
        <span class="font-600 ml-05"
          >{{
            ((unAccessible / (accessible + unAccessible)) * 100).toFixed(2)
          }}%</span
        >
        <span class="mr-05 ml-05">{{ formatNumber(unAccessible) }}</span>
        <span class="font-14 text-other-color">items</span>
      </div>
    </div>
  </div>
</template>

<script setup name="pieChart">
  import * as echarts from 'echarts';
  import { defineProps, nextTick, onMounted, onUnmounted } from 'vue';
  import { formatNumber } from '@/utils/nodeCommon';

  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Array,
    },
    title: {
      type: String,
    },
    totalSize: {
      type: String,
    },
    accessible: {
      type: Number,
    },
    accessibleSize: {
      type: String,
    },
    unAccessible: {
      type: Number,
    },
    unAccessibleSize: {
      type: String,
    },
  });
  const echartInit = () => {
    const pieChart = echarts.init(document.getElementById(props.id));

    // 根据屏幕尺寸调整字体大小
    const getResponsiveFontSize = () => {
      const width = window.innerWidth;
      if (width <= 576) {
        return { title: 16, subtitle: 12 };
      } else if (width <= 768) {
        return { title: 18, subtitle: 13 };
      } else {
        return { title: 22, subtitle: 14 };
      }
    };

    const fontSize = getResponsiveFontSize();

    const option = {
      color: ['#3A78E8', '#2ADAC9'],
      title: {
        text: props.title,
        subtext: `${props.totalSize}\n${formatNumber(
          props.accessible + props.unAccessible,
        )} items`,
        textStyle: {
          fontSize: fontSize.title,
          rich: {
            a: {
              fontSize: fontSize.title,
              color: '#FFF',
            },
          },
        },
        subtextStyle: {
          fontSize: fontSize.subtitle,
          color: '#999999',
          padding: [5, 10],
        },
        textVerticalAlign: 'top',
        x: 'center',
        y: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          return `<span class="mr-1">${params.name}</span>
<!--                  <span>${params.percent + '%'}</span>-->
                  <hr>
                   <div class="d-flex justify-space-between">
                    <span class="mr-1">Number:</span>
                    <span>${params.value}</span>
                  </div>
                  <div class="d-flex justify-space-between">
                    <span class="mr-1">Size:</span>
                    <span>${params.data.size}</span>
                  </div>`;
        },
      },
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          center: ['50%', '50%'],
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: props.accessible,
              name: 'Accessible',
              size: props.accessibleSize,
            },
            {
              value: props.unAccessible,
              name: 'Unaccessible',
              size: props.unAccessibleSize,
            },
          ],
        },
      ],
    };
    pieChart.setOption(option);

    // 改进的窗口大小调整处理
    const handleResize = () => {
      pieChart.resize();
      // 当窗口大小改变时，重新计算字体大小并更新图表
      const newFontSize = getResponsiveFontSize();
      const newOption = {
        ...option,
        title: {
          ...option.title,
          textStyle: {
            ...option.title.textStyle,
            fontSize: newFontSize.title,
          },
          subtextStyle: {
            ...option.title.subtextStyle,
            fontSize: newFontSize.subtitle,
          },
        },
      };
      pieChart.setOption(newOption);
    };

    window.addEventListener('resize', handleResize);

    // 清理事件监听器
    const cleanup = () => {
      window.removeEventListener('resize', handleResize);
      pieChart.dispose();
    };

    // 返回清理函数供组件卸载时使用
    return cleanup;
  };
  let cleanupFunction = null;

  onMounted(() => {
    nextTick(() => {
      cleanupFunction = echartInit();
    });
  });

  onUnmounted(() => {
    if (cleanupFunction) {
      cleanupFunction();
    }
  });
</script>

<style scoped lang="scss">
  .metadata-item-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    min-height: 280px;

    @media (max-width: 768px) {
      min-height: 250px;
      padding: 8px;
    }

    @media (max-width: 576px) {
      min-height: 220px;
      padding: 5px;
    }
  }

  .chart-container {
    width: 100%;
    height: 200px;
    flex-shrink: 0;

    @media (max-width: 768px) {
      height: 180px;
    }

    @media (max-width: 576px) {
      height: 160px;
    }
  }

  .info-container {
    width: 100%;
    max-width: 300px;
    margin-top: 10px;
    padding: 0 10px;

    @media (max-width: 768px) {
      max-width: 280px;
      padding: 0 5px;
    }

    @media (max-width: 576px) {
      max-width: 100%;
      padding: 0;
    }
  }

  .info-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin-bottom: 8px;
    text-align: left;

    @media (max-width: 576px) {
      font-size: 14px;
      margin-bottom: 6px;
    }
  }

  .accessible {
    display: inline-block;
    width: 103px;
    text-align: left;
    flex-shrink: 0;

    @media (max-width: 576px) {
      width: 90px;
      font-size: 13px;
    }
  }

  .unaccessible {
    flex-shrink: 0;

    @media (max-width: 576px) {
      font-size: 13px;
    }
  }

  .accessible:before,
  .unaccessible:before {
    content: '';
    display: inline-block;
    height: 6px;
    width: 6px;
    border-radius: 50%;
    margin-right: 8px;
    vertical-align: middle;

    @media (max-width: 576px) {
      height: 5px;
      width: 5px;
      margin-right: 6px;
    }
  }

  .accessible:before {
    background: #3a78e8;
  }

  .unaccessible:before {
    background: #2adac9;
  }
</style>
