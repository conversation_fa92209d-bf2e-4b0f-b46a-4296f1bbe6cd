<template>
  <div class="analyse-page">
    <div class="container-fluid">
      <Breadcrumb :bread-item="t('help.breadcrumb')" />

      <el-row :gutter="20" class="mt-1 helps">
        <el-col :span="6" class="hidden-xs-only">
          <div class="card plr-0" style="position: sticky; top: 30px">
            <!--            <ul id="navbar-example" class="pb-1">-->
            <el-tree :data="toc" :props="defaultProps" default-expand-all>
              <template #default="{ data }">
                <ul>
                  <li
                    :class="[
                      `layer-${data.layer}`,
                      { active: activeId === data.id },
                    ]"
                  >
                    <a
                      class="d-flex"
                      :title="data.title"
                      @click="handleClick(data.id)"
                    >
                      <span class="font-600 mr-03">{{ data.sort }}</span>

                      <span
                        v-if="data.subtitle"
                        class="text-warning font-600 font-14"
                        >( {{ data.subtitle }} )</span
                      >
                      <span
                        style="
                          display: inline-block;
                          white-space: normal;
                          width: 100%;
                          overflow-wrap: break-word;
                        "
                        class="font-600"
                        >{{ data.title }}</span
                      >
                    </a>
                  </li>
                </ul>
              </template>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="18" :xs="24" :sm="24" :md="18">
          <div class="card">
            <h4 id="help-1" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 1 </el-tag>
              {{ t('help.overview.title') }}
            </h4>
            <p>
              {{ t('help.overview.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help1.png" alt="" />
            </div>
            <h5 id="help-1-1" class="text-primary font-16">
              {{ t('help.overview.experimentType.title') }}
            </h5>
            <p class="mb-03 text-main-color">
              {{ t('help.overview.experimentType.description') }}
            </p>
            <ul>
              <li>
                <p>
                  (1) <b>Genomic:</b>
                  {{ t('help.overview.experimentType.types.genomic') }}
                </p>
              </li>
              <li>
                <p>
                  (2) <b>Transcriptomic:</b>
                  {{ t('help.overview.experimentType.types.transcriptomic') }}
                </p>
              </li>
              <li>
                <p>
                  (3) <b>Metagenomic:</b>
                  {{ t('help.overview.experimentType.types.metagenomic') }}
                </p>
              </li>
              <li>
                <p>
                  (4) <b>Metatranscriptomic:</b>
                  {{
                    t('help.overview.experimentType.types.metatranscriptomic')
                  }}
                </p>
              </li>
              <li>
                <p>
                  (5) <b>Genomic single cell:</b>
                  {{
                    t('help.overview.experimentType.types.genomicSingleCell')
                  }}
                </p>
              </li>
              <li>
                <p>
                  (6) <b>Transcriptomic single cell:</b>
                  {{
                    t(
                      'help.overview.experimentType.types.transcriptomicSingleCell',
                    )
                  }}
                </p>
              </li>
              <li>
                <p>
                  (7) <b>Proteomic:</b>
                  {{ t('help.overview.experimentType.types.proteomic') }}
                </p>
              </li>
              <li>
                <p>
                  (8) <b>Metabolomic:</b>
                  {{ t('help.overview.experimentType.types.metabolomic') }}
                </p>
              </li>
              <li>
                <p>
                  (9) <b>Metabolomic-NMR:</b>
                  {{ t('help.overview.experimentType.types.metabolomicNMR') }}
                </p>
              </li>
              <li>
                <p>
                  (10) <b>Electron microscopy:</b>
                  {{
                    t('help.overview.experimentType.types.electronMicroscopy')
                  }}
                </p>
              </li>
              <li>
                <p>
                  (11) <b>Microarray:</b>
                  {{ t('help.overview.experimentType.types.microarray') }}
                </p>
              </li>
              <li>
                <p>
                  (12) <b>Synthetic:</b>
                  {{ t('help.overview.experimentType.types.synthetic') }}
                </p>
              </li>
              <li>
                <p>
                  (13) <b>Viral RNA:</b>
                  {{ t('help.overview.experimentType.types.viralRNA') }}
                </p>
              </li>
              <li>
                <p>
                  (14) <b>Flow cytometry:</b>
                  {{ t('help.overview.experimentType.types.flowCytometry') }}
                </p>
              </li>
            </ul>
            <h5 id="help-1-2" class="text-primary font-16">
              {{ t('help.overview.sampleType.title') }}
            </h5>
            <p class="mb-03 text-main-color">
              {{ t('help.overview.sampleType.description') }}
            </p>
            <ul>
              <li>
                <p>
                  (1) <b>Human:</b>
                  {{ t('help.overview.sampleType.types.human') }}
                </p>
              </li>
              <li>
                <p>
                  (2) <b>Animalia:</b>
                  {{ t('help.overview.sampleType.types.animalia') }}
                </p>
              </li>
              <li>
                <p>
                  (3) <b>Plant:</b>
                  {{ t('help.overview.sampleType.types.plant') }}
                </p>
              </li>
              <li>
                <p>
                  (4) <b>Cell line:</b>
                  {{ t('help.overview.sampleType.types.cellLine') }}
                </p>
              </li>
              <li>
                <p>
                  (5) <b>Microbe:</b>
                  {{ t('help.overview.sampleType.types.microbe') }}
                </p>
              </li>
              <li>
                <p>
                  (6) <b>Environment host:</b>
                  {{ t('help.overview.sampleType.types.environmentHost') }}
                </p>
              </li>
              <li>
                <p>
                  (7) <b>Environment non-host:</b>
                  {{ t('help.overview.sampleType.types.environmentNonHost') }}
                </p>
              </li>
              <li>
                <p>
                  (8) <b>Pathogen affecting public health:</b>
                  {{ t('help.overview.sampleType.types.pathogen') }}
                </p>
              </li>
            </ul>

            <el-divider> </el-divider>

            <h4 id="help-2" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 2 </el-tag>
              {{ t('help.registerLogin.title') }}
            </h4>
            <h5 id="help-3" class="text-primary font-16">
              {{ t('help.registerLogin.signUp.title') }}
            </h5>
            <p class="mb-03 text-main-color">
              {{ t('help.registerLogin.signUp.step1.title') }}
            </p>
            <p>
              {{ t('help.registerLogin.signUp.step1.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-1.png" alt="" />
            </div>

            <p class="mb-03 text-main-color">
              {{ t('help.registerLogin.signUp.step2.title') }}
            </p>
            <p>{{ t('help.registerLogin.signUp.step2.description') }}</p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-2.png" alt="" />
            </div>

            <p class="mb-03 text-main-color">
              {{ t('help.registerLogin.signUp.step3.title') }}
            </p>
            <p>
              {{ t('help.registerLogin.signUp.step3.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-3.png" alt="" />
            </div>

            <p class="mb-03 text-main-color">
              {{ t('help.registerLogin.signUp.step4.title') }}
            </p>
            <p>
              {{ t('help.registerLogin.signUp.step4.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-4.png" alt="" />
            </div>
            <p class="mb-03 font-600">
              {{ t('help.registerLogin.signUp.tips.title') }}
            </p>
            <p>
              {{ t('help.registerLogin.signUp.tips.description') }}
            </p>
            <p>{{ t('help.registerLogin.signUp.tips.emailRegistered') }}</p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-5.png" alt="" />
            </div>
            <p>{{ t('help.registerLogin.signUp.tips.invalidEmail') }}</p>
            <div class="text-center">
              <img src="@/assets/images/help2-1-6.png" alt="" />
            </div>

            <h5 id="help-4" class="text-primary font-16">
              {{ t('help.registerLogin.login.title') }}
            </h5>
            <p>
              {{ t('help.registerLogin.login.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-2-1.png" alt="" />
            </div>

            <h5 id="help-5" class="text-primary font-16">
              {{ t('help.registerLogin.resetPassword.title') }}
            </h5>
            <p>
              {{ t('help.registerLogin.resetPassword.step1') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-3-1.png" alt="" />
            </div>
            <p>
              {{ t('help.registerLogin.resetPassword.step2') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-3-2.png" alt="" />
            </div>
            <p>
              {{ t('help.registerLogin.resetPassword.step3') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help2-3-3.png" alt="" />
            </div>
            <h5 id="help-6" class="text-primary font-16">
              {{ t('help.registerLogin.modifyInfo.title') }}
            </h5>
            <p>
              {{ t('help.registerLogin.modifyInfo.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help2-4-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help2-4-1.png"
                alt=""
              />
            </div>

            <el-divider> </el-divider>

            <h4 id="help-7" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 3 </el-tag>
              {{ t('help.userCenter.title') }}
            </h4>
            <h5 id="help7-1" class="text-primary font-16">
              {{ t('help.userCenter.overview.title') }}
            </h5>
            <p>
              {{ t('help.userCenter.overview.description1') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-1.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.userCenter.overview.description2') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-2.png"
                alt=""
              />
            </div>
            <h5 id="help7-2" class="text-primary font-16">
              {{ t('help.userCenter.dataList.title') }}
            </h5>
            <p>
              {{ t('help.userCenter.dataList.description1') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-3.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-3.png"
                alt=""
              />
            </div>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-4.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-4.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.userCenter.dataList.description2') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-5.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-5.png"
                alt=""
              />
            </div>
            <h5 id="help7-3" class="text-primary font-16">
              {{ t('help.userCenter.dataStatistics.title') }}
            </h5>
            <p>
              {{ t('help.userCenter.dataStatistics.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-6.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-6.png"
                alt=""
              />
            </div>
            <h5 id="help7-4" class="text-primary font-16">
              {{ t('help.userCenter.dataActivity.title') }}
            </h5>
            <p>
              {{ t('help.userCenter.dataActivity.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help3-7.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help3-7.png"
                alt=""
              />
            </div>
            <el-divider> </el-divider>
            <h4 id="help-26" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 4 </el-tag>
              {{ t('help.detailPage.title') }}
            </h4>
            <h5 id="help26-1" class="text-primary font-16">
              {{ t('help.detailPage.projectDetail.title') }}
            </h5>
            <p>
              {{ t('help.detailPage.projectDetail.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-1.png" alt="" />
            </div>
            <h5 id="help26-2" class="text-primary font-16">
              {{ t('help.detailPage.experimentDetail.title') }}
            </h5>
            <p>
              {{ t('help.detailPage.experimentDetail.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-2.png" alt="" />
            </div>
            <h5 id="help26-3" class="text-primary font-16">
              {{ t('help.detailPage.sampleDetail.title') }}
            </h5>
            <p>
              {{ t('help.detailPage.sampleDetail.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-3.png" alt="" />
            </div>
            <h5 id="help26-4" class="text-primary font-16">
              {{ t('help.detailPage.analysisDetail.title') }}
            </h5>
            <p>
              {{ t('help.detailPage.analysisDetail.description') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help26-4.png" alt="" />
            </div>
            <el-divider> </el-divider>

            <h4 id="help-8" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 5 </el-tag>
              {{ t('help.rawDataUpload.title') }}
            </h4>

            <p>
              {{ t('help.rawDataUpload.description1') }}
            </p>
            <p class="mt-1">
              {{ t('help.rawDataUpload.description2') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help4-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help4-1.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.rawDataUpload.description3') }}
            </p>
            <h5 id="help8-1" class="text-primary font-16">
              {{ t('help.rawDataUpload.httpUpload.title') }}
            </h5>
            <p>
              {{ t('help.rawDataUpload.httpUpload.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help4-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help4-2.png"
                alt=""
              />
            </div>
            <h5 id="help8-2" class="text-primary font-16">
              {{ t('help.rawDataUpload.sftpUpload.title') }}
            </h5>
            <p>
              {{ t('help.rawDataUpload.sftpUpload.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help4-3.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help4-3.png"
                alt=""
              />
            </div>

            <h5 id="help8-2-1" class="text-primary font-16">
              {{ t('help.rawDataUpload.ftpTools.title') }}
            </h5>

            <p>
              {{ t('help.rawDataUpload.ftpTools.description', { fileZilla: `<a
                href="https://filezilla-project.org/download.php?type=client"
                target="_blank"
                class="text-primary"
                >FileZilla</a
              >` }) }}
            </p>
            <p>{{ t('help.rawDataUpload.ftpTools.host') }}</p>
            <p>{{ t('help.rawDataUpload.ftpTools.username') }}</p>
            <p>{{ t('help.rawDataUpload.ftpTools.password') }}</p>
            <p>{{ t('help.rawDataUpload.ftpTools.port') }}</p>
            <div class="text-center">
              <img src="@/assets/images/help4-4.png" alt="" />
            </div>
            <div class="text-center">
              <img src="@/assets/images/help4-5.png" alt="" />
            </div>
            <h5 id="help8-2-2" class="text-primary font-16">
              {{ t('help.rawDataUpload.commandLine.title') }}
            </h5>
            <p class="ml-1">
              {{ t('help.rawDataUpload.commandLine.sftpTitle') }}
            </p>
            <div class="alert-default">
              <p>
                {{ t('help.rawDataUpload.commandLine.sftpCommand') }}
                <a
                  class="text-primary"
                  href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </p>
            </div>
            <p class="pl-20">
              {{ t('help.rawDataUpload.commandLine.password') }}
            </p>
            <div class="alert-default">
              <p>{{ t('help.rawDataUpload.commandLine.navigate') }}</p>
              <p>{{ t('help.rawDataUpload.commandLine.putExample') }}</p>
            </div>
            <p class="ml-1">
              {{ t('help.rawDataUpload.commandLine.lftpTitle') }}
            </p>
            <div class="alert-default">
              <p>{{ t('help.rawDataUpload.commandLine.lftpCommand1') }}</p>
              <p>
                {{ t('help.rawDataUpload.commandLine.lftpCommand2') }}
                <a href="sftp://fms.biosino.org:44397" class="text-primary"
                  >sftp://fms.biosino.org:44397</a
                >
              </p>
            </div>
            <p class="pl-20">
              {{ t('help.rawDataUpload.commandLine.password') }}
            </p>
            <p class="pl-20">
              {{ t('help.rawDataUpload.commandLine.mputDescription') }}
            </p>
            <div class="alert-default">
              <p>{{ t('help.rawDataUpload.commandLine.mputCommand') }}</p>
            </div>
            <p>
              {{ t('help.rawDataUpload.commandLine.uncheckedDescription') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help5-2-2-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help5-2-2-1.png"
                alt=""
              />
            </div>

            <h5 id="help8-2-3" class="text-primary font-16">
              {{ t('help.rawDataUpload.dataIntegrityCheck.title') }}
            </h5>
            <p>
              {{ t('help.rawDataUpload.dataIntegrityCheck.description1') }}
            </p>
            <p>
              {{ t('help.rawDataUpload.dataIntegrityCheck.description2') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help5-2-2-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help5-2-2-2.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.rawDataUpload.dataIntegrityCheck.description3') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help5-2-2-3.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help5-2-2-3.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.rawDataUpload.dataIntegrityCheck.description4') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-4.png" alt="" />
            </div>
            <p>
              {{ t('help.rawDataUpload.dataIntegrityCheck.description5') }}
            </p>
            <div class="text-center">
              <img src="@/assets/images/help5-2-2-5.png" alt="" />
            </div>
            <div class="mb-05 mt-05 font-600">
              {{ t('help.rawDataUpload.dataIntegrityCheck.tips.title') }}
            </div>
            <div>
              <div class="mt-05">
                {{ t('help.rawDataUpload.dataIntegrityCheck.tips.tip1') }}
              </div>
              <div class="mt-05">
                {{ t('help.rawDataUpload.dataIntegrityCheck.tips.tip2') }}
              </div>
            </div>

            <h5 id="help8-3" class="text-primary font-16">
              {{ t('help.rawDataUpload.expressHardDrive.title') }}
            </h5>
            <p>
              {{ t('help.rawDataUpload.expressHardDrive.description') }}
            </p>
            <p>
              {{ t('help.rawDataUpload.expressHardDrive.email') }}
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
            </p>
            <p>
              {{ t('help.rawDataUpload.expressHardDrive.address') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help4-6.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help4-6.png"
                alt=""
              />
            </div>
            <el-divider> </el-divider>

            <h4 id="help-9" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 6 </el-tag>
              {{ t('help.metadataArchiving.title') }}
            </h4>
            <p>
              {{ t('help.metadataArchiving.description1') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help5-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help5-1.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.metadataArchiving.description2') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help5-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help5-2.png"
                alt=""
              />
            </div>
            <h5 id="help-10" class="text-primary font-16">
              {{ t('help.metadataArchiving.rawDataArchiving.title') }}
            </h5>
            <p id="help-11" class="text-primary mb-05">
              {{
                t('help.metadataArchiving.rawDataArchiving.newSubmission.title')
              }}
            </p>
            <p>
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.description1',
                )
              }}
            </p>
            <p>
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.description2',
                )
              }}
            </p>
            <div class="font-600 mt-05">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.submitter.title',
                )
              }}
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submitter.firstName',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submitter.lastName',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submitter.organization',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submitter.email',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submitter.countryRegion',
                  )
                }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-3.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-3.png"
                  alt=""
                />
              </div>
            </div>

            <div class="font-600">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.project.title',
                )
              }}
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.project.name',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.project.description',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.project.relatedLinks',
                  )
                }}
              </div>

              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-4.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-4.png"
                  alt=""
                />
              </div>
            </div>

            <div class="font-600">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.title',
                )
              }}
              <p>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.name',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.description',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.platform',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.plannedReadLength',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.matePair',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.libraryName',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.libraryStrategy',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.librarySource',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.librarySelection',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.libraryLayout',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.libraryConstruction',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.experiment.relatedLinks',
                  )
                }}
              </p>

              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-5.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-5.png"
                  alt=""
                />
              </div>
            </div>
            <div class="font-600">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.sample.title',
                )
              }}
              <p>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.sample.name',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.sample.organism',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.sample.description',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.sample.attributes',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.sample.provider',
                  )
                }}
              </p>
              <p class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.sample.relatedLinks',
                  )
                }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-6.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-6.png"
                  alt=""
                />
              </div>
            </div>
            <div class="font-600">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.run.title',
                )
              }}
              <p>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.run.name',
                  )
                }}
              </p>
              <div>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.run.description',
                  )
                }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-7.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-7.png"
                  alt=""
                />
              </div>
            </div>

            <div class="font-600">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.archives.title',
                )
              }}
              <div>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.archives.description',
                  )
                }}
              </div>
              <div>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.archives.runDescription',
                  )
                }}
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-8.png" alt="" />
              </div>
            </div>

            <div class="font-600">
              {{
                t(
                  'help.metadataArchiving.rawDataArchiving.newSubmission.submission.title',
                )
              }}
              <div>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submission.description',
                  )
                }}
              </div>
              <div>
                {{
                  t(
                    'help.metadataArchiving.rawDataArchiving.newSubmission.submission.runDescription',
                  )
                }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-9.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-9.png"
                  alt=""
                />
              </div>
            </div>
            <p id="help-12" class="text-primary mb-05">
              {{ t('help.metadataArchiving.reArchive.title') }}
            </p>
            <p>
              {{ t('help.metadataArchiving.reArchive.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-1-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-1-2.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.metadataArchiving.reArchive.experimentBatch') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-1-3.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-1-3.png"
                alt=""
              />
            </div>
            <p>
              {{ t('help.metadataArchiving.reArchive.sampleBatch') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-1-4.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-1-4.png"
                alt=""
              />
            </div>
            <ul class="re-archive">
              <li>
                {{
                  t('help.metadataArchiving.reArchive.scenarios.modifyRunName')
                }}
              </li>
              <li>
                {{ t('help.metadataArchiving.reArchive.scenarios.addNewData') }}
              </li>
              <li>
                {{
                  t(
                    'help.metadataArchiving.reArchive.scenarios.modifyDataNewRun',
                  )
                }}
              </li>
              <li>
                {{
                  t(
                    'help.metadataArchiving.reArchive.scenarios.addNewExperiment',
                  )
                }}
              </li>
            </ul>
            <p>
              {{ t('help.metadataArchiving.reArchive.finalStep') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-1-5.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-1-5.png"
                alt=""
              />
            </div>
            <h5 id="help-13" class="text-primary font-16 mt-2">
              {{ t('help.metadataArchiving.analysisDataArchiving.title') }}
            </h5>
            <p id="help-14" class="text-primary">
              {{
                t(
                  'help.metadataArchiving.analysisDataArchiving.newSubmission.title',
                )
              }}
            </p>

            <div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.enterInterface',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.guidance',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.analysisName',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.analysisDescription',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.program',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.link',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.version',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.output',
                  )
                }}
              </div>
              <div class="mt-05">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.target',
                  )
                }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-10.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-10.png"
                  alt=""
                />
              </div>
              <div class="font-600">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.archives.title',
                  )
                }}
              </div>
              <div>
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.archives.description',
                  )
                }}
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-11.png" alt="" />
              </div>

              <div class="font-600">
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.submission.title',
                  )
                }}
              </div>
              <div>
                {{
                  t(
                    'help.metadataArchiving.analysisDataArchiving.newSubmission.submission.description',
                  )
                }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help5-12.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help5-12.png"
                  alt=""
                />
              </div>
              <div class="text-center">
                <img src="@/assets/images/help5-13.png" alt="" />
              </div>
            </div>
            <p id="help-5-2-2" class="text-primary mb-05">
              {{
                t(
                  'help.metadataArchiving.analysisDataArchiving.reArchive.title',
                )
              }}
            </p>
            <p>
              {{
                t(
                  'help.metadataArchiving.analysisDataArchiving.reArchive.description',
                )
              }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-2-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-2-1.png"
                alt=""
              />
            </div>
            <p>
              {{
                t(
                  'help.metadataArchiving.analysisDataArchiving.reArchive.batchModify',
                )
              }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-2-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-2-2.png"
                alt=""
              />
            </div>
            <p>
              {{
                t(
                  'help.metadataArchiving.analysisDataArchiving.reArchive.addNewData',
                )
              }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help6-2-3.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help6-2-3.png"
                alt=""
              />
            </div>
            <el-divider> </el-divider>

            <h4 id="help-15" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 7 </el-tag>
              {{ t('help.dataManagement.title') }}
            </h4>
            <h5 id="help15-1" class="text-primary font-16">
              {{ t('help.dataManagement.rawDataManagement.title') }}
            </h5>
            <div>
              <div>
                {{ t('help.dataManagement.rawDataManagement.description') }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help15-1.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help15-1.png"
                  alt=""
                />
              </div>
            </div>
            <h5 id="help15-2" class="text-primary font-16">
              {{ t('help.dataManagement.metaDataManagement.title') }}
            </h5>
            <p>
              {{ t('help.dataManagement.metaDataManagement.description') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help15-2.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help15-2.png"
                alt=""
              />
            </div>

            <el-divider> </el-divider>

            <h4 id="help-16" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 8 </el-tag>
              {{ t('help.dataSecurity.title') }}
            </h4>
            <h5 id="help16-1" class="text-primary font-16">
              {{ t('help.dataSecurity.overview.title') }}
            </h5>
            <div>
              <p>
                {{ t('help.dataSecurity.overview.description1') }}
              </p>
              <p class="mt-05">
                {{ t('help.dataSecurity.overview.description2') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help7-1.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help7-1.png"
                  alt=""
                />
              </div>
              <div>{{ t('help.dataSecurity.overview.imageCaption') }}</div>
              <p>
                {{ t('help.dataSecurity.overview.description3') }}
              </p>
              <div>
                {{ t('help.dataSecurity.overview.note') }}
              </div>
              <h5 id="help16-2" class="text-primary font-16">
                {{ t('help.dataSecurity.privateToRestricted.title') }}
              </h5>
              <p>
                {{ t('help.dataSecurity.privateToRestricted.description1') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help7-2.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help7-2.png"
                  alt=""
                />
              </div>
              <p>
                {{ t('help.dataSecurity.privateToRestricted.description2') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help7-3.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help7-3.png"
                  alt=""
                />
              </div>
              <p>
                {{ t('help.dataSecurity.privateToRestricted.description3') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help7-4.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help7-4.png"
                  alt=""
                />
              </div>
              <h5 id="help16-3" class="text-primary font-16">
                {{ t('help.dataSecurity.privateToPublic.title') }}
              </h5>
              <p>
                {{ t('help.dataSecurity.privateToPublic.description') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help7-5.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help7-5.png"
                  alt=""
                />
              </div>
              <h5 id="help16-4" class="text-primary font-16">
                {{ t('help.dataSecurity.restrictedToPublic.title') }}
              </h5>
              <p>
                {{ t('help.dataSecurity.restrictedToPublic.description1') }}
              </p>
              <p>
                {{ t('help.dataSecurity.restrictedToPublic.description2') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help7-6.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help7-6.png"
                  alt=""
                />
              </div>
              <div class="mb-05 mt-05 font-600">
                {{ t('help.dataSecurity.tips.title') }}
              </div>
              <div>
                {{ t('help.dataSecurity.tips.description') }}
                <a class="text-primary" href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
                <div class="mt-05">
                  {{ t('help.dataSecurity.tips.restriction1') }}
                </div>
                <div class="mt-05">
                  {{ t('help.dataSecurity.tips.restriction2') }}
                </div>
                <div class="mt-05">
                  {{ t('help.dataSecurity.tips.restriction3') }}
                </div>
              </div>
            </div>
            <el-divider> </el-divider>

            <h4 id="help-17" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 9 </el-tag>
              {{ t('help.nodeSearch.title') }}
            </h4>
            <h5 id="help-18" class="text-primary font-16">
              {{ t('help.nodeSearch.fullTextSearch.title') }}
            </h5>
            <div>
              <p>
                {{ t('help.nodeSearch.fullTextSearch.description') }}
              </p>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help8-1.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help8-1.png"
                  alt=""
                />
              </div>
            </div>
            <h5 id="help-19" class="text-primary font-16">
              {{ t('help.nodeSearch.advancedSearch.title') }}
            </h5>
            <div>
              {{ t('help.nodeSearch.advancedSearch.description') }}
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help8-2.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help8-2.png"
                  alt=""
                />
              </div>
            </div>
            <el-divider> </el-divider>

            <h4 id="help-20" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 10 </el-tag>
              {{ t('help.shareReviewRequest.title') }}
            </h4>
            <h5 id="help20-1" class="text-primary font-16">
              {{ t('help.shareReviewRequest.dataShare.title') }}
            </h5>
            <div>
              <div style="text-align: justify">
                {{ t('help.shareReviewRequest.dataShare.description1') }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-1.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-1.png"
                    alt=""
                  />
                </div>
                {{ t('help.shareReviewRequest.dataShare.description2') }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-2.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-2.png"
                    alt=""
                  />
                </div>
                {{ t('help.shareReviewRequest.dataShare.description3') }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-3.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-3.png"
                    alt=""
                  />
                </div>
                {{ t('help.shareReviewRequest.dataShare.description4') }}
                <div class="text-center">
                  <img src="@/assets/images/help9-4.png" alt="" />
                </div>
                {{ t('help.shareReviewRequest.dataShare.description5') }}
                <div class="text-center">
                  <img src="@/assets/images/help9-5.png" alt="" />
                </div>
              </div>
              <h5 id="help20-2" class="text-primary font-16">
                {{ t('help.shareReviewRequest.dataReview.title') }}
              </h5>
              <div style="text-align: justify">
                {{ t('help.shareReviewRequest.dataReview.description1') }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-6.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-6.png"
                    alt=""
                  />
                </div>
                {{ t('help.shareReviewRequest.dataReview.description2') }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-7.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-7.png"
                    alt=""
                  />
                </div>
                {{ t('help.shareReviewRequest.dataReview.description3') }}
                <div class="text-center">
                  <img src="@/assets/images/help9-8.png" alt="" />
                </div>
                {{ t('help.shareReviewRequest.dataReview.description4') }}
                <div class="text-center">
                  <img src="@/assets/images/help9-9.png" alt="" />
                </div>
              </div>
              <h5 id="help20-3" class="text-primary font-16">
                {{ t('help.shareReviewRequest.requestRestrictedData.title') }}
              </h5>
              <div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.description1',
                  )
                }}
                <div>
                  {{
                    t('help.shareReviewRequest.requestRestrictedData.example')
                  }}
                </div>
                <div>
                  {{
                    t(
                      'help.shareReviewRequest.requestRestrictedData.userADescription',
                    )
                  }}
                </div>
                <div>
                  {{
                    t(
                      'help.shareReviewRequest.requestRestrictedData.userBDescription1',
                    )
                  }}
                </div>
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-10.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-10.png"
                    alt=""
                  />
                </div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.userBDescription2',
                  )
                }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-11.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-11.png"
                    alt=""
                  />
                </div>
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-12.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-12.png"
                    alt=""
                  />
                </div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.userBDescription3',
                  )
                }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-13.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-13.png"
                    alt=""
                  />
                </div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.emailNotification',
                  )
                }}
                <div class="text-center">
                  <img src="@/assets/images/help9-14.png" alt="" />
                </div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.checkRequests',
                  )
                }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-15.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-15.png"
                    alt=""
                  />
                </div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.permitDecline',
                  )
                }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-16.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-16.png"
                    alt=""
                  />
                </div>
                {{
                  t(
                    'help.shareReviewRequest.requestRestrictedData.authorization',
                  )
                }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-17.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-17.png"
                    alt=""
                  />
                </div>
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help9-18.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help9-18.png"
                    alt=""
                  />
                </div>
              </div>
            </div>
            <el-divider> </el-divider>

            <h4 id="help-21" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 11 </el-tag>
              {{ t('help.dataDownload.title') }}
            </h4>
            <h5 id="help-22" class="text-primary font-16">
              {{ t('help.dataDownload.httpDownload.title') }}
            </h5>
            <div>
              <span class="text-main-color">{{
                t('help.dataDownload.httpDownload.bulkDownload.title')
              }}</span>

              <div>
                {{
                  t('help.dataDownload.httpDownload.bulkDownload.description')
                }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help10-1.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help10-1.png"
                  alt=""
                />
              </div>
              {{ t('help.dataDownload.httpDownload.bulkDownload.example') }}
              <div class="text-center">
                <img src="@/assets/images/help10-2.png" alt="" />
              </div>
              <span class="text-main-color">{{
                t('help.dataDownload.httpDownload.specificDownload.title')
              }}</span>
              <div>
                {{
                  t(
                    'help.dataDownload.httpDownload.specificDownload.description',
                  )
                }}
                <div class="text-center">
                  <img
                    v-if="lang === 'en'"
                    src="@/assets/images/help10-3.png"
                    alt=""
                  />
                  <img
                    v-if="lang === 'zh'"
                    src="@/assets/images/help-zh/help10-3.png"
                    alt=""
                  />
                </div>
              </div>
            </div>
            <h5 id="help-23" class="text-primary font-16">
              {{ t('help.dataDownload.ftpDownload.title') }}
            </h5>
            <div>
              {{ t('help.dataDownload.ftpDownload.tips') }}
              <div>
                {{ t('help.dataDownload.ftpDownload.directoryStructure') }}
              </div>
              <div class="mt-05">
                {{ t('help.dataDownload.ftpDownload.byRun') }}
              </div>
              <div class="mt-05">
                {{ t('help.dataDownload.ftpDownload.byAnalysis') }}
              </div>
              <div class="mt-05">
                {{ t('help.dataDownload.ftpDownload.runListQuestion') }}
              </div>
              <div class="mt-05">
                {{ t('help.dataDownload.ftpDownload.runListAnswer') }}
              </div>
              <div class="text-center">
                <img
                  v-if="lang === 'en'"
                  src="@/assets/images/help10-5.png"
                  alt=""
                />
                <img
                  v-if="lang === 'zh'"
                  src="@/assets/images/help-zh/help10-5.png"
                  alt=""
                />
              </div>
              <span class="text-main-color">{{
                t('help.dataDownload.ftpDownload.windows.title')
              }}</span>
              <div>
                {{ t('help.dataDownload.ftpDownload.windows.description') }}
              </div>
              <div>{{ t('help.dataDownload.ftpDownload.windows.host') }}</div>
              <div>
                {{ t('help.dataDownload.ftpDownload.windows.username') }}
              </div>
              <div>
                {{ t('help.dataDownload.ftpDownload.windows.password') }}
              </div>
              <div>{{ t('help.dataDownload.ftpDownload.windows.port') }}</div>
              {{ t('help.dataDownload.ftpDownload.windows.quickConnect') }}
              <div class="text-center">
                <img src="@/assets/images/help10-4.png" alt="" />
              </div>
              <span class="text-main-color">{{
                t('help.dataDownload.ftpDownload.linux.title')
              }}</span>
              <div class="ml-1">
                {{ t('help.dataDownload.ftpDownload.linux.sftp.title') }}
              </div>
              <div class="alert-default">
                sftp -oPort=44398
                <a
                  class="text-primary"
                  href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </div>
              <div class="mt-05 ml-1">
                {{ t('help.dataDownload.ftpDownload.linux.sftp.password') }}
              </div>
              <div class="mt-05 ml-1">
                {{ t('help.dataDownload.ftpDownload.linux.sftp.navigate') }}
              </div>
              <div class="alert-default mt-05">
                {{ t('help.dataDownload.ftpDownload.linux.sftp.command') }}
              </div>
              <div class="mt-05 ml-1">
                {{ t('help.dataDownload.ftpDownload.linux.lftp.title') }}
              </div>
              <div class="mt-05 alert-default">
                lftp :~> connect sftp://fms.biosino.org:44398
              </div>
              <div class="mt-05 alert-default">
                lftp fms.biosino.org:~> your-node-register-email
              </div>
              <div class="mt-05 ml-1">
                {{ t('help.dataDownload.ftpDownload.linux.lftp.password') }}
              </div>
              <div class="mt-05 ml-1">
                {{ t('help.dataDownload.ftpDownload.linux.lftp.description') }}
              </div>
              <div class="mt-05 alert-default">
                {{ t('help.dataDownload.ftpDownload.linux.lftp.command1') }}
              </div>
              <div class="mt-05 alert-default">
                {{ t('help.dataDownload.ftpDownload.linux.lftp.command2') }}
              </div>
            </div>
            <el-divider> </el-divider>
            <h4 id="help-24" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 12 </el-tag>
              {{ t('help.humanGeneticResources.title') }}
            </h4>
            <div style="text-align: justify">
              {{ t('help.humanGeneticResources.description') }}
              <a
                href="http://www.gov.cn/zhengce/content/2019-06/10/content_5398829.html"
                target="_blank"
                class="text-primary"
                >http://www.gov.cn/zhengce/content/2019-06/10/content_5398829.html</a
              >
              {{ t('help.humanGeneticResources.and') }}
              <a
                href="https://www.most.gov.cn/xxgk/xinxifenlei/fdzdgknr/fgzc/bmgz/202306/t20230601_186416.html"
                target="_blank"
                class="text-primary"
                >https://www.most.gov.cn/xxgk/xinxifenlei/fdzdgknr/fgzc/bmgz/202306/t20230601_186416.html</a
              >
            </div>

            <el-divider> </el-divider>
            <h4 id="help-25" class="text-main-color font-16 mb-1">
              <el-tag effect="dark" class="radius-12"> 13 </el-tag>
              {{ t('help.faq.title') }}
            </h4>
            <h5 id="help25-1" class="text-primary font-16">
              {{ t('help.faq.activationFailed.title') }}
            </h5>
            <p>
              {{ t('help.faq.activationFailed.description') }}
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
              {{ t('help.faq.activationFailed.additionalInfo') }}
            </p>
            <h5 id="help25-2" class="text-primary font-16">
              {{ t('help.faq.usernamePasswordError.title') }}
            </h5>
            <p>
              {{ t('help.faq.usernamePasswordError.description') }}
            </p>
            <h5 id="help25-3" class="text-primary font-16">
              {{ t('help.faq.setDataSecurity.title') }}
            </h5>
            <p>
              {{ t('help.faq.setDataSecurity.description') }}
              <a href="#help-15" class="text-primary">1.2.7</a>
              {{ t('help.faq.setDataSecurity.linkText') }}
            </p>
            <h5 id="help25-4" class="text-primary font-16">
              {{ t('help.faq.citeData.title') }}
            </h5>
            <p>
              {{ t('help.faq.citeData.description') }}
            </p>
            <p>
              {{ t('help.faq.citeData.accessText') }}
              <a
                href="https://www.biosino.org/node/"
                target="_blank"
                class="text-primary"
                >https://www.biosino.org/node/</a
              >
              {{ t('help.faq.citeData.accessionText') }}
              <a
                href="https://www.biosino.org/node/project/detail/XXX"
                target="_blank"
                class="text-primary"
                >https://www.biosino.org/node/project/detail/XXX</a
              >
            </p>
            <p class="mt-1">{{ t('help.faq.citeData.citationPrompt') }}</p>
            <p>
              {{ t('help.faq.citeData.publicationText') }}
              <a
                href="https://lifescience.sinh.ac.cn/article.php?id=3716"
                target="_blank"
                class="text-primary"
                >10.13376/j.cbls/2023169</a
              >
            </p>
            <h5 id="help25-5" class="text-primary font-16">
              {{ t('help.faq.useTemplate.title') }}
            </h5>
            <p>
              {{ t('help.faq.useTemplate.description1') }}
            </p>
            <p>
              {{ t('help.faq.useTemplate.description2') }}
            </p>
            <div class="text-center">
              <img
                v-if="lang === 'en'"
                src="@/assets/images/help12-1.png"
                alt=""
              />
              <img
                v-if="lang === 'zh'"
                src="@/assets/images/help-zh/help12-1.png"
                alt=""
              />
            </div>
            <h5 id="help25-6" class="text-primary font-16">
              {{ t('help.faq.integrityVerification.title') }}
            </h5>
            <p>
              {{ t('help.faq.integrityVerification.description') }}
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
            </p>
            <div class="text-center">
              <img src="@/assets/images/help12-2.png" alt="" />
            </div>
            <h5 id="help25-7" class="text-primary font-16">
              {{ t('help.faq.submissionReview.title') }}
            </h5>
            <p>
              {{ t('help.faq.submissionReview.description') }}
              <a href="mailto:<EMAIL>" class="text-primary"
                ><EMAIL></a
              >
            </p>
            <h5 id="help25-8" class="text-primary font-16">
              {{ t('help.faq.splitMd5.title') }}
            </h5>
            <p>
              {{ t('help.faq.splitMd5.description1') }}
            </p>
            <div class="alert-default mt-05">
              <pre>
87409b3f8fe799930e9fda080535c756	A.R1.fq.gz
8d82dd4365b6fdf740327c04bf08b754	A.R2.fq.gz
74242c579e8342da4fbf54f09864fb83	B.R1.fq.gz
1c42cf18fbd58be4a5edacf6419e1c78	B.R2.fq.gz</pre
              >
            </div>
            <p>{{ t('help.faq.splitMd5.description2') }} “md5_split.sh”:</p>
            <div class="alert-default mt-05">
              <pre>
while read line
do
    filename=`echo $line |cut -d' ' -f2`
    echo $line > ${filename}.md5
done < $1</pre
              >
            </div>
            <p>{{ t('help.faq.splitMd5.description3') }}</p>
            <div class="alert-default mt-05">
              <pre>
sh [split script file name] [md5 documents name from sequencing companies]
sh md5_split.sh md5_result-1.txt </pre
              >
            </div>

            <h5 id="help25-9" class="text-primary font-16">
              {{ t('help.faq.dataIntegrityCheck.title') }}
            </h5>
            <p>
              {{ t('help.faq.dataIntegrityCheck.description') }}
              <a
                href="javascript:void(0)"
                class="text-primary"
                @click="handleClick('help8-2-3')"
                >{{ t('help.faq.dataIntegrityCheck.linkText') }}</a
              >
            </p>
            <h5 id="help25-10" class="text-primary font-16">
              {{ t('help.faq.transferData.title') }}
            </h5>
            <p>
              {{ t('help.faq.transferData.description') }}
            </p>
            <ul>
              <li>
                <p>
                  {{ t('help.faq.transferData.step1') }}
                </p>
              </li>
              <li>
                <p>
                  {{ t('help.faq.transferData.step2') }}
                  <a href="mailto:<EMAIL>" class="text-primary"
                    ><EMAIL></a
                  >{{ t('help.faq.transferData.step2Additional') }}
                </p>
              </li>
              <li>
                <p>
                  {{ t('help.faq.transferData.step3') }}
                </p>
              </li>
              <li>
                <p>
                  {{ t('help.faq.transferData.step4') }}
                </p>
              </li>
            </ul>
            <p>
              {{ t('help.faq.transferData.conclusion') }}
            </p>
            <h5 id="help25-11" class="text-primary font-16">
              {{ t('help.faq.groupSubmissions.title') }}
            </h5>
            <p>
              {{ t('help.faq.groupSubmissions.description') }}
            </p>
            <p>
              {{ t('help.faq.groupSubmissions.recommendation') }}
            </p>
            <ul class="spot-list">
              <li>
                {{
                  t('help.faq.groupSubmissions.strategy1', {
                    email: '<EMAIL>',
                  })
                }}
                <a
                  href="javascript:void(0)"
                  class="text-primary"
                  @click="handleClick('help25-10')"
                  >13.10</a
                >
                {{ t('help.faq.groupSubmissions.strategy1Additional') }}
              </li>
              <li>
                {{ t('help.faq.groupSubmissions.strategy2') }}
                <a
                  href="javascript:void(0)"
                  class="text-primary"
                  @click="handleClick('help-11')"
                  >6.1.1</a
                >
                {{ t('help.faq.groupSubmissions.strategy2Additional') }}
              </li>
              <li>
                {{ t('help.faq.groupSubmissions.strategy3') }}
              </li>
            </ul>
            <p>
              {{ t('help.faq.groupSubmissions.conclusion') }}
            </p>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import { computed, reactive, ref } from 'vue';
  import { defaultProps } from 'element-plus/es/components/select-v2/src/useProps';
  import { useI18n } from 'vue-i18n';

  const { t, locale } = useI18n();

  // 当前语言显示
  const lang = computed(() => {
    return locale.value;
  });

  const activeId = ref('help-1');
  const toc = reactive([
    {
      id: 'help-1',
      title: t('help.toc.overview.title'),
      layer: 'first',
      sort: '1.',
      children: [
        {
          id: 'help-1-1',
          title: t('help.toc.overview.experimentType'),
          layer: 'second',
          sort: '1.1',
        },
        {
          id: 'help-1-2',
          title: t('help.toc.overview.sampleType'),
          layer: 'second',
          sort: '1.2',
        },
      ],
    },
    {
      id: 'help-2',
      title: t('help.toc.registerLogin.title'),
      layer: 'first',
      sort: '2.',
      children: [
        {
          id: 'help-3',
          title: t('help.toc.registerLogin.signUp'),
          layer: 'second',
          sort: '2.1',
        },
        {
          id: 'help-4',
          title: t('help.toc.registerLogin.login'),
          layer: 'second',
          sort: '2.2',
        },
        {
          id: 'help-5',
          title: t('help.toc.registerLogin.resetPassword'),
          layer: 'second',
          sort: '2.3',
        },
        {
          id: 'help-6',
          title: t('help.toc.registerLogin.modifyInfo'),
          layer: 'second',
          sort: '2.4',
        },
      ],
    },

    {
      id: 'help-7',
      title: t('help.toc.userCenter.title'),
      layer: 'first',
      subtitle: t('help.toc.updated'),
      sort: '3.',
      children: [
        {
          id: 'help7-1',
          title: t('help.toc.userCenter.overview'),
          layer: 'second',
          sort: '3.1',
        },
        {
          id: 'help7-2',
          title: t('help.toc.userCenter.dataList'),
          layer: 'second',
          sort: '3.2',
        },
        {
          id: 'help7-3',
          title: t('help.toc.userCenter.dataStatistics'),
          layer: 'second',
          sort: '3.3',
        },
        {
          id: 'help7-4',
          title: t('help.toc.userCenter.dataActivity'),
          layer: 'second',
          sort: '3.4',
        },
      ],
    },
    {
      id: 'help-26',
      title: t('help.toc.detailPage.title'),
      layer: 'first',
      subtitle: t('help.toc.updated'),
      sort: '4.',
      children: [
        {
          id: 'help26-1',
          title: t('help.toc.detailPage.project'),
          layer: 'second',
          sort: '4.1',
        },
        {
          id: 'help26-2',
          title: t('help.toc.detailPage.experiment'),
          layer: 'second',
          sort: '4.2',
        },
        {
          id: 'help26-3',
          title: t('help.toc.detailPage.sample'),
          layer: 'second',
          sort: '4.3',
        },
        {
          id: 'help26-4',
          title: t('help.toc.detailPage.analysis'),
          layer: 'second',
          sort: '4.4',
        },
      ],
    },
    {
      id: 'help-8',
      title: t('help.toc.rawDataUpload.title'),
      layer: 'first',
      subtitle: t('help.toc.updated'),
      sort: '5.',
      children: [
        {
          id: 'help8-1',
          title: t('help.toc.rawDataUpload.httpUpload'),
          layer: 'second',
          sort: '5.1',
        },
        {
          id: 'help8-2',
          title: t('help.toc.rawDataUpload.sftpUpload'),
          layer: 'second',
          sort: '5.2',
          children: [
            {
              id: 'help8-2-1',
              title: t('help.toc.rawDataUpload.ftpTools'),
              layer: 'third',
              sort: '5.2.1',
            },
            {
              id: 'help8-2-2',
              title: t('help.toc.rawDataUpload.commandLine'),
              layer: 'third',
              sort: '5.2.2',
            },
            {
              id: 'help8-2-3',
              title: t('help.toc.rawDataUpload.dataIntegrityCheck'),
              layer: 'third',
              sort: '5.2.3',
            },
          ],
        },
        {
          id: 'help8-3',
          title: t('help.toc.rawDataUpload.expressHardDrive'),
          layer: 'second',
          sort: '5.3',
        },
      ],
    },
    {
      id: 'help-9',
      title: t('help.toc.metadataArchiving.title'),
      layer: 'first',
      subtitle: t('help.toc.updated'),
      sort: '6.',
      children: [
        {
          id: 'help-10',
          title: t('help.toc.metadataArchiving.rawDataArchiving'),
          layer: 'second',
          sort: '6.1',
          children: [
            {
              id: 'help-11',
              title: t('help.toc.metadataArchiving.newSubmission'),
              layer: 'third',
              sort: '6.1.1',
            },
            {
              id: 'help-12',
              title: t('help.toc.metadataArchiving.reArchive'),
              layer: 'third',
              sort: '6.1.2',
            },
          ],
        },
        {
          id: 'help-13',
          title: t('help.toc.metadataArchiving.analysisDataArchiving'),
          layer: 'second',
          sort: '6.2',
          children: [
            {
              id: 'help-14',
              title: t('help.toc.metadataArchiving.newSubmission'),
              layer: 'third',
              sort: '6.2.1',
            },
            {
              id: 'help-5-2-2',
              title: t('help.toc.metadataArchiving.reArchive'),
              layer: 'third',
              sort: '6.2.2',
            },
          ],
        },
      ],
    },

    {
      id: 'help-15',
      title: t('help.toc.dataManagement.title'),
      layer: 'first',
      sort: '7.',
      children: [
        {
          id: 'help15-1',
          title: t('help.toc.dataManagement.rawData'),
          layer: 'second',
          sort: '7.1',
        },
        {
          id: 'help15-2',
          title: t('help.toc.dataManagement.metaData'),
          layer: 'second',
          sort: '7.2',
        },
      ],
    },
    {
      id: 'help-16',
      title: t('help.toc.dataSecurity.title'),
      layer: 'first',
      sort: '8.',
      children: [
        {
          id: 'help16-1',
          title: t('help.toc.dataSecurity.overview'),
          layer: 'second',
          sort: '8.1',
        },
        {
          id: 'help16-2',
          title: t('help.toc.dataSecurity.privateToRestricted'),
          layer: 'second',
          sort: '8.2',
        },
        {
          id: 'help16-3',
          title: t('help.toc.dataSecurity.privateToPublic'),
          layer: 'second',
          sort: '8.3',
        },
        {
          id: 'help16-4',
          title: t('help.toc.dataSecurity.restrictedToPublic'),
          layer: 'second',
          sort: '8.4',
        },
      ],
    },
    {
      id: 'help-17',
      title: t('help.toc.nodeSearch.title'),
      layer: 'first',
      subtitle: t('help.toc.updated'),
      sort: '9.',
      children: [
        {
          id: 'help-18',
          title: t('help.toc.nodeSearch.fullTextSearch'),
          layer: 'second',
          sort: '9.1',
        },
        {
          id: 'help-19',
          title: t('help.toc.nodeSearch.advancedSearch'),
          layer: 'second',
          sort: '9.2',
        },
      ],
    },

    {
      id: 'help-20',
      title: t('help.toc.shareReviewRequest.title'),
      layer: 'first',
      sort: '10.',
      children: [
        {
          id: 'help20-1',
          title: t('help.toc.shareReviewRequest.dataShare'),
          layer: 'second',
          sort: '10.1',
        },
        {
          id: 'help20-2',
          title: t('help.toc.shareReviewRequest.dataReview'),
          layer: 'second',
          sort: '10.2',
        },
        {
          id: 'help20-3',
          title: t('help.toc.shareReviewRequest.requestRestrictedData'),
          layer: 'second',
          sort: '10.3',
        },
      ],
    },
    {
      id: 'help-21',
      title: t('help.toc.dataDownload.title'),
      layer: 'first',
      sort: '11.',
      children: [
        {
          id: 'help-22',
          title: t('help.toc.dataDownload.httpDownload'),
          layer: 'second',
          sort: '11.1',
        },
        {
          id: 'help-23',
          title: t('help.toc.dataDownload.ftpDownload'),
          layer: 'second',
          sort: '11.2',
        },
      ],
    },

    {
      id: 'help-24',
      title: t('help.toc.humanGeneticResources'),
      layer: 'first',
      sort: '12.',
    },
    {
      id: 'help-25',
      title: t('help.toc.faq.title'),
      layer: 'first',
      subtitle: t('help.toc.updated'),
      sort: '13.',
      children: [
        {
          id: 'help25-1',
          title: t('help.toc.faq.activationFailed'),
          layer: 'second',
          sort: '13.1',
        },
        {
          id: 'help25-2',
          title: t('help.toc.faq.usernamePasswordError'),
          layer: 'second',
          sort: '13.2',
        },
        {
          id: 'help25-3',
          title: t('help.toc.faq.setDataSecurity'),
          layer: 'second',
          sort: '13.3',
        },
        {
          id: 'help25-4',
          title: t('help.toc.faq.citeData'),
          layer: 'second',
          sort: '13.4',
        },
        {
          id: 'help25-5',
          title: t('help.toc.faq.useTemplate'),
          layer: 'second',
          sort: '13.5',
        },
        {
          id: 'help25-6',
          title: t('help.toc.faq.integrityVerification'),
          layer: 'second',
          sort: '13.6',
        },
        {
          id: 'help25-7',
          title: t('help.toc.faq.submissionReview'),
          layer: 'second',
          sort: '13.7',
        },
        {
          id: 'help25-8',
          title: t('help.toc.faq.splitMd5'),
          layer: 'second',
          sort: '13.8',
        },
        {
          id: 'help25-9',
          title: t('help.toc.faq.dataIntegrityCheck'),
          layer: 'second',
          sort: '13.9',
        },
        {
          id: 'help25-10',
          title: t('help.toc.faq.transferData'),
          layer: 'second',
          sort: '13.10',
        },
        {
          id: 'help25-11',
          title: t('help.toc.faq.groupSubmissions'),
          layer: 'second',
          sort: '13.11',
        },
      ],
    },
  ]);

  const handleClick = id => {
    const section = document.getElementById(id);
    // section.scrollIntoView({ behavior: 'smooth' });
    section.scrollIntoView({
      top: 100,
      behavior: 'smooth',
      block: 'start',
      inline: 'start',
    });
    // Highlight the corresponding directory title
    activeId.value = id;
  };
</script>

<style lang="scss" scoped>
  p {
    text-align: justify;
  }
  .re-archive {
    padding-left: 20px;
  }
  .re-archive li {
    list-style-type: disc; /* 实心圆点 */
    &::marker {
      color: #3a78e8;
    }
  }

  .Updated {
    color: #3a78e8 !important;
  }
  .active {
    a {
      color: #3a78e8 !important;
    }
  }

  .analyse-page {
    padding: 20px 0 25px 0;
    font-size: 16px;
    .container {
      margin: 0 auto;
    }
    .detail-box {
      background: #fff;
      border-radius: 4px;
      position: relative;
      box-shadow: 0 1px 20px 0 rgba(118, 109, 224, 0.1);
      padding: 16px;
    }
    .el-tag {
      border-radius: 8px;
      margin-right: 6px;
    }
    .el-divider {
      margin: 0.5rem 0 1rem !important;
    }
    h5 {
      margin-bottom: 0.5rem;
    }
    .mb-03 {
      margin-bottom: 0.3rem;
    }
    h5 {
      font-weight: 600;
    }
    img {
      text-align: center;
      max-width: 80%;
      margin: 10px 0;
    }
  }
  .layer-first {
    a {
      color: #333333 !important;
    }
    &.active a {
      color: #3a78e8 !important;
    }
  }
  .layer-second {
    color: #777777 !important;
  }
  .layer-third {
    color: #888888 !important;
  }
  li {
    a {
      font-weight: 600;
      font-size: 16px;
    }
    padding: 6px 0;
    line-height: 18px;
    &:hover a {
      color: #3a78e8 !important;
    }
  }

  .el-tree {
    max-height: 700px;
    overflow-y: scroll;
  }
  :deep(.el-tree-node__content) {
    margin-bottom: 6px;
  }

  .alert-default {
    padding: 5px 15px;
    margin: 0 15px;
    color: #666;
    background-color: #f0f0f0;
    border-color: #e5e5e5;
  }

  :deep(.el-tree-node__content) {
    height: auto !important;
  }

  .spot-list {
    list-style-type: none; /* 去掉默认的列表样式 */
    padding-left: 20px; /* 给圆点留出空间 */
  }

  .spot-list li {
    line-height: 1.7;
    position: relative; /* 允许伪元素定位 */
    margin-bottom: 8px; /* 可选：调整列表项间距 */
    padding-left: 0; /* 确保内容与圆点保持对齐 */
    text-indent: 0; /* 避免缩进 */
  }

  .spot-list li::before {
    content: ''; /* 添加伪元素 */
    position: absolute;
    left: -15px; /* 定位到最左侧 */
    top: 17px; /* 确保与第一行对齐 */
    width: 6px; /* 圆点宽度 */
    height: 6px; /* 圆点高度 */
    background-color: #1456f0; /* 设置圆点颜色 */
    border-radius: 50%; /* 圆点形状 */
  }
</style>
