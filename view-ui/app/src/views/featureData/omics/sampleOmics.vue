<template>
  <div class="d-flex justify-space-between mb-1">
    <div>
      <el-input
        v-model="searchID"
        placeholder="Search for ID"
        style="width: 350px"
      ></el-input>
      <el-button class="radius-12 ml-1 mr-1" type="primary">Search </el-button>
    </div>
    <div>
      <TypeList
        :original-data="originalData"
        type-name="Experiment Type"
        :type-list="colData.slice(2)"
      ></TypeList>
      <ToolBar :columns="colData" :width="300" :checkbox-width="140"></ToolBar>
    </div>
  </div>
  <el-table
    tooltip-effect="dark"
    :data="sampleOmicsData"
    :header-cell-style="{
      backgroundColor: '#F8F8F8',
      color: '#333333',
      fontWeight: 700,
    }"
    border
  >
    <el-table-column
      v-if="colData[0].visible"
      prop="sampID"
      label="Sample ID"
      width="100"
    >
      <template #default="scope">
        <router-link
          :to="`/sample/detail/${scope.row.sampID}`"
          class="text-primary"
        >
          {{ scope.row.sampID }}</router-link
        >
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[1].visible"
      prop="sampType"
      label="Sample Type"
      show-overflow-tooltip
      width="160"
    />
    <el-table-column
      v-if="colData[1].visible"
      prop="genomic"
      label="Genomic"
      width="90"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.genomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[2].visible"
      prop="transcriptomic"
      label="Transcriptomic"
      min-width="120"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.transcriptomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[3].visible"
      prop="transcriptomicSingCell"
      label="Transcriptomic sing cell"
      min-width="180"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.transcriptomicSingCell === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[4].visible"
      prop="microarray"
      label="Microarray"
      min-width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.microarray === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[5].visible"
      prop="proteomic"
      label="Proteomic"
      width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.proteomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[6].visible"
      prop="metagenomic"
      label="Metagenomic"
      min-width="120"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metagenomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[7].visible"
      min-width="150"
      prop="metatranscriptomic"
      label="Metatranscriptomic"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metatranscriptomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[8].visible"
      prop="metabolomic"
      label="Metabolomic"
      min-width="110"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metabolomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[9].visible"
      prop="other"
      label="Other"
      width="80"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.other === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import ToolBar from '@/components/toolBar.vue';
  import TypeList from './typeList.vue';

  import { reactive, ref, watch } from 'vue';

  const searchID = ref('');

  const originalData = reactive([]);
  const sampleOmicsData = reactive([
    {
      sampID: 'OES000001',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: 'Y',
      transcriptomicSingCell: '',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000002',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: 'Y',
      transcriptomicSingCell: '',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000003',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: '',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000004',
      sampType: 'Cell line',
      genomic: 'Y',
      transcriptomic: '',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000005',
      sampType: 'Environment no-host',
      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: '',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000006',
      sampType: 'Environment no-host',
      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: 'Y',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000007',
      sampType: 'Environment no-host',
      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: 'Y',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
    {
      sampID: 'OES000008',
      sampType: 'Environment no-host',

      genomic: '',
      transcriptomic: 'Y',
      transcriptomicSingCell: 'Y',
      microarray: '',
      proteomic: 'Y',
      metagenomic: '',
      metatranscriptomic: '',
      metabolomic: '',
      other: '',
    },
  ]);

  const colData = ref([
    { label: `Sample ID`, visible: true, show: true },
    { label: `Sample Type`, visible: true, show: true },
    { label: `Genomic`, visible: true, show: true },
    { label: `Transcriptomic`, visible: true, show: true },
    { label: `Transcriptomic sing cell`, visible: true, show: true },
    { label: `Microarray`, visible: true, show: true },
    { label: `Proteomic`, visible: true, show: true },
    { label: `Metagenomic`, visible: true, show: true },
    { label: `Metatranscriptomic`, visible: true, show: true },
    { label: `Metabolomic`, visible: true, show: true },
    { label: `Other`, visible: true, show: true },
  ]);

  function initFdData() {
    console.log('initFdData3');
  }

  defineExpose({
    initFdData,
  });
</script>

<style lang="scss" scoped></style>
