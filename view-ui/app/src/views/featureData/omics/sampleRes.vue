<template>
  <div class="d-flex justify-space-between mb-1">
    <div>
      <el-input
        v-model="searchID"
        placeholder="Search for ID"
        style="width: 350px"
      ></el-input>
      <el-button class="radius-12 ml-1 mr-1" type="primary">Search </el-button>
    </div>
    <div>
      <TypeList
        :original-data="originalData"
        type-name="Sample Type"
        :type-list="colData.slice(1)"
      ></TypeList>

      <ToolBar :columns="colData" :width="300" :checkbox-width="140"></ToolBar>
    </div>
  </div>
  <el-table
    tooltip-effect="dark"
    :data="sampData"
    :header-cell-style="{
      backgroundColor: '#F8F8F8',
      color: '#333333',
      fontWeight: 700,
    }"
    border
  >
    <el-table-column
      v-if="colData[0].visible"
      prop="projID"
      label="Project ID"
      width="100"
    >
      <template #default="scope">
        <router-link
          :to="`/project/detail/${scope.row.projID}`"
          class="text-primary"
        >
          {{ scope.row.projID }}</router-link
        >
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[1].visible"
      prop="human"
      label="Human"
      width="90"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.human === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[2].visible"
      prop="animalia"
      label="Animalia"
      min-width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.animalia === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[3].visible"
      prop="plantae"
      label="Plantae"
      min-width="100"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.plantae === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[4].visible"
      prop="pathogen"
      label="Pathogen affecting public health"
      min-width="240"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.pathogen === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[5].visible"
      prop="cellLine"
      label="Cell Line"
      width="80"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.cellLine === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[6].visible"
      prop="environmentHost"
      label="Environment Host"
      min-width="140"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.environmentHost === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[7].visible"
      min-width="180"
      prop="environmentNoHost"
      label="Environment non-host"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.metatranscriptomic === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
    <el-table-column
      v-if="colData[8].visible"
      prop="microbe"
      label="Microbe"
      min-width="80"
    >
      <template #default="scope">
        <el-icon v-if="scope.row.microbe === 'Y'" color="#3A78E8"
          ><Select
        /></el-icon>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  import ToolBar from '@/components/toolBar.vue';
  import TypeList from './typeList.vue';

  import { reactive, ref, watch } from 'vue';

  const searchID = ref('');
  const originalData = reactive([]);
  const sampData = reactive([
    {
      projID: 'OEP000001',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000002',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000003',
      human: 'Y',
      animalia: '',
      plantae: '',
      pathogen: 'Y',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000004',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: 'Y',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000005',
      human: 'Y',
      animalia: 'Y',
      plantae: '',
      pathogen: 'Y',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000006',
      human: 'Y',
      animalia: 'Y',
      plantae: 'Y',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000007',
      human: '',
      animalia: 'Y',
      plantae: 'Y',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
    {
      projID: 'OEP000008',
      human: 'Y',
      animalia: '',
      plantae: 'Y',
      pathogen: '',
      cellLine: '',
      environmentHost: '',
      environmentNoHost: '',
      Microbe: '',
    },
  ]);

  const colData = ref([
    { label: `Project ID`, visible: true, show: true },
    { label: `Human`, visible: true, show: true },
    { label: `Animalia`, visible: true, show: true },
    { label: `Plantae`, visible: true, show: true },
    { label: `Pathogen affecting public health`, visible: true, show: true },
    { label: `Cell line`, visible: true, show: true },
    { label: `Environment host`, visible: true, show: true },
    { label: `Environment non-host`, visible: true, show: true },
    { label: `Microbe`, visible: true, show: true },
  ]);

  function initFdData() {
    console.log('initFdData2');
  }

  defineExpose({
    initFdData,
  });
</script>

<style lang="scss" scoped></style>
