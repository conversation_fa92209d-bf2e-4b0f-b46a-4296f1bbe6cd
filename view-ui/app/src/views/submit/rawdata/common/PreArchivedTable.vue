<template>
  <div class="unarchivedTable">
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        placeholder="Search for [File Name]"
        clearable
        class="w-50"
        @keydown.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">Period</span>
      <el-date-picker
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="To"
        start-placeholder="Start date"
        end-placeholder="End date"
      />
      <el-button class="radius-12 ml-1" type="primary" @click="getDataList"
        >Search
      </el-button>
      <el-tooltip
        :content="'If you do not select row, all data you search will be exported by default.'"
        :teleported="false"
      >
        <el-button
          v-if="showExportBtn"
          class="radius-12 btn-round-primary"
          :icon="Download"
          round
          @click="handleExport"
          >Export
        </el-button>
      </el-tooltip>
    </div>
    <!--    <el-divider v-if="showFilter" />
    <div v-if="showFilter" class="status-select mb-1">
      <span class="font-600 text-main-color mr-2">Status:</span>
      <el-radio-group v-model="queryParams.status" @change="getDataList">
        <el-radio label="">All</el-radio>
        <el-radio label="editing">Un Archived</el-radio>
        <el-radio label="waiting">Archived</el-radio>
      </el-radio-group>
    </div>-->
    <el-table
      ref="table"
      v-loading="loading"
      class="mt-1"
      :data="tableData"
      stripe
      :row-key="row => row.id"
      style="width: 100%; margin-bottom: 20px"
      show-overflow-tooltip
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      :default-sort="defaultSort"
      border
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :reserve-selection="true" width="40" />
      <el-table-column prop="datNo" label="Data ID" sortable width="120" />
      <el-table-column prop="name" label="File Name" min-width="150" sortable />
      <el-table-column
        v-if="showArchivedColumn && !queryParams.rawData"
        prop="analNo"
        label="Analysis"
        width="130"
      />
      <el-table-column
        v-if="showArchivedColumn && queryParams.rawData"
        prop="projectNo"
        label="Project"
        width="130"
      />
      <el-table-column
        v-if="showArchivedColumn && queryParams.rawData"
        prop="expNo"
        label="Experiment"
        width="130"
      />
      <el-table-column
        v-if="showArchivedColumn && queryParams.rawData"
        prop="sapNo"
        label="Sample"
        width="130"
      />
      <el-table-column
        v-if="showArchivedColumn && queryParams.rawData"
        prop="runNo"
        label="Run"
        width="150"
      />
      <el-table-column
        prop="uploadType"
        label="Upload Type"
        width="130"
        sortable
      />
      <el-table-column prop="fileSize" label="File Size" width="100" sortable>
        <template #default="scope">
          {{ scope.row.readableFileSize }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        label="Upload Date"
        width="155"
        sortable
      />
    </el-table>
    <el-button
      v-if="showCancelArchiveBtn"
      :disabled="
        selectedRows.filter(
          it => !isStrBlank(it.runNo) || !isStrBlank(it.analNo),
        ).length === 0
      "
      style="float: right"
      type="danger"
      class="btn"
      plain
      round
      @click="handleCancelArchive"
      >Cancel Archive
    </el-button>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
  </div>
</template>

<script setup>
  import {
    getCurrentInstance,
    onActivated,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';

  import { listPreArchivedData } from '@/api/metadata/data';
  import useSubmissionStore from '@/store/modules/metadata';
  import { storeToRefs } from 'pinia';
  import { Download } from '@element-plus/icons-vue';
  import { isStrBlank } from '@/utils';

  const { proxy } = getCurrentInstance();
  const submissionStore = useSubmissionStore();

  const { subNo } = storeToRefs(submissionStore);
  defineExpose({
    getDataList,
    removeSelection,
  });

  onActivated(() => {
    getDataList();
    proxy.$refs['table'].clearSelection();
  });

  const props = defineProps({
    showExportBtn: {
      type: Boolean,
      required: false,
      default: false,
    },
    showOperateCol: {
      type: Boolean,
      required: false,
      default: false,
    },
    showCancelArchiveBtn: {
      type: Boolean,
      required: false,
      default: true,
    },
    selectedRows: {
      type: Array,
      required: false,
      default: () => [],
    },
    tableType: {
      type: String,
      required: true,
      default: 'rawData',
    },
    showFilter: {
      type: Boolean,
      required: false,
      default: false,
    },
    showArchivedColumn: {
      type: Boolean,
      required: false,
      default: true,
    },
    archived: {
      type: String,
      required: false,
    },
  });
  let selectedRows = ref(
    props.selectedRows && props.selectedRows.length > 0
      ? props.selectedRows
      : [],
  );
  // 监听用户输入的值，动态修改父组件的值
  watch(
    selectedRows,
    newVal => {
      proxy.$emit('update:selectedRows', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );
  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      rawData: props.tableType === 'rawData',
      subNo: subNo,
      name: '',
      archived: props.archived,
      pageNum: 1,
      pageSize: 10,
    },
    dateRange: [],
    defaultSort: { prop: '', order: '' },
    loading: true,
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, defaultSort, loading } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listPreArchivedData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  /** handleSelectionChange */
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  /** 移除选中的数据 */
  function removeSelection(row) {
    proxy.$refs['table'].toggleRowSelection(row, false);
  }

  /** 检查是否可以选中 */
  function checkSelectTable(row) {
    return !(row.analNo || row.projectNo);
  }

  /** 导出 */
  function handleExport() {
    let b = selectedRows.value.length === 0;
    let content = '';
    if (b) {
      content = 'All table data will be exported';
    } else {
      content = 'Selected table data will be exported';
    }
    const downloadParams = b
      ? { ...queryParams.value }
      : {
          ...queryParams.value,
          ids: selectedRows.value.map(it => it.id),
        };

    proxy.$modal.confirm(content).then(() => {
      let filename = props.archived === 'yes' ? 'Archiving' : 'Unarchived';
      proxy.download(
        '/upload/data/export/preArchive',
        downloadParams,
        `${filename}_${new Date().getTime()}.xlsx`,
      );
    });
  }

  function handleCancelArchive() {
    proxy.$emit('cancel-archive');
  }
</script>

<style scoped lang="scss">
  :deep(.el-divider--horizontal) {
    margin: 5px 0;
  }
</style>
