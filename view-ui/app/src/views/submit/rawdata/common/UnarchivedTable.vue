<template>
  <div class="unarchivedTable">
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        placeholder="Search for [Data ID, File Name, MD5]"
        clearable
        class="w-50"
        @keydown.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">Period</span>
      <el-date-picker
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="To"
        start-placeholder="Start date"
        end-placeholder="End date"
      />
      <el-button class="radius-12 ml-1" type="primary" @click="getDataList"
        >Search
      </el-button>
    </div>
    <el-table
      ref="table"
      v-loading="loading"
      class="mt-1"
      :data="tableData"
      stripe
      :row-key="row => row.id"
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      :default-sort="defaultSort"
      border
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :reserve-selection="true" width="40" />
      <el-table-column prop="datNo" label="Data ID" sortable width="130" />
      <el-table-column prop="name" label="File Name" sortable>
        <template #default="scope">
          {{ scope.row.name }}
          <el-tooltip
            placement="bottom"
            trigger="hover"
            content="Edit File Name"
          >
            <svg-icon
              icon-class="pen_edit"
              class-name="svg svg-edit"
              @click="openEditDialog(scope.row.datNo, scope.row.name)"
            ></svg-icon>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="sourcePath"
        label="Source Path"
        sortable
        show-overflow-tooltip
      />
      <el-table-column prop="dataType" label="Data Type" sortable width="130" />
      <el-table-column
        prop="uploadType"
        label="Upload Type"
        width="130"
        sortable
      />
      <el-table-column prop="fileSize" label="File Size" width="150" sortable>
        <template #default="scope">
          {{ scope.row.readableFileSize }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        label="Upload Date"
        width="155"
        sortable
      />
      <el-table-column label="Operate" width="100">
        <template #default="scope">
          <el-button
            link
            type="danger"
            size="small"
            @click="handleDelete(scope.row)"
            >Delete
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
    <el-divider />
    <div v-if="showToolbox" class="text-align-right">
      <el-button
        class="btn-round-primary"
        :icon="Download"
        round
        @click="handleExport"
        >Export
      </el-button>
      <el-button
        class="btn-round-warning"
        round
        :icon="Delete"
        @click="handleDelete"
        >Delete
      </el-button>
    </div>
    <el-dialog v-model="editDialogVisible" title="Edit File Name" width="500">
      <el-row class="mb-1">
        <el-alert
          title="Notice: Only the file name can be edited, but the file suffix cannot be edited."
          type="error"
          :closable="false"
        />
      </el-row>
      <el-input v-model="editName" style="width: 400px" />
      {{ '.' + nameSuffix }}
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">Cancel</el-button>
          <el-button type="primary" @click="handleEditName">
            Confirm
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import { Delete, Download } from '@element-plus/icons-vue';
  import {
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';

  import {
    deleteUnarchivedData,
    editFileName,
    listUnarchived,
  } from '@/api/metadata/data';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { getFileNamePrefix, getFileNameSuffix } from '@/utils';

  const { proxy } = getCurrentInstance();
  defineExpose({
    getDataList,
    removeSelection,
  });

  onMounted(() => {
    getDataList();
  });

  const props = defineProps({
    showToolbox: {
      type: Boolean,
      required: false,
      default: true,
    },
    selectedRows: {
      type: Array,
      required: false,
      default: () => [],
    },
  });
  let selectedRows = ref(
    props.selectedRows && props.selectedRows.length > 0
      ? props.selectedRows
      : [],
  );
  // 监听用户输入的值，动态修改父组件的值
  watch(
    selectedRows,
    newVal => {
      proxy.$emit('update:selectedRows', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );
  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
    },
    dateRange: [],
    defaultSort: { prop: 'datNo', order: 'descending' },
    loading: true,
  });
  /** 解构 */
  const { tableData, total, queryParams, dateRange, defaultSort, loading } =
    toRefs(data);

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listUnarchived(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column, prop, order) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  /** handleSelectionChange */
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  /** 移除选中的数据 */
  function removeSelection(row) {
    proxy.$refs['table'].toggleRowSelection(row, false);
  }

  /* 批量删除文件 */
  function handleDelete(row) {
    let selected = [];
    if (row instanceof PointerEvent) {
      selected = selectedRows.value;
    } else {
      selected = [row];
    }
    if (selected.length === 0) {
      proxy.$modal.msgError('Please select the files that need to delete');
      return;
    }
    proxy.$modal
      .confirm(
        `The following unarchived data will be deleted. Do you want to continue?<br>
                ${selected.map(x => x.datNo).join('; ')}`,
      )
      .then(() => {
        return deleteUnarchivedData(selected.map(it => it.id));
      })
      .then(() => {
        getDataList();
      });
  }

  /* 批量导出 */
  function handleExport() {
    let b = selectedRows.value.length === 0;
    let content = '';
    if (b) {
      content = 'All unarchived table data will be exported';
    } else {
      content = 'Selected unarchived table data will be exported';
    }

    const downloadParams = b
      ? { ...queryParams.value }
      : {
          ...queryParams.value,
          ids: selectedRows.value.map(it => it.id),
        };

    proxy.$modal.confirm(content).then(() => {
      proxy.download(
        '/upload/data/export/unarchived',
        downloadParams,
        `unarchived_${new Date().getTime()}.xlsx`,
      );
    });
  }

  let editDialogVisible = ref(false);
  let editDataNo = ref('');
  let editName = ref('');
  let nameSuffix = ref('');

  function openEditDialog(dataNo, name) {
    editDataNo.value = dataNo;
    editName.value = getFileNamePrefix(name);
    nameSuffix.value = getFileNameSuffix(name);
    editDialogVisible.value = true;
  }

  function handleEditName() {
    proxy.$modal.loading('saving...');
    editFileName({
      dataNo: editDataNo.value,
      name: editName.value,
    })
      .then(() => {
        editDialogVisible.value = false;
        getDataList();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
</script>

<style scoped lang="scss">
  .svg {
    width: 23px;
    height: 23px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .svg-edit {
    position: relative;
    top: 8px;
    width: 16px;
    margin-left: 0.2rem;
  }
</style>
