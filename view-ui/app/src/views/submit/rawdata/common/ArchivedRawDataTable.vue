<template>
  <div class="archived-data mb-1">
    <div class="d-flex align-items-center mb-1">
      <el-input
        v-model="queryParams.name"
        placeholder="Search for [File Name]"
        clearable
        class="w-50"
        @keydown.enter="getDataList"
      />
      <span class="font-600 text-main-color ml-1 mr-1">Period</span>
      <el-date-picker
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="To"
        start-placeholder="Start date"
        end-placeholder="End date"
      />
      <el-button class="radius-12 ml-1" type="primary" @click="getDataList"
        >Search
      </el-button>
      <el-tooltip
        :content="'If you do not select row, all data you search will be exported by default.'"
        :teleported="false"
      >
        <el-button
          v-if="showExportBtn"
          class="radius-12 btn-round-primary"
          :icon="Download"
          round
          @click="handleExport"
          >Export
        </el-button>
      </el-tooltip>
    </div>
    <el-table
      ref="table"
      v-loading="loading"
      class="mt-1"
      :data="tableData"
      stripe
      :row-key="row => row.id"
      style="width: 100%; margin-bottom: 20px"
      show-overflow-tooltip
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      :default-sort="defaultSort"
      border
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :reserve-selection="true" width="40" />
      <el-table-column prop="datNo" label="Data ID" sortable width="130" />
      <el-table-column prop="name" label="File Name" sortable />
      <el-table-column prop="projectNo" label="Project">
        <template #default="scope">
          <span>{{
            formatIdAndName(scope.row.projectNo, scope.row.projectName)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="expNo" label="Experiment">
        <template #default="scope">
          <span
            >{{ formatIdAndName(scope.row.expNo, scope.row.expName) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="sapNo" label="Sample">
        <template #default="scope">
          <span>{{ formatIdAndName(scope.row.sapNo, scope.row.sapName) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="runNo" label="Run">
        <template #default="scope">
          <span
            >{{ formatIdAndName(scope.row.runNo, scope.row.runName) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column prop="fileSize" label="File Size" width="150" sortable>
        <template #default="scope">
          {{ scope.row.readableFileSize }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createDate"
        label="Upload Date"
        width="155"
        sortable
      />
    </el-table>
    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />
  </div>
</template>
<script setup>
  import {
    getCurrentInstance,
    onActivated,
    reactive,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import { listArchivedRawData } from '@/api/metadata/data';
  import { Download } from '@element-plus/icons-vue';
  import { formatIdAndName } from '@/utils';

  const { proxy } = getCurrentInstance();
  defineExpose({
    getDataList,
    removeSelection,
  });

  const props = defineProps({
    selectedRows: {
      type: Array,
      required: false,
      default: () => [],
    },
    showExportBtn: {
      type: Boolean,
      required: false,
      default: false,
    },
  });

  let selectedRows = ref(
    props.selectedRows && props.selectedRows.length > 0
      ? props.selectedRows
      : [],
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    selectedRows,
    newVal => {
      proxy.$emit('update:selectedRows', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );

  /** 响应式数据 */
  const data = reactive({
    tableData: [],
    total: 0,
    queryParams: {
      name: '',
      pageNum: 1,
      pageSize: 10,
    },
    dateRange: [],
    defaultSort: { prop: 'datNo', order: 'descending' },
    loading: true,
  });
  // 解构
  const { tableData, total, queryParams, dateRange, defaultSort, loading } =
    toRefs(data);

  /** onActivated */
  onActivated(() => {
    getDataList();
    proxy.$refs['table'].clearSelection();
  });

  /** 查询列表数据*/
  function getDataList() {
    loading.value = true;
    listArchivedRawData(proxy.addDateRange(queryParams.value, dateRange.value))
      .then(response => {
        // 将结果赋值给tableData
        tableData.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 排序触发事件 */
  function handleSortChange(column) {
    if (column.order) {
      queryParams.value.orderByColumn = column.prop;
      queryParams.value.isAsc = column.order;
      getDataList();
    }
  }

  /** handleSelectionChange */
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
  }

  /** 移除选中的数据 */
  function removeSelection(row) {
    proxy.$refs['table'].toggleRowSelection(row, false);
  }

  /** 导出 */
  function handleExport() {
    let b = selectedRows.value.length === 0;
    let content = '';
    if (b) {
      content = 'All table data will be exported';
    } else {
      content = 'Selected table data will be exported';
    }
    const downloadParams = b
      ? { ...queryParams.value }
      : {
          ...queryParams.value,
          ids: selectedRows.value.map(it => it.id),
        };

    proxy.$modal.confirm(content).then(() => {
      proxy.download(
        '/upload/data/export/archived/rawData',
        downloadParams,
        `Archived_RawData_${new Date().getTime()}.xlsx`,
      );
    });
  }
</script>
<style scoped lang="scss">
  .archived-data {
    :deep(.el-input__wrapper) {
      border-radius: 12px;
    }

    .date-to {
      margin: 0 0.5rem;
    }

    :deep(.el-table__header .cell) {
      font-weight: 600;
    }

    :deep(.el-radio__input.is-checked) {
      .el-radio__inner {
        border: none;
        background-color: #fe7f2b;
      }

      & + .el-radio__label {
        color: #333333;
      }
    }

    :deep(.el-table td.el-table__cell div) {
      display: flex;
      align-items: center;
    }
  }
</style>
