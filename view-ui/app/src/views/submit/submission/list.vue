<template>
  <div class="page submission">
    <div class="container-fluid">
      <Breadcrumb bread-item="My Submission" />
      <div class="card mt-1 hidden-xs-only">
        <h2 class="text-main-color">Start New Submission</h2>
        <div class="d-flex align-items-center mb-1">
          <div class="d-flex flex-column align-items-center">
            <router-link to="/submit/rawdata" class="step rawdata mb-05"
              >UploadData
            </router-link>
            <el-icon
              :size="22"
              color="#999999"
              style="position: relative; top: 8px"
            >
              <CaretBottom />
            </el-icon>
            <router-link to="/submit/metadata" class="step metadata mt-05 mb-05"
              >Metadata
            </router-link>
            <el-icon
              :size="22"
              color="#999999"
              style="position: relative; top: 18px"
            >
              <CaretBottom />
            </el-icon>
            <router-link to="/submit/metadata" class="step archiving mt-2"
              >Archiving Data
            </router-link>
          </div>
          <div class="ml-1">
            <p class="bg-gray radius-14 mb-1">
              The NODE accepts a variety of omics data and corresponding
              analysis data, such as genomics transcriptomic, metagenomics,
              proteomics data and so on. A personal account is required before
              you upload raw data. There are three ways to accomplish the raw
              data uploading: HTTP, FTP, Express. An MD5 checksum file for all
              uploaded data is also need.
            </p>
            <p class="bg-gray radius-14 mb-1">
              The NODE metadata specifies the relevant information about raw
              data, which helps in identifying the feature of the data. For
              example, the publication information of a rawdata dataset is
              typical described in a project, sequencing methods details are
              showed in experiments, source material is illustrated in samples.
            </p>
            <p class="bg-gray radius-14">
              In this part, rawdata have been uploaded in a storage location and
              metadata have been created. Rawdata is represented in the database
              as run objects, which can associate rawdata with metadata. Thus,
              the function of archiving is to link metadata with rawdata by
              building run objects. After archiving, the whole data publishing
              process is completed.
            </p>
          </div>
        </div>
      </div>
      <div class="card list mt-1">
        <h2 class="text-main-color">Submission List</h2>
        <my-submission-list :height="450"></my-submission-list>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import MySubmissionList from '@/views/submit/components/mySubmissionList.vue';
</script>

<style lang="scss" scoped>
  .submission {
    padding: 20px 0 25px 0;

    .step {
      width: 140px;
      text-align: center;
      background-color: #3a78e8;
      color: #ffffff;
      font-size: 16px;
      border-radius: 14px;
      padding: 15px;
      box-shadow: 0 3px 6px 0 #c6bebe;

      &.metadata {
        background-color: #07bbb3;
        position: relative;
        top: 12px;
      }

      &.archiving {
        background-color: #fe7f2b;
      }
    }

    p {
      display: flex;
      align-items: center;
      color: #666666;
      line-height: 1.5;
      padding: 8px 10px;
      text-align: justify;
      min-height: 84px;
    }

    .list {
      a {
        &:hover {
          color: #3a78e8;
        }
      }

      .number {
        margin-left: 0.1rem;
      }
    }
  }

  :deep(.tool-tip) {
    word-break: break-word;
    min-width: 210px;
  }

  :deep(.el-divider--horizontal) {
    margin: 5px 0;
  }

  .svg-data {
    width: 20px;
    height: 22px;
  }

  .edit-svg {
    width: 19px;
    height: 19px;
  }
</style>
