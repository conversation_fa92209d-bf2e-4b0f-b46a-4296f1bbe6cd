<template>
  <div v-if="show" class="bg-gray p-15 mt-1">
    <el-row :gutter="20" class="detail-submitter">
      <el-col :span="8">
        <div class="label font-600 text-secondary-color">Journal</div>
        <div class="content radius-8 bg-white">
          {{ $text(publish?.publication) }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label font-600 text-secondary-color">DOI</div>
        <div class="content radius-8 bg-white">
          {{ $text(publish?.doi) }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label font-600 text-secondary-color">PMID</div>
        <div class="content radius-8 bg-white">
          {{ $text(publish?.pmid) }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label font-600 text-secondary-color">Title</div>
        <div class="content radius-8 bg-white">
          {{ $text(publish?.articleName) }}
        </div>
      </el-col>
      <el-col :span="8">
        <div class="label font-600 text-secondary-color">Reference</div>
        <div class="content radius-8 bg-white">
          {{ $text(publish?.reference) }}
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';

  const props = defineProps({
    publishData: {
      type: Object,
      required: false,
      default() {
        return {
          publication: undefined,
          doi: undefined,
          pmid: undefined,
          articleName: undefined,
          reference: undefined,
        };
      },
    },
  });

  const publish = reactive(props.publishData);
  const show = ref(false);

  watch(
    publish,
    newVal => {
      if (!newVal) {
        show.value = false;
        return;
      }
      if (
        newVal.publication ||
        newVal.doi ||
        newVal.pmid ||
        newVal.articleName ||
        newVal.reference
      ) {
        show.value = true;
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>
