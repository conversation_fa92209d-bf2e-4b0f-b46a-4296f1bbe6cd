<template>
  <el-dialog
    v-model="dialogVisible"
    width="25%"
    class="dialog radius-14"
    append-to-body
  >
    <div class="d-flex align-items-center justify-center">
      <svg
        t="1703494573388"
        class="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        p-id="5547"
        width="20"
        height="20"
      >
        <path
          d="M932.54 573.38C950.08 543.68 960 512 960 473.7c0-88.03-74.436-171.16-171.64-171.16H715.4c9.84-25.62 17.7-56.26 17.7-93.08C733.1 63.872 657.72 0 542.56 0c-123.214 0-116.186 189.866-143.52 217.2-45.494 45.494-99.23 132.894-137.52 166.8H64c-35.346 0-64 28.654-64 64v480c0 35.346 28.654 64 64 64h128c29.786 0 54.816-20.348 61.956-47.9 89.018 2.002 150.12 79.88 355.604 79.88 14.44 0 30.44 0.02 44.44 0.02 154.234 0 223.972-78.846 225.88-190.66 26.638-36.85 40.598-86.244 34.68-133.98 19.708-36.904 27.328-80.686 17.98-125.98z m-123.5 107.66c25.12 42.26 2.52 98.82-27.88 115.14 15.4 97.56-35.216 131.8-106.24 131.8h-75.64c-143.278 0-236.058-75.64-343.28-75.64V480h21.84c56.72 0 135.96-141.78 189.08-194.92 56.72-56.72 37.82-151.26 75.64-189.08 94.54 0 94.54 65.96 94.54 113.46 0 78.34-56.72 113.44-56.72 189.08h207.98c42.22 0 75.46 37.82 75.64 75.64 0.18 37.8-25.64 75.62-44.54 75.62 26.978 29.11 32.742 90.472-10.42 131.24zM176 864c0 26.51-21.49 48-48 48s-48-21.49-48-48 21.49-48 48-48 48 21.49 48 48z"
          p-id="5548"
          fill="#07BCB4"
        ></path>
      </svg>
      <p class="ml-05 text-main-color font-20 font-600">
        {{ props.textTip }}
      </p>
    </div>
    <div class="font-16 mt-1 text-center">
      You can click the <strong>Submit</strong> button to submit your data
    </div>
    <div class="font-16 mt-05 mb-1 text-center">
      You can also view the submission results in
      <router-link
        to="/submit/submission/list"
        class="text-primary to-submission"
        >my submission page
      </router-link>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <div class="text-align-center">
          <el-button
            type="primary"
            class="btn-primary btn btn-s btn-shadow"
            round
            @click="submitSubmission"
            >Submit</el-button
          >
          <el-button
            class="btn-primary btn btn-round"
            round
            @click="dialogVisible = false"
            >Back Edit</el-button
          >
        </div>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { getCurrentInstance, ref } from 'vue';
  import { submit } from '@/api/submission';
  import { useRouter } from 'vue-router';

  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const dialogVisible = ref(false);
  const subNo = ref('');
  let props = defineProps({
    textTip: {
      type: String,
      required: false,
      default: 'The archive is completed.',
    },
  });

  function showDialog(no) {
    subNo.value = no;
    dialogVisible.value = true;
  }

  function submitSubmission() {
    if (!subNo.value) {
      proxy.$modal.msgError('Submission NO cannot be empty');
    }
    proxy.$modal
      .confirm(`Confirm submitting data for ${subNo.value}?`)
      .then(() => {
        proxy.$modal.loading('Submitting, please wait');
        submit(subNo.value)
          .then(response => {
            if (response.data) {
              proxy.$alert(
                "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
                  response.data +
                  '</div>',
                'Error',
                { dangerouslyUseHTMLString: true },
              );
            } else {
              toList();
            }
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      });
  }

  const toList = () => {
    router.push({
      path: `/submit/submission/list`,
    });
  };

  defineExpose({
    showDialog,
  });
</script>
