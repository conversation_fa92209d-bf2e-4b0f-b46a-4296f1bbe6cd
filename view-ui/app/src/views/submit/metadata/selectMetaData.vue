<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="Metadata" />
      <div class="card mt-1">
        <div class="MetaData">
          <h2 class="text-main-color text-align-center mb-2">
            Select the option that best describes your submission
          </h2>
          <div class="d-flex justify-space-between mb-2">
            <div
              class="choose radius-12 mr-2 flex-1"
              :class="{ active: isActive === 'rawData' }"
              @click="selectData('rawData')"
            >
              <h3 class="text-align-center">Raw data</h3>
              <div class="text-center">
                Choose this step to archive a variety of omics data such as
                genomics,transcriptomic, metagenomics fastq data, proteomics raw
                data and so on.
              </div>
            </div>
            <div
              class="choose radius-12 flex-1"
              :class="{ active: isActive === 'analysisData' }"
              @click="selectData('analysisData')"
            >
              <h3 class="text-align-center">Analysis data</h3>
              <div class="text-center">
                Choose this step to archive analysis data related to omics data,
                such as vcf, bam, tsy, txt files and so on.
              </div>
            </div>
          </div>
          <div class="text-align-center mt-2">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="toSubmit"
              >Continue
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import { getCurrentInstance, onMounted, onUnmounted, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import useSubmissionStore from '@/store/modules/metadata';

  const router = useRouter();
  const { proxy } = getCurrentInstance();

  const isActive = ref('rawData');
  const selectData = val => {
    isActive.value = val;
  };

  onMounted(() => {
    // 重置Submission
    useSubmissionStore().update({});
  });

  const toSubmit = () => {
    proxy.$modal.loading();
    router.push({
      path: `/submit/metadata/${isActive.value}`,
    });
  };

  onUnmounted(() => {
    proxy.$modal.closeLoading();
  });
</script>

<style lang="scss" scoped>
  .submit-page {
    .submit-step {
      margin: 20px 0;

      svg {
        width: 20px;
      }

      & > div {
        flex: 1;
        display: flex;
        color: #333333;
        justify-content: space-between;
        align-items: center;
        border: 2px solid;
        padding: 0 32px;
        border-radius: 30px;
        margin-right: 12px;
        position: relative;
        transition: all 0.5s ease;

        &:hover {
          cursor: pointer;
        }

        &.active:after {
          position: absolute;
          content: '';
          border: 8px solid;
          left: 8%;
          bottom: -17px;
          border-color: #3a78e8 transparent transparent transparent;
          /* transform: translateX(-50%); */
        }
      }

      & > div:last-child {
        margin-right: 0;
      }

      .rawdata {
        border-color: #3a78e8;

        .el-icon {
          color: #3a78e8;
        }

        &.active {
          background-color: #3a78e8;
          color: #fff;

          &:after {
            border-color: #3a78e8 transparent transparent transparent;
          }

          & .el-icon {
            color: #ffffff;
          }
        }
      }

      .metadata {
        border-color: #fe7f2b;

        .el-icon {
          color: #fe7f2b;
        }

        &.active {
          background-color: #fe7f2b;
          color: #fff;

          &:after {
            border-color: #fe7f2b transparent transparent transparent;
          }

          & .el-icon {
            color: #ffffff;
          }
        }
      }

      .archiving {
        border-color: #07bcb4;

        el-icon {
          color: #07bcb4;
        }

        &.active {
          background-color: #07bcb4;
          color: #fff;

          &:after {
            border-color: #07bcb4 transparent transparent transparent;
          }

          & .el-icon {
            color: #ffffff;
          }
        }
      }
    }
  }

  .MetaData {
    padding: 30px;

    .choose {
      padding: 20px;
      border: 2px solid #999999;
      border-top-width: 20px;
      box-shadow: 0 3px 10px -2px rgba(226, 236, 232, 0.5);

      &.active {
        border-color: #3a78e8;

        & > h3 {
          color: #3a78e8;
        }
      }

      &:hover {
        cursor: pointer;
      }

      h3 {
        font-size: 22px;
        /* font-weight: 700; */
        margin: 0;

        & > div {
          font-size: 15px;
          color: #666666;
        }
      }
    }
  }
</style>
