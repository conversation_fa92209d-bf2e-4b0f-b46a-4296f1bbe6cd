<template>
  <div v-loading="currSampleModel.isRequesting" class="w-100">
    <div class="card card-container mt-1">
      <div class="category-title font-600 text-main-color">
        Sample Type and Information
      </div>
      <div class="mt-1">
        <h3 class="plr-20 d-flex align-items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="30"
            height="30"
            viewBox="0 0 32 32"
            fill="none"
          >
            <g opacity="1" transform="translate(0 0)  rotate(0)">
              <path
                id="路径 546"
                fill-rule="evenodd"
                style="fill: #3a78e8"
                opacity="1"
                d="M30.5027,16.0022c0,8.01 -6.49,14.51 -14.5,14.51c-8.01002,0 -14.51002,-6.5 -14.51002,-14.51c0,-8.01001 6.5,-14.51001 14.51002,-14.51001c8.01,0 14.5,6.5 14.5,14.51001zM18.7176,8.97919c0,-1.3 -1.06,-2.36 -2.36,-2.36c-1.3,0 -2.36,1.06 -2.36,2.36c0,1.30001 1.06,2.36001 2.36,2.36001c1.3,0 2.36,-1.06 2.36,-2.36001zM20.5514,23.3909l-0.99,-0.37c-0.56,-0.2 -0.93,-0.73 -0.93,-1.32v-8.32h-5.98v1.06l0.99,0.36c0.55,0.21 0.92,0.73 0.92,1.32v5.58c0,0.59 -0.37,1.12 -0.92,1.32l-0.99,0.37v1.03h7.9z"
              ></path>
            </g>
          </svg>
          <span class="font-600 ml-05"
            >After filling out a sample type and clicking save, you can choose a
            new type to continue uploading</span
          >
        </h3>

        <SampleType
          :key="'sample-type-mult'"
          v-model="currSampleModel"
          :curr-sub-no="subNo"
        ></SampleType>
      </div>

      <DownloadTemplate
        :key="`downloadMultSample${currStage}`"
        v-model="currSampleModel.isRequesting"
        v-model:show-sap-attr-detail-dialog="showSapAttrDetailDialog"
        :curr-stage="currStage"
        :curr-data-type="currSampleModel.currSampleType"
        @change-ht-table-data="changeHtTableData"
      ></DownloadTemplate>

      <div>
        <HtTable
          :key="'sample-ht-table-' + updateKey"
          ref="sampleHtTable"
          v-model="currSampleModel.isRequesting"
          :hot-table-data="hotTableData"
          :hot-columns="hotColumns"
          :sample-type="currSampleModel.currSampleType"
          :max-rows="maxRows"
          :custom-column="true"
          :upload-temp="uploadTempRef"
          @open-sample-dialog="openSampleDialog"
        ></HtTable>

        <ResultLog
          v-if="resultDialogOpen"
          ref="resultLog"
          :log-data="resultDialogData"
          :curr-exp-type="currSampleModel.currSampleType"
        ></ResultLog>
      </div>

      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext"
          >Continue
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="saveData"
          >Check & Save
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >Reset
        </el-button>
        <el-button
          :disabled="!subNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >Delete
        </el-button>
      </div>

      <LoadSampleData
        ref="loadSampleDataRef"
        @load-exist-data="loadExistData"
      ></LoadSampleData>
    </div>
    <DeleteLog ref="deleteLog" curr-type="Multiple Sample"></DeleteLog>

    <exp-sap-attr-detail-dialog
      v-model:curr-type="currSampleModel.currSampleType"
      v-model:show-dialog="showSapAttrDetailDialog"
      v-model:attr-list="attrList"
      base-type="Sample"
    >
    </exp-sap-attr-detail-dialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref, watch } from 'vue';
  import LoadSampleData from '@/views/submit/metadata/rawData/sample/LoadSampleData.vue';
  import DownloadTemplate from '@/views/submit/metadata/rawData/common/DownloadTemplate.vue';
  import SampleType from '@/views/submit/metadata/rawData/sample/SampleType.vue';
  import { initHtTableTitle } from '@/utils/ht/util';
  import useSubmissionStore from '@/store/modules/metadata';
  import { storeToRefs } from 'pinia';
  import { isArrEmpty } from '@/utils';
  import {
    batchSaveSample,
    deleteSample,
    findSampleByNosAndType,
    getSampleTypeData,
  } from '@/api/metadata/sample';
  import HtTable from '@/components/HtTable/index.vue';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import ResultLog from '@/views/submit/metadata/rawData/common/ResultLog.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';
  import ExpSapAttrDetailDialog from '@/views/submit/metadata/rawData/common/ExpSapAttrDetailDialog.vue';

  const { proxy } = getCurrentInstance();
  // 提交者信息
  const submissionStore = useSubmissionStore();
  // 提交编号
  const { subNo } = storeToRefs(submissionStore);

  const emit = defineEmits(['continueMessage']);
  const updateKey = ref(1);
  // 当前阶段
  const currStage = ref('sample');
  // 子组件公用对象，双向绑定
  const currSampleModel = ref({
    currSampleType: '',
    isRequesting: false,
    savedTypes: [],
  });

  // 表格数据
  const hotTableData = reactive([]);
  // 表格列配置
  const hotColumns = reactive([]);
  const uploadTempRef = ref({
    uploadFlag: false,
    data: null,
  });
  const hotTableLoading = ref(false);
  const maxRows = ref(8000);

  // 后台校验错误信息弹窗
  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);

  /** 上传表格数据 */
  function changeHtTableData(excelData, uploadTemp) {
    uploadTempRef.value.data = uploadTemp;
    uploadTempRef.value.uploadFlag = true;

    hotTableLoading.value = true;
    hotTableData.length = 0;
    excelData.forEach(item => {
      let row = {};
      for (let key2 in item) {
        row[key2] = item[key2]['value'];
      }
      hotTableData.push(row);
    });
    updateKey.value++;
  }

  /** 打开样本筛选弹窗 */
  function openSampleDialog() {
    const { initData } = proxy.$refs['loadSampleDataRef'];
    if (initData) {
      initData(currSampleModel.value.currSampleType);
    }
  }

  /** 加载样本筛选后数据到表格 */
  function loadExistData(nos) {
    if (!isArrEmpty(nos)) {
      proxy.$modal
        .confirm('Are you sure to add the selected data to the table?')
        .then(() => {
          const params = {
            sampleNos: nos,
            sampleType: currSampleModel.value.currSampleType,
          };
          findSampleByNosAndType(params).then(response => {
            const data = response.data;
            if (!isArrEmpty(data)) {
              const { closeDialog } = proxy.$refs['loadSampleDataRef'];
              if (closeDialog) {
                // 关闭筛选弹窗
                closeDialog();
              }
              hotTableData.push(...data);
              refreshHtTable();
              // 更新key将销毁组件，重新渲染
              // updateKey.value++;
            }
          });
        })
        .catch(() => {});
    }
  }

  function refreshHtTable() {
    // 调用updateHtData方法不会销毁组件
    const { updateHtData } = proxy.$refs['sampleHtTable'];
    if (updateHtData) {
      // 刷新表格数据
      updateHtData();
    }
  }

  let attrList = ref([]);
  let filterAttrList = ref([]);
  let selectName = ref('');

  function nameChange() {
    if (selectName.value) {
      filterAttrList.value = attrList.value.filter(
        it => it.attributesField.indexOf(selectName.value) !== -1,
      );
    } else {
      filterAttrList.value = attrList.value;
    }
  }

  let showSapAttrDetailDialog = ref(false);

  /** 初始化表格数据和配置 */
  function initHtTable(data) {
    initHtTableTitle(data, hotTableData, hotColumns);
  }

  /** 切换样本类型时，刷新表格 */
  function loadSampleTable() {
    changeLoadingFlag(true);
    let type = currSampleModel.value.currSampleType;
    if (type) {
      currSampleModel.value.savedTypes = [];
      getSampleTypeData(type, subNo.value)
        .then(response => {
          const { attributes, rows, savedTypes } = response.data;
          attrList.value = attributes;
          filterAttrList.value = attributes;
          initHtTable(attributes);
          if (!isArrEmpty(rows)) {
            hotTableData.length = 0;
            hotTableData.push(...rows);
          }
          currSampleModel.value.savedTypes = savedTypes;
          updateKey.value++;
        })
        .catch(() => {
          changeLoadingFlag(false);
        });
    } else {
      changeLoadingFlag(false);
    }
  }

  const resetForm = () => {
    loadSampleTable();
  };

  /** 继续 */
  function continueNext() {
    const { validateChanged } = proxy.$refs['sampleHtTable'];
    if (validateChanged) {
      if (validateChanged()) {
        sendMessage('ArchivingMultiple');
      } else {
        proxy.$modal
          .confirm(
            'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
          )
          .then(function () {
            sendMessage('ArchivingMultiple');
          })
          .catch(() => {});
      }
    }
  }

  //  continue
  const sendMessage = val => {
    emit('continueMessage', val);
  };

  /** 提交数据 */
  const saveData = () => {
    changeLoadingFlag(true);
    // 使用setTimeout，保证loading动画立即弹出
    setTimeout(checkAndSubmit, 100);
  };

  /** 删除数据 */
  const deleteForm = () => {
    proxy.$modal
      .confirm('Are you sure to delete all Multiple Samples?')
      .then(() => {
        const params = {
          subNo: subNo.value,
          sampleType: currSampleModel.value.currSampleType,
          single: false,
        };
        deleteSample(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess('Delete successful');
          resetForm();
        });
      })
      .catch(() => {});
  };

  function checkAndSubmit() {
    proxy.$modal.loading('Saving data, please wait...');
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  }

  function saveForm() {
    const htTbRef = proxy.$refs['sampleHtTable'];
    const { validatePromise, refreshOldData, hotInstance, allAttrDes } =
      htTbRef;
    if (!validatePromise) {
      proxy.$modal.closeLoading();
      return;
    }
    validatePromise().then(valid => {
      if (!valid) {
        changeLoadingFlag(false);
        proxy.$modal.closeLoading();
        // 校验失败
        proxy.$modal.msgWarning('Please correct all invalid cells.');
      } else {
        doSaveForm(hotInstance, refreshOldData, allAttrDes);
      }
    });
  }

  function changeLoadingFlag(flag) {
    currSampleModel.value.isRequesting = flag;
  }

  function doSaveForm(hotInstance, refreshOldData, allAttrDes) {
    resultDialogOpen.value = false;
    let datas = hotInstance.getData();
    if (isArrEmpty(datas)) {
      changeLoadingFlag(false);
      proxy.$modal.closeLoading();
      proxy.$modal.alertWarning('The submitted data cannot be empty');
      return false;
    }
    let titles = hotInstance.getColHeader();
    const currSampleType = currSampleModel.value.currSampleType;
    let paramData = {
      subNo: subNo.value,
      sampleType: currSampleType,
      stage: currStage.value,
      datas: datas,
      titles: titles,
      allAttrDes: allAttrDes(),
    };
    batchSaveSample(paramData)
      .then(response => {
        let data = response.data;
        if (data) {
          // 提交失败，展示错误信息
          resultDialogData.value = data;
          resultDialogOpen.value = true;
        } else {
          let savedTypes = currSampleModel.value.savedTypes;
          if (!savedTypes) {
            savedTypes = [];
          }
          let savedTypesSet = new Set(savedTypes);
          savedTypesSet.add(currSampleType);
          currSampleModel.value.savedTypes = [...savedTypesSet];
          refreshOldData();
          proxy.$modal.alertSuccess(
            'Sample has been saved successfully, you can continue next step now!',
          );
        }
        changeLoadingFlag(false);
      })
      .catch(() => {
        changeLoadingFlag(false);
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** 切换样本类型 */
  watch(
    () => currSampleModel.value.currSampleType,
    () => {
      loadSampleTable();
    },
  );
</script>

<style lang="scss" scoped>
  .exist-form {
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 10px;

      .el-select {
        width: 100%;
      }
    }

    :deep(.el-select__wrapper) {
      border-radius: 12px;
    }
  }

  .radio .el-form-item__label {
    font-weight: 700;
  }

  .exp-type {
    margin: 0;
  }

  //文本溢出
  .radio-content {
    text-align: justify;

    &.ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 40px;
      transition: all 1s;
    }
  }

  .expand {
    & .text {
      padding: 6px 0;
      text-align: justify;
      -webkit-line-clamp: 10;
      max-height: 120px;
      line-height: normal !important;
      height: 100% !important;

      &:after {
        display: none;
      }
    }
  }

  :deep(.el-radio__label) {
    font-size: 16px;
  }

  .type-radio:nth-child(-n + 5) {
    :deep(.el-radio .el-radio--large) {
      width: 90px;
    }
  }

  .type-radio:nth-child(n + 6) {
    :deep(.el-radio) {
      width: 230px !important;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 700;
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }

  .select-column {
    .select-col-btn {
      color: #ffffff;

      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #ffffff;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tag {
      color: #333333;
    }

    .required-tag {
      border: none;
      background-color: #e7e5a5;
    }

    .recommend-tag {
      border: none;
      background-color: #c8e6cb;
    }

    .invalid-tag {
      border: none;
      background-color: #ffbeba;
    }

    .col-radio {
      padding: 2px 6px;
      border-radius: 12px;
    }

    :deep(.el-radio .el-radio--large .el-radio__label) {
      font-size: 12px !important;
    }

    .color-key {
      font-size: 13px;
    }

    .w-85 {
      width: 85%;
    }

    .popover-btn {
      :deep(span) {
        padding-top: 1px;
      }
    }
  }

  :deep(.el-dialog__body) {
    padding: 0 15px !important;
  }

  :deep(.el-dialog__title) {
    font-weight: 600 !important;
  }
</style>
