<template>
  <div>
    <div
      v-for="(item, index) in sapTypeList"
      :key="index + item + 'a'"
      class="d-flex type-radio mt-1"
    >
      <div class="plr-20 mr-1" :class="bindClass(item)">
        <el-radio
          v-model="currSampleModel.currSampleType"
          :label="item.name"
          size="large"
          @change="changeSampleType(index, item.children)"
          >{{ item.name }}
          <el-icon v-show="item.completed" class="ml-05" color="#3A78E8">
            <Select />
          </el-icon>
        </el-radio>
      </div>
      <div
        class="text plr-20 bg-gray w-100 d-flex align-items-center"
        :class="bindClass(item)"
      >
        <div
          :ref="el => setRef(el, index)"
          class="radio-content"
          :class="{ ellipsis: !isExpanded[index] }"
        >
          <span v-html="getDescription(item)"></span>
          <div
            v-if="
              (item.children && isExpanded[index]) ||
              currSampleModel.currSampleType === item.name
            "
            class="sub-type-content"
          >
            <div
              v-for="(item2, index2) in item.children"
              :key="index2 + item2 + 'sap'"
              class="d-flex type-radio mt-1"
            >
              <div class="plr-20 mr-1" :class="bindClass(item2)">
                <el-radio
                  v-model="currSampleModel.currSampleType"
                  :label="item2.value"
                  size="large"
                  >{{ item2.name }}
                  <el-icon
                    v-show="item2.completed"
                    class="ml-05"
                    color="#3A78E8"
                  >
                    <Select />
                  </el-icon>
                </el-radio>
              </div>
              <div
                class="text plr-20 bg-gray w-100 d-flex align-items-center"
                :class="bindClass(item2)"
              >
                <div
                  :ref="el => setRef(el, subIndex(index, index2))"
                  class="radio-content"
                  :class="{ ellipsis: !isExpanded[subIndex(index, index2)] }"
                  v-html="getDescription(item2)"
                ></div>
                <span
                  v-show="showToggleButton(subIndex(index, index2))"
                  class="text-primary pointer more ml-1"
                  style="width: 100px"
                  @click="toggleText(subIndex(index, index2))"
                >
                  {{
                    isExpanded[subIndex(index, index2)]
                      ? $t(
                          'userCenter.edit.sample.sampleSingle.sampleType.collapse',
                        )
                      : $t(
                          'userCenter.edit.sample.sampleSingle.sampleType.more',
                        )
                  }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <span
          v-show="showToggleButton(index)"
          class="text-primary pointer more ml-1"
          style="width: 100px"
          @click="toggleText(index)"
        >
          {{
            isExpanded[index]
              ? $t('userCenter.edit.sample.sampleSingle.sampleType.collapse')
              : $t('userCenter.edit.sample.sampleSingle.sampleType.more')
          }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    defineProps,
    getCurrentInstance,
    onActivated,
    onDeactivated,
    ref,
    watch,
  } from 'vue';
  import { getSampleType } from '@/api/metadata/dict';
  import { isArrEmpty } from '@/utils';
  import { useI18n } from 'vue-i18n';

  let { proxy } = getCurrentInstance();
  const { locale } = useI18n();

  // 双向绑定数据
  const currSampleModel = defineModel({
    default: {
      currSampleType: '',
      isRequesting: false,
      savedTypes: [],
    },
    required: false,
  });

  const props = defineProps({
    currSubNo: {
      type: String,
      required: false,
    },
  });

  const { currSubNo } = props;

  const sapTypeList = ref([]); // 系统拥有的样本类型列表
  // 展开 收起
  const isExpanded = ref([false]);
  const refs = ref([]);

  /** 根据当前语言获取描述 */
  function getDescription(item) {
    if (locale.value === 'zh' && item.zhDescription) {
      return item.zhDescription;
    }
    return item.description;
  }

  /** 生成二级类型索引号 */
  function subIndex(pIndex, subIndex) {
    return (pIndex + 1) * 99 + subIndex;
  }

  /** 判断每个类型是否已入库 */
  function initComplete(savedTypes = []) {
    if (isArrEmpty(savedTypes)) {
      return false;
    }
    let data = sapTypeList.value;
    if (!isArrEmpty(data)) {
      data.forEach(item => {
        item.completed = savedTypes.includes(item.value);
        const children = item.children;
        if (!isArrEmpty(children)) {
          children.forEach(childItem => {
            childItem.completed = savedTypes.includes(childItem.value);
          });
        }
      });
      sapTypeList.value = data;
    }
  }

  const changeSampleType = (index, children) => {
    if (children != null) {
      isExpanded.value[index] = true;
    }
  };

  const setRef = (el, index) => {
    refs.value[index] = el;
    return el;
  };

  const showToggleButton = index => {
    const textRef = refs.value[index];
    if (textRef) {
      // 如果已经展开，则始终显示收起按钮
      if (isExpanded.value[index]) {
        return true;
      }

      // 如果未展开，则根据文字长度判断是否显示展开按钮
      const textLength = textRef.innerText.length;
      const containerWidth = textRef.parentNode.offsetWidth;
      const maxCharsPerLine = Math.floor(containerWidth / (14 * 0.45));
      const numLines = Math.ceil(textLength / maxCharsPerLine);
      return numLines > 1;
    }
    return false;
  };

  /** 展开/隐藏操作 */
  const toggleText = index => {
    isExpanded.value[index] = !isExpanded.value[index];
  };

  /** 展开指定索引项 */
  const doExpanded = index => {
    isExpanded.value[index] = true;
  };

  /** 按照样本类型父子关系，构建树结构 */
  function transformSampleType(data) {
    const transformedData = [];
    const map = new Map();
    // 第一次遍历，将每个元素的 name 和 value 添加到 map 中
    for (const item of data) {
      const { name } = item;
      const value = name;
      map.set(name, {
        name,
        value,
        description: item.description,
        zhDescription: item.zhDescription,
        children: [],
      });
    }

    // 第二次遍历，构建转换后的数据结构
    for (const item of data) {
      const { name, parentName } = item;
      const currentItem = map.get(name);

      if (parentName) {
        const parentItem = map.get(parentName);
        if (parentItem) {
          if (
            !parentItem.children.find(child => child.name === currentItem.name)
          ) {
            parentItem.children.push(currentItem);
          }
        }
      } else {
        transformedData.push(currentItem);
      }
    }

    // 添加 "Default" 对象到相应的父节点的 children 数组中
    for (const item of transformedData) {
      if (item.children.length >= 1) {
        const defaultItem = {
          name: 'Default',
          value: item.name,
          description: item.description,
          zhDescription: item.zhDescription,
        };
        item.children.unshift(defaultItem);
      }
    }

    return transformedData;
  }

  /** 加载系统所拥有的样本类型 */
  function loadSampleType() {
    currSampleModel.value.isRequesting = true;
    return getSampleType(currSubNo)
      .then(response => {
        const { types, savedMultTypes } = response.data;
        const typeData = transformSampleType(types);
        sapTypeList.value = typeData;
        if (!isArrEmpty(typeData)) {
          // 回显选中的样本类型，并展开选中项
          let typeName,
            typeIndex = [];
          if (!isArrEmpty(savedMultTypes)) {
            for (let i = 0; i < typeData.length; i++) {
              let item = typeData[i];
              if (savedMultTypes.includes(item.value)) {
                // 一级展开项
                typeIndex.push(i);
                if (!typeName) {
                  typeName = item.value;
                }
              }
              const children = item.children;
              if (!isArrEmpty(children)) {
                for (let j = 0; j < children.length; j++) {
                  let childItem = children[j];
                  if (savedMultTypes.includes(childItem.value)) {
                    // 一级展开项
                    typeIndex.push(i);
                    // 二级展开项
                    typeIndex.push(subIndex(i, j));
                    if (!typeName) {
                      typeName = childItem.value;
                    }
                    break;
                  }
                }
              }
            }
          }
          if (typeName) {
            currSampleModel.value.currSampleType = typeName;
            typeIndex = [...new Set(typeIndex)];
            typeIndex.forEach(item => {
              doExpanded(item);
            });
          } else {
            currSampleModel.value.currSampleType = typeData[0].value;
            doExpanded(0);
          }
        }
        currSampleModel.value.isRequesting = false;
      })
      .catch(() => {
        currSampleModel.value.isRequesting = false;
      });
  }

  /** radio切换时修改样式 */
  function bindClass(item) {
    if (item.completed) {
      // 以完成保存的样式
      return 'bg-complete';
    }
    if (item.value === currSampleModel.value.currSampleType) {
      return 'bg-warning';
    } else {
      return 'bg-gray';
    }
  }

  onActivated(async () => {
    await loadSampleType();
  });

  onDeactivated(() => {
    currSampleModel.value.currSampleType = null;
  });

  /** 切换已保存类型样式*/
  watch(
    () => currSampleModel.value.savedTypes,
    new_value => {
      initComplete(new_value);
    },
    { immediate: true },
  );
</script>

<style scoped lang="scss">
  .exist-form {
    width: 100%;
    flex-wrap: wrap;

    .el-form-item {
      width: calc((100% - 100px) / 3) !important;
      margin-right: 30px;

      .el-select {
        width: 100%;
      }
    }
  }

  // 文本溢出
  .radio-content {
    text-align: justify;

    &.ellipsis {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      line-height: 40px;
      transition: all 1s;
    }
  }

  .type-radio:nth-child(-n + 5) {
    :deep(.el-radio.el-radio--large) {
      width: 90px;
    }
  }

  .type-radio:nth-child(n + 6) {
    :deep(.el-radio) {
      width: 230px !important;
    }
  }

  .general-info .el-form {
    .el-form-item {
      width: 30%;

      .el-select {
        width: 100%;
      }

      .el-radio {
        width: 100%;
        margin-right: 0;

        :deep(.el-radio__label) {
          width: 100%;
        }
      }
    }
  }

  .links {
    .el-button {
      padding: 2px 8px;
      border-radius: 50%;
    }

    :deep(.el-form-item__content) {
      flex-direction: column;
      align-items: flex-start;

      & + .el-form-item__label {
        font-weight: 700;
      }
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-form-item__label) {
    font-weight: 700;
  }

  :deep(.el-radio__label) {
    font-size: 16px;
  }
</style>
