<template>
  <el-dialog
    v-model="showDialog"
    :title="
      $t('expSapAttrDialog.title', {
        baseType: $t('dataStatistic.types.' + baseType.toLowerCase()),
        currType,
      })
    "
    width="1000"
    class="dialog radius-14"
    @close="() => {}"
  >
    <el-row class="exist-form mb-1">
      <el-col :span="12" class="font-bold">
        {{ $t('expSapAttrDialog.labels.attributeName') }}
        <el-select
          v-model="selectName"
          class="radius-8"
          style="width: 250px"
          filterable
          clearable
          @change="nameChange"
        >
          <el-option
            v-for="item in attrList"
            :key="item.id"
            :label="item.attributesField"
            :value="item.attributesField"
          ></el-option>
        </el-select>
      </el-col>
      <el-col :span="12">
        <fill-tip :recommend="true"></fill-tip>
      </el-col>
    </el-row>
    <el-table
      :data="filterAttrList"
      stripe
      border
      style="width: 100%; margin-bottom: 20px"
      :header-cell-style="{
        backgroundColor: '#f2f2f2',
        color: '#333333',
        fontWeight: 700,
      }"
      height="650"
    >
      <el-table-column
        prop="attributesField"
        :label="$t('expSapAttrDialog.table.attributeName')"
        min-width="70"
      >
        <template #default="scope">
          <span v-if="scope.row.required === 'required'" class="text-danger"
            >*</span
          >
          <el-icon
            v-if="scope.row.required === 'recommend'"
            size="12"
            color="#3A78E8"
          >
            <InfoFilled />
          </el-icon>
          {{ scope.row.attributesField }}
        </template>
      </el-table-column>
      <el-table-column
        prop="description"
        :label="$t('expSapAttrDialog.table.description')"
      >
        <template #default="scope">
          <span v-html="getDescription(scope.row)"></span>
        </template>
      </el-table-column>
      <el-table-column
        prop="valueFormat"
        :label="$t('expSapAttrDialog.table.valueFormat')"
        min-width="120"
      >
        <template #default="scope">
          <el-select
            v-if="
              scope.row.attributesField === 'organism' ||
              scope.row.dataSource === 'Taxonomy'
            "
            v-model="taxonomy"
            clearable
            filterable
            remote
            reserve-keyword
            :fit-input-width="false"
            :placeholder="$t('expSapAttrDialog.placeholders.searchTaxonomy')"
            :remote-method="taxonomyQuerySearch"
            :loading="taxonomyLoading"
          >
            <el-option
              v-for="taxonomyItem in taxonomyOptions"
              :key="`taxonomyOption-${taxonomyItem.label} [${taxonomyItem.value}]`"
              :label="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
              :value="`${taxonomyItem.label} [Taxonomy ID: ${taxonomyItem.value}]`"
              :title="`Lineage: ${trimStr(taxonomyItem.title)}`"
            />

            <!--<el-option
              v-for="item in taxonomyOptions"
              :key="'taxonomyOption-' + item.value"
              :label="item.label"
              :value="item.value"
            />-->
          </el-select>
          <el-select
            v-else-if="
              scope.row.attributesField === 'platform' &&
              currType === 'Microarray'
            "
            v-model="platform"
            filterable
            remote
            reserve-keyword
            :placeholder="$t('expSapAttrDialog.placeholders.search')"
            :remote-method="platformQuerySearch"
            :loading="platformLoading"
          >
            <el-option
              v-for="item in platformOptions"
              :key="'platformOption-' + item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-else-if="
              scope.row.attributesField === 'disease_name' &&
              (currType === 'Human' ||
                currType === 'Animalia' ||
                currType === 'Cell line')
            "
            v-model="disease"
            class="w-100"
            filterable
            clearable
            remote
            reserve-keyword
            :placeholder="$t('expSapAttrDialog.placeholders.searchDisease')"
            :remote-method="diseaseQuerySearch"
            :loading="diseaseLoading"
          >
            <el-option
              v-for="item in diseaseOptions"
              :key="'diseaseOption-' + item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-else-if="
              scope.row.dataSource === 'host_biome' ||
              scope.row.dataSource === 'non_host_biome' ||
              scope.row.dataSource === 'env_biome' ||
              scope.row.dataSource === 'env_biome_water'
            "
            v-model="biome[scope.row.attributesField]"
            class="w-100"
            filterable
            clearable
            :fit-input-width="false"
            remote
            reserve-keyword
            :placeholder="$t('expSapAttrDialog.placeholders.search')"
            :remote-method="
              query => {
                querySearchBiomeAsync(query, scope.row.dataSource);
              }
            "
            :loading="biomeLoading[scope.row.dataSource]"
          >
            <el-option
              v-for="item in biomeOptions[scope.row.dataSource]"
              :key="
                scope.row.attributesField +
                scope.row.dataSource +
                'Option-' +
                item.value
              "
              :title="item.label"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-else-if="
              scope.row.dataType === 'Select' &&
              scope.row.valueRange.length > 10
            "
          >
            <el-option
              v-for="(item, idx) in scope.row.valueRange"
              :key="idx"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <div
            v-else-if="
              scope.row.dataType === 'Select' &&
              scope.row.valueRange.length <= 10
            "
          >
            <span>{{ scope.row.valueRange.join(' | ') }}</span>
          </div>
          <el-select
            v-else-if="scope.row.dataType === 'Select2'"
            filterable
            clearable
            :placeholder="$t('expSapAttrDialog.placeholders.select')"
          >
            <el-option-group
              v-for="group in scope.row.valueRange"
              :key="group.parent_name"
              :label="group.parent_name"
            >
              <el-option
                v-for="option in group.value_array"
                :key="group.parent_name + option"
                :label="option"
                :value="option"
              />
            </el-option-group>
          </el-select>
          <span v-else>{{ scope.row.valueFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup>
  import FillTip from '@/views/submit/components/FillTip.vue';
  import { computed, getCurrentInstance, ref, toRefs, watch } from 'vue';
  import {
    findBiomeLike,
    findDiseaseLike,
    findPlatformLike,
    findTaxonomyLike,
  } from '@/api/search';
  import { trimStr } from '@/utils';
  import { useI18n } from 'vue-i18n';

  const { proxy } = getCurrentInstance();
  const { locale } = useI18n();

  let props = defineProps({
    showDialog: {
      type: Boolean,
      default: false,
    },
    baseType: {
      type: String,
      default: '',
    },
    currType: {
      type: String,
      default: '',
    },
    attrList: {
      type: Object,
      default: () => {},
    },
  });

  let { baseType, currType, attrList } = toRefs(props);

  let filterAttrList = ref([]);
  let selectName = ref('');

  // 根据当前语言选择 description 或 zhDescription
  const getDescription = row => {
    if (locale.value === 'zh' && row.zhDescription) {
      return row.zhDescription;
    }
    return row.description || '';
  };

  function nameChange() {
    if (selectName.value) {
      filterAttrList.value = attrList.value.filter(
        it => it.attributesField.indexOf(selectName.value) !== -1,
      );
    } else {
      filterAttrList.value = attrList.value;
    }
  }

  watch(
    () => props.showDialog,
    () => {
      selectName.value = '';
      nameChange();
    },
  );

  const showDialog = computed({
    get() {
      return props.showDialog;
    },
    set(val) {
      proxy.$emit('update:showDialog', val);
    },
  });

  let taxonomy = ref('');
  let taxonomyOptions = ref([]);
  let taxonomyLoading = ref(false);

  /** 查询ES中的Taxonomy */
  const taxonomyQuerySearch = query => {
    taxonomyLoading.value = true;
    findTaxonomyLike({ keyword: query })
      .then(response => {
        taxonomyOptions.value = response.data;
      })
      .finally(() => {
        taxonomyLoading.value = false;
      });
  };

  let platform = ref('');
  let platformOptions = ref([]);
  let platformLoading = ref(false);

  /** 查询ES中的Platform */
  const platformQuerySearch = query => {
    platformLoading.value = true;
    findPlatformLike({ keyword: query })
      .then(response => {
        platformOptions.value = response.data;
      })
      .finally(() => {
        platformLoading.value = false;
      });
  };

  let disease = ref('');
  const diseaseOptions = ref([]);
  const diseaseLoading = ref(false);

  /** 查询ES中的Disease */
  const diseaseQuerySearch = query => {
    diseaseLoading.value = true;
    findDiseaseLike({ keyword: query })
      .then(response => {
        diseaseOptions.value = response.data;
      })
      .finally(() => {
        diseaseLoading.value = false;
      });
  };

  let biome = ref({});
  const biomeOptions = ref({});
  const biomeLoading = ref({
    host_biome: false,
    non_host_biome: false,
    env_biome: false,
    env_biome_water: false,
  });
  /** 查询ES中的Biome */
  const querySearchBiomeAsync = (query, type) => {
    biomeLoading.value[type] = true;
    findBiomeLike({ type: type, keyword: query })
      .then(response => {
        biomeOptions.value[type] = response.data;
      })
      .finally(() => {
        biomeLoading.value[type] = false;
      });
  };
</script>

<style scoped lang="scss">
  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
