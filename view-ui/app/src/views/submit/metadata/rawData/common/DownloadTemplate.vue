<template>
  <div>
    <h3 class="plr-20 d-flex align-items-center">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        width="30"
        height="30"
        viewBox="0 0 32 32"
        fill="none"
      >
        <g opacity="1" transform="translate(0 0)  rotate(0)">
          <path
            id="路径 546"
            fill-rule="evenodd"
            style="fill: #3a78e8"
            opacity="1"
            d="M30.5027,16.0022c0,8.01 -6.49,14.51 -14.5,14.51c-8.01002,0 -14.51002,-6.5 -14.51002,-14.51c0,-8.01001 6.5,-14.51001 14.51002,-14.51001c8.01,0 14.5,6.5 14.5,14.51001zM18.7176,8.97919c0,-1.3 -1.06,-2.36 -2.36,-2.36c-1.3,0 -2.36,1.06 -2.36,2.36c0,1.30001 1.06,2.36001 2.36,2.36001c1.3,0 2.36,-1.06 2.36,-2.36001zM20.5514,23.3909l-0.99,-0.37c-0.56,-0.2 -0.93,-0.73 -0.93,-1.32v-8.32h-5.98v1.06l0.99,0.36c0.55,0.21 0.92,0.73 0.92,1.32v5.58c0,0.59 -0.37,1.12 -0.92,1.32l-0.99,0.37v1.03h7.9z"
          ></path>
        </g>
      </svg>
      <span class="font-600 ml-05"
        >Using built-in table editor or Uploading a excel file to provide your
        {{ titleObj.title }} information?</span
      >
    </h3>
    <div class="d-flex">
      <div class="p-15 bg-gray mr-1">
        <span class="font-600 text-other-color"
          >Template for {{ titleObj.title }} package {{ currDataType }}; version
          2.0</span
        >
        <div class="d-flex align-items-center" style="margin-top: 0.5rem">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="22"
            height="22"
            viewBox="0 0 22 22"
            fill="none"
          >
            <g opacity="1" transform="translate(0 0)  rotate(0)">
              <path
                id="路径 432"
                fill-rule="evenodd"
                style="fill: #fe7f2b"
                opacity="1"
                d="M16.2682,11.6964zM13.8282,11.6964h2.44c0.47,0 0.71,0.56 0.4,0.91l-5.27,5.69c-0.21,0.23 -0.58,0.23 -0.79,0l-5.26996,-5.69c-0.32,-0.35 -0.07,-0.91 0.4,-0.91h2.45v-4.56999c0,-0.58 0.47,-1.06 1.06,-1.06h3.50996c0.59,0 1.07,0.48 1.07,1.06z"
              ></path>
            </g>
          </svg>
          <a
            href="javascript:void(0);"
            class="text-warning"
            @click="downloadExcel"
          >
            Download Excel
          </a>
        </div>
        <div class="d-flex align-items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            width="22"
            height="22"
            viewBox="0 0 22 22"
            fill="none"
          >
            <g opacity="1" transform="translate(0 0)  rotate(0)">
              <path
                id="路径 432"
                fill-rule="evenodd"
                style="fill: #fe7f2b"
                opacity="1"
                d="M16.2682,11.6964zM13.8282,11.6964h2.44c0.47,0 0.71,0.56 0.4,0.91l-5.27,5.69c-0.21,0.23 -0.58,0.23 -0.79,0l-5.26996,-5.69c-0.32,-0.35 -0.07,-0.91 0.4,-0.91h2.45v-4.56999c0,-0.58 0.47,-1.06 1.06,-1.06h3.50996c0.59,0 1.07,0.48 1.07,1.06z"
              ></path>
            </g>
          </svg>
          <a
            href="javascript:void(0);"
            class="text-warning"
            @click="downloadExampleExcel"
          >
            Download Example
          </a>
        </div>
        <div class="d-flex align-items-center">
          For column explanations and examples, please see the
          <a
            href="javascript:void(0);"
            class="text-warning ml-05"
            @click="
              () => {
                showSapAttrDetailDialog = true;
              }
            "
            >{{ currStage }} attributes page.</a
          >
        </div>
      </div>
      <div class="p-15 flex-2 bg-gray">
        <span class="font-600 text-main-color">
          Upload a fle using Excel that includes the attributes for each of your
          {{ titleObj.titles }}
        </span>
        <div class="d-flex align-items-center mt-1">
          <el-upload
            ref="uploadRef"
            class="upload-demo w-100 mr-1"
            :limit="1"
            accept=".xlsx"
            :headers="upload.headers"
            :action="upload.url + `?stage=${currStage}&type=` + currDataType"
            :disabled="isUploading"
            :before-upload="handleFileUploadBefore"
            :on-progress="handleFileUploadProgress"
            :on-success="handleFileSuccess"
            :auto-upload="true"
            drag
          >
            <el-icon class="el-icon--upload">
              <UploadFilled />
            </el-icon>
            <div class="el-upload__text">
              <div class="font-16">
                Drag The File Here Or
                <span class="text-primary">Click On Upload</span>
              </div>
            </div>
          </el-upload>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import {
    computed,
    defineModel,
    getCurrentInstance,
    reactive,
    toRefs,
  } from 'vue';
  import { getToken } from '@/utils/auth';
  import { trimStr } from '@/utils';

  const props = defineProps({
    currStage: {
      type: String,
      required: true,
    },
    currDataType: {
      type: String,
      required: false,
    },
    uploadUrl: {
      type: String,
      required: false,
      default: '',
    },
    downloadExcelUrl: {
      type: String,
      required: false,
      default: '',
    },
    downloadExampleExcelUrl: {
      type: String,
      required: false,
      default: '',
    },
    showSapAttrDetailDialog: {
      type: Boolean,
      required: false,
      default: false,
    },
  });
  const { uploadUrl, downloadExcelUrl, downloadExampleExcelUrl } = props;

  // 双向绑定数据,用于控制loading动画
  const isUploading = defineModel({ default: false, required: true });

  const emit = defineEmits(['changeHtTableData']);

  const { currDataType, currStage } = toRefs(props);
  let { proxy } = getCurrentInstance();
  const basApi = import.meta.env.VITE_APP_BASE_API;

  /** 根据stage获取标题 */
  const titleObj = computed(() => {
    let title, titles;
    switch (currStage.value) {
      case 'experiment':
        title = 'experiment';
        titles = 'experiments';
        break;
      case 'sample':
        title = 'sample';
        titles = 'samples';
        break;
    }
    return { title, titles };
  });

  /*** 模板导入参数 */
  const upload = reactive({
    // 是否显示弹出层（用户导入）
    // open: false,
    // 弹出层标题（用户导入）
    // title: '',
    // 设置上传的请求头部
    headers: { Authorization: 'Bearer ' + getToken() },
    // 上传的地址
    url:
      basApi + (trimStr(uploadUrl) ? uploadUrl : '/upload/common/uploadExcel'),
  });

  /** 下载模板文件 */
  function downloadExcel() {
    let url = downloadExcelUrl
      ? downloadExcelUrl
      : `/upload/common/downloadTemplate/${currStage.value}/${trimStr(
          currDataType.value,
        )}`;
    proxy.download(url);
  }

  /** 下载带样例数据的模板文件 */
  function downloadExampleExcel() {
    let url = downloadExampleExcelUrl
      ? downloadExampleExcelUrl
      : `/upload/common/downloadExampleTbl/${currStage.value}/${trimStr(
          currDataType.value,
        )}`;
    proxy.download(url);
  }

  /**文件上传中处理 */
  const handleFileUploadBefore = file => {
    return true;
  };

  const handleFileUploadProgress = (event, file, fileList) => {
    isUploading.value = true;
  };

  /** 文件上传成功处理 */
  const handleFileSuccess = (response, file, fileList) => {
    isUploading.value = false;
    proxy.$refs['uploadRef'].handleRemove(file);
    if (response.code !== 200) {
      let msg = response.msg;
      proxy.$modal.alertError(`${msg}`, true);
    } else {
      // 上传成功
      let data = response.data;
      if (data) {
        let titleAndDesc = data['title_and_desc'];
        let i = 0;
        for (let key in data) {
          if (i === 0) {
            emit('changeHtTableData', data[key], titleAndDesc);
          }
          i++;
        }
      }
    }
  };

  const showSapAttrDetailDialog = computed({
    get() {
      return props.showSapAttrDetailDialog;
    },
    set(val) {
      emit('update:showSapAttrDetailDialog', val);
    },
  });
</script>

<style scoped lang="scss"></style>
