<template>
  <el-form-item
    class="d-flex flex-column links w-100"
    label="Related Links"
    prop="relatedLinks"
  >
    <div
      v-for="(val, idx) in inputs"
      :key="'link-2' + idx"
      class="d-flex w-40"
      :class="idx === 0 ? '' : 'mt-1'"
    >
      <el-input v-model="inputs[idx]" :disabled="isDisabled" />
      <el-button
        v-if="idx === 0"
        type="primary"
        class="ml-2"
        circle
        plain
        @click="addLink"
      >
        <el-icon>
          <Plus />
        </el-icon>
      </el-button>
      <el-button
        v-if="idx !== 0"
        class="ml-2"
        circle
        type="warning"
        plain
        @click.prevent="removeLink(idx)"
      >
        <el-icon>
          <Minus />
        </el-icon>
      </el-button>
    </div>
  </el-form-item>
</template>

<script setup>
  import { ref, defineProps, watch, getCurrentInstance } from 'vue';
  const { proxy } = getCurrentInstance();

  const props = defineProps({
    relatedLinks: {
      type: Array,
      default: () => [''],
    },
    isDisabled: {
      type: <PERSON><PERSON><PERSON>,
    },
  });

  const inputs = ref(
    props.relatedLinks && props.relatedLinks.length > 0
      ? props.relatedLinks
      : [''],
  );

  const addLink = () => {
    inputs.value.push('');
  };

  const removeLink = index => {
    inputs.value.splice(index, 1);
  };

  // 监听props中的Value变化，更新inputs数组的第一个元素
  watch(
    () => props.relatedLinks,
    newValue => {
      // 使用深拷贝更新第一个输入框的值
      inputs.value[0] = JSON.parse(
        JSON.stringify(newValue && newValue.length > 0 ? newValue[0] : ''),
      );
    },
  );

  // 监听用户输入的值，动态修改父组件的值
  watch(
    inputs,
    newVal => {
      proxy.$emit('update:relatedLinks', newVal);
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>
