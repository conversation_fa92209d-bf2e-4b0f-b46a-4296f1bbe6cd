<template>
  <el-dialog
    v-model="visible"
    :close-on-click-modal="false"
    title="Delete failed. Target deletion data is already in use in the following resources."
    width="900px"
    class="preview-dialog radius-14"
  >
    <div class="ht-parent">
      <div id="errMsgLogTable"></div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          :icon="Download"
          @click="exportTableData"
          >Export</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
  import { defineProps, nextTick, reactive, ref } from 'vue';
  import Handsontable from 'handsontable';
  import { Download } from '@element-plus/icons-vue';

  const visible = ref(false);
  let hdTableObj = null;
  const props = defineProps({
    currType: {
      type: String,
      required: false,
    },
  });

  const hotColumns = reactive([
    {
      title: 'Target Deletion Data',
      type: 'text',
      data: 'target',
      cellStyle: 'custom-err-cell',
    },
    {
      title: 'Submission No',
      type: 'text',
      data: 'subNo',
      cellStyle: 'custom-err-cell',
    },
    {
      title: 'Type',
      type: 'text',
      data: 'type',
      cellStyle: 'custom-err-cell',
    },
    {
      title: 'No',
      type: 'text',
      data: 'no',
    },
    {
      title: 'Name',
      type: 'text',
      data: 'name',
    },
  ]);

  const currType = ref(props.currType);

  function openLog(data) {
    visible.value = true;
    nextTick().then(() => {
      if (hdTableObj) {
        hdTableObj.destroy();
      }
      let domObj = document.getElementById('errMsgLogTable');
      hdTableObj = new Handsontable(domObj, {
        data: data,
        colHeaders: true,
        columns: hotColumns,
        comments: true,
        currentRowClassName: 'currentRow', // 突出显示行
        currentColClassName: 'currentCol', // 突出显示列
        height: 500,
        stretchH: 'last',
        width: '100%',
        autoColumnSize: true,
        rowHeaders: true, // 显示行号
        filters: false, // 使用过滤功能
        columnSorting: true, // 开启排序
        licenseKey: 'non-commercial-and-evaluation', //去除底部非商用声明
        autoWrapRow: true,
        autoWrapCol: true,
        className: 'custom-err-table',
      });
    });
  }

  function exportTableData() {
    if (hdTableObj) {
      const exportPlugin = hdTableObj.getPlugin('exportFile');
      exportPlugin.downloadFile('csv', {
        bom: false,
        columnDelimiter: '\t',
        columnHeaders: true,
        exportHiddenColumns: true,
        exportHiddenRows: true,
        fileExtension: 'tsv',
        filename: currType.value
          ? `${currType.value}-delete-fail-log_[YYYY]-[MM]-[DD]`
          : `delete-fail-log_[YYYY]-[MM]-[DD]`,
        mimeType: 'text/tab-separated-values',
        rowDelimiter: '\r\n',
        rowHeaders: false,
      });
    }
  }

  defineExpose({
    openLog,
  });
</script>

<style lang="scss">
  .custom-err-cell {
    vertical-align: middle !important;
    color: #000 !important;
    min-width: 100px !important;
  }

  .custom-err-table thead th {
    background-color: #f56c6c1a !important;
  }
</style>
