<template>
  <div class="d-flex align-items-center ml-1">
    <el-icon size="14" color="#3A78E8"><InfoFilled /></el-icon>
    &nbsp;&nbsp;recommend information：at least&nbsp;
    <strong>{{ recommendNum === 1 ? 'one' : recommendNum }}</strong
    >&nbsp; of those fields is mandatory. {{ recommendTip }}
  </div>
</template>

<script setup>
  defineProps({
    recommendNum: {
      type: Number,
      required: true,
      default: 0,
    },
    recommendTip: {
      type: String,
      required: false,
    },
  });
</script>
