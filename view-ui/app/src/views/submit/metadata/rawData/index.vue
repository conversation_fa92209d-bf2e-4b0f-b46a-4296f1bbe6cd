<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="Metadata" />
      <ChooseData :active-menu="activeMenu" class="hidden-xs-only" />
      <div class="d-flex submitData">
        <div class="mr-1">
          <div
            class="item bubble-right"
            :class="{ active: isActive === 'Submitter' }"
            @click="
              isActive = 'Submitter';
              activeMenu = 'metadata';
            "
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">Submitter</span>
          </div>
          <el-divider />
          <div
            class="bubble-right text-primary font-600"
            :class="{ active: isActive === 'Project' }"
            @click="
              isActive = 'Project';
              activeMenu = 'metadata';
            "
          >
            Project
          </div>
          <el-divider />
          <div
            class="bubble-right"
            :class="{ active: isActive.includes('Exp') }"
            @click="activeMenu = 'metadata'"
          >
            <span class="text-primary font-600">Experiment</span>
            <div class="ml-05 d-flex flex-column">
              <span
                class="before-circle"
                :class="{ active: isActive === 'ExpMultiple' }"
                @click="isActive = 'ExpMultiple'"
                >Multiple</span
              >
              <span
                class="before-circle"
                :class="{ active: isActive === 'ExpSingle' }"
                @click="isActive = 'ExpSingle'"
                >Single</span
              >
            </div>
          </div>
          <el-divider />
          <div
            class="bubble-right"
            :class="{ active: isActive.includes('Sample') }"
            @click="activeMenu = 'metadata'"
          >
            <span class="text-primary font-600">Sample</span>
            <div class="ml-05 d-flex flex-column">
              <span
                class="before-circle"
                :class="{ active: isActive === 'SampleMultiple' }"
                @click="isActive = 'SampleMultiple'"
                >Multiple</span
              >
              <span
                class="before-circle"
                :class="{ active: isActive === 'SampleSingle' }"
                @click="isActive = 'SampleSingle'"
                >Single</span
              >
            </div>
          </div>
          <el-divider />

          <div
            class="bubble-right"
            :class="{ active: isActive.includes('Archiving') }"
            @click="activeMenu = 'archiving'"
          >
            <span class="text-danger">*</span>
            <span class="text-primary font-600">Archiving</span>
            <div class="ml-05 d-flex flex-column">
              <span
                class="before-circle"
                :class="{ active: isActive === 'ArchivingMultiple' }"
                @click="isActive = 'ArchivingMultiple'"
                >Multiple</span
              >
              <span
                class="before-circle"
                :class="{ active: isActive === 'ArchivingSingle' }"
                @click="isActive = 'ArchivingSingle'"
                >Single</span
              >
            </div>
          </div>
        </div>
        <transition name="animation" mode="out-in">
          <keep-alive>
            <component
              :is="tabs[isActive]"
              @continue-message="handleMessage"
            ></component>
          </keep-alive>
        </transition>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import ChooseData from '../../components/chooseData.vue';
  import { ref } from 'vue';
  import Submitter from '@/views/submit/metadata/rawData/submitter.vue';
  import Project from '@/views/submit/metadata/rawData/project.vue';
  import ExpSingle from '@/views/submit/metadata/rawData/experiment/expSingle.vue';
  import ExpMultiple from '@/views/submit/metadata/rawData/experiment/expMultiple.vue';
  import SampleSingle from '@/views/submit/metadata/rawData/sample/sampleSingle.vue';
  import ArchivingSingle from '@/views/submit/metadata/rawData/archivingSingle.vue';
  import ArchivingMultiple from '@/views/submit/metadata/rawData/archivingMultiple.vue';
  import SampleMultiple from '@/views/submit/metadata/rawData/sample/sampleMultiple.vue';

  const isActive = ref('Submitter');
  const activeMenu = ref('metadata');
  const tabs = {
    Submitter,
    Project,
    ExpSingle,
    ExpMultiple,
    SampleSingle,
    ArchivingSingle,
    ArchivingMultiple,
    SampleMultiple,
  };

  const handleMessage = val => {
    isActive.value = val;
  };
</script>

<style lang="scss" scoped>
  .animation-enter-from,
  .animation-leave-to {
    transform: translateX(20px);
    opacity: 0;
  }

  .animation-enter-to,
  .animation-leave-from {
    opacity: 1;
  }

  .animation-enter-active {
    transition: all 0.7s ease;
  }

  .animation-leave-active {
    transition: all 0.3s cubic-bezier(1, 0.6, 0.6, 1);
  }

  .submitData {
    .before-circle {
      &.active {
        background-color: #fff;
        border-radius: 14px;
        color: #3a78e8;
        /* padding: 0 20px; */
        border: 1px solid #3a78e8;
        padding: 0 15px;
        text-align: center;

        &:before {
          display: none;
          background-color: #3a78e8 !important;
        }
      }

      &:before {
        background-color: #999999 !important;
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;
      }
    }
  }

  .fade-enter-active,
  .fade-leave-active {
    transition:
      opacity 0.3s,
      transform 0.3s;
  }

  .fade-enter {
    opacity: 0;
    //transform: translateX(10px);
  }

  animate__fadeInLeft .fade-leave-to {
    opacity: 0;
    //transform: translateX(-10px);
  }

  @media (max-width: 767px) {
    .submitData {
      margin-top: 1rem;
      flex-direction: column;
    }
  }
</style>
