<template>
  <div class="archiving w-100">
    <div class="card p-20">
      <h3>Select Data</h3>
      <el-tabs v-model="activeName" type="card" class="demo-tabs">
        <el-tab-pane
          label="Unarchived/Archiving Data"
          name="Unarchived/Archiving Data"
        >
          <pre-archived-table
            ref="unarchivedTableRef"
            v-model:selectedRows="unarchivedSelectRows"
            table-type="analysisData"
            :show-operate-col="false"
            @cancel-archive="handleCancelArchive"
          ></pre-archived-table>
        </el-tab-pane>
        <el-tab-pane
          label="Archived Analysis Data"
          name="Archived Analysis Data"
        >
          <archived-analysis-data-table
            ref="archivedAnalysisDataTable"
            v-model:selected-rows="archivedAnalysisSelectRows"
            :show-operate-col="false"
          ></archived-analysis-data-table>
        </el-tab-pane>
        <el-tab-pane label="Archived Raw Data">
          <archived-raw-data-table
            ref="archivedRawDataTable"
            v-model:selected-rows="archivedRawDataSelectRows"
            :show-operate-col="false"
          ></archived-raw-data-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="card card-container mt-1 pt-0">
      <FillTip></FillTip>
      <h3 class="plr-20">Archiving</h3>
      <div class="select-item bg-gray d-flex mb-1 p-20">
        <div class="d-flex align-items-center items-selected">
          <el-icon color="#3A78E8" size="20">
            <WarningFilled />
          </el-icon>
          <span class="text-primary ml-1">{{
            unarchivedSelectRows.length +
            archivedAnalysisSelectRows.length +
            archivedRawDataSelectRows.length
          }}</span>
          <span class="ml-05">items are selected</span>
        </div>
        <div class="tag d-flex align-items-center flex-wrap">
          <span
            class="text-primary pointer font-600 mr-1"
            @click="clearAllSelection"
            >Clear</span
          >
          <el-tag
            v-for="(it, index) in unarchivedSelectRows"
            :key="'unarchived-selected-' + index"
            class="mr-05 mx-1"
            effect="light"
            size="large"
            closable
            round
            @close="removeUnarchived(it, index)"
            >{{ it.datNo }}
          </el-tag>
          <el-tag
            v-for="(it, index) in archivedAnalysisSelectRows"
            :key="'archived-analysis-selected-' + index"
            class="mr-05 mx-1"
            effect="light"
            size="large"
            closable
            round
            @close="removeArchivedAnalysisData(it, index)"
            >{{ it.datNo }}
          </el-tag>
          <el-tag
            v-for="(it, index) in archivedRawDataSelectRows"
            :key="'archived-rawdata-selected-' + index"
            class="mr-05 mx-1"
            effect="light"
            size="large"
            closable
            round
            @close="removeArchivedRawData(it, index)"
            >{{ it.datNo }}
          </el-tag>
        </div>
      </div>
      <div class="category-title font-600 text-main-color">
        Select Archiving Analysis
      </div>
      <div class="plr-20 bg-gray mt-1">
        <el-form
          label-position="top"
          label-width="100px"
          :model="submitForm"
          :inline="true"
          style="padding-top: 8px"
          class="flex-column"
        >
          <el-form-item required label="Analysis">
            <el-select
              v-model="submitForm.analysisNo"
              :teleported="false"
              filterable
              placeholder="Please select analysis"
              clearable
              remote
              reverse-keyword
              :loading="loading"
              :remote-method="remoteSearch"
              @visible-change="handleVisibleChange"
            >
              <el-option
                v-for="(it, index) in analysisOptions"
                :key="'analysis-option-' + index"
                :label="it.label"
                :value="it.value"
                :disabled="it.value === 'No data'"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div class="text-align-right mt-2 pr-20">
        <div class="text-align-right mt-2 pr-20">
          <el-button
            type="primary"
            class="btn-primary btn btn-s"
            round
            @click="saveData"
            >Check & Save
          </el-button>
          <el-button
            type="primary"
            class="btn-primary btn btn-s"
            round
            @click="submitSubmission"
            >Submit
          </el-button>
          <el-button class="btn-primary btn btn-round" round @click="resetForm"
            >Reset
          </el-button>
        </div>
      </div>
    </div>
    <ArchivingDialog ref="archivingDialogRef"></ArchivingDialog>
  </div>
</template>

<script setup>
  import { getCurrentInstance, reactive, ref } from 'vue';
  import { isStrBlank } from '@/utils';
  import { cancelArchiving, saveAnalysisArchive } from '@/api/metadata/archive';
  import { storeToRefs } from 'pinia';
  import { getAnalysisOptionsByPage } from '@/api/metadata/analysis';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import useSubmissionStore from '@/store/modules/metadata';
  import PreArchivedTable from '@/views/submit/rawdata/common/PreArchivedTable.vue';
  import ArchivedAnalysisDataTable from '@/views/submit/rawdata/common/ArchivedAnalysisDataTable.vue';
  import ArchivedRawDataTable from '@/views/submit/rawdata/common/ArchivedRawDataTable.vue';
  import ArchivingDialog from '@/views/submit/components/ArchivingDialog.vue';
  import FillTip from '@/views/submit/components/FillTip.vue';

  let { proxy } = getCurrentInstance();
  const submissionStore = useSubmissionStore();

  const { subNo } = storeToRefs(submissionStore);
  const activeName = ref('Unarchived/Archiving Data');
  let unarchivedSelectRows = ref([]);
  let archivedAnalysisSelectRows = ref([]);
  let archivedRawDataSelectRows = ref([]);
  let analysisOptions = ref([]);

  /** 选项查询条件 */
  let query = reactive({
    pageNum: 1,
    pageSize: 100,
    name: '',
  });

  /** 加载控制开关 */
  let loading = ref(false);

  function remoteSearch(keyword) {
    loading.value = true;
    // 重置检索条件
    query.name = keyword;
    getAnalysisOptionsByPage(query)
      .then(response => {
        analysisOptions.value = response.rows;
        if (analysisOptions.value.length === 0) {
          analysisOptions.value.push({
            label: 'No data',
            value: 'No data',
          });
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 暂时废弃 */
  function handleVisibleChange() {}

  /** 删除选择的未归档的 */
  function removeUnarchived(row, index) {
    unarchivedSelectRows.value.splice(index, 1);
    proxy.$refs['unarchivedTableRef'].removeSelection(row);
  }

  /** 删除选择的归档的analysisData */
  function removeArchivedAnalysisData(row, index) {
    archivedAnalysisSelectRows.value.splice(index, 1);
    proxy.$refs['archivedAnalysisDataTable'].removeSelection(row);
  }

  /** 删除选择的归档的analysisData */
  function removeArchivedRawData(row, index) {
    archivedRawDataSelectRows.value.splice(index, 1);
    proxy.$refs['archivedRawDataTable'].removeSelection(row);
  }

  /** 删除所有选中的 */
  function clearAllSelection() {
    unarchivedSelectRows.value.forEach(item => {
      proxy.$refs['unarchivedTableRef'].removeSelection(item);
    });
    unarchivedSelectRows.value = [];
    archivedAnalysisSelectRows.value.forEach(item => {
      proxy.$refs['archivedAnalysisDataTable'].removeSelection(item);
    });
    archivedAnalysisSelectRows.value = [];
    archivedRawDataSelectRows.value.forEach(item => {
      proxy.$refs['archivedRawDataTable'].removeSelection(item);
    });
    archivedRawDataSelectRows.value = [];
  }

  const submitForm = reactive({
    analysisNo: '',
    datNos: [],
  });

  /** 保存数据 */
  function saveData() {
    if (
      unarchivedSelectRows.value.length === 0 &&
      archivedAnalysisSelectRows.value.length === 0 &&
      archivedRawDataSelectRows.value.length === 0
    ) {
      proxy.$modal.alertError(
        'Please select the data that needs to be archived',
      );
      return;
    }
    if (isStrBlank(submitForm.analysisNo)) {
      proxy.$modal.alertError('Please select Analysis');
      return;
    }
    submitForm.datNos = [];
    submitForm.datNos.push(...unarchivedSelectRows.value.map(it => it.datNo));
    submitForm.datNos.push(
      ...archivedAnalysisSelectRows.value.map(it => it.datNo),
    );
    submitForm.datNos.push(
      ...archivedRawDataSelectRows.value.map(it => it.datNo),
    );
    proxy.$modal.loading('Saving data, please wait...');
    if (!subNo.value) {
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  }

  function submitSubmission() {
    if (isStrBlank(subNo.value)) {
      proxy.$modal.alertError(
        "The submission information has not been completed. We recommend that you follow the navigation on the left sidebar of the page, starting with the submitter's details, and fill in the information step by step, saving as you go. ",
      );
      return;
    }
    proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
  }

  function saveForm() {
    submitForm.subNo = subNo.value;
    saveAnalysisArchive(submitForm)
      .then(() => {
        proxy.$refs['unarchivedTableRef'].getDataList();
        proxy.$refs['archivedAnalysisDataTable'].getDataList();
        proxy.$refs['archivedRawDataTable'].getDataList();
        proxy.$refs['archivingDialogRef'].showDialog(subNo.value);
        resetForm();
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  /** 重置表格 */
  function resetForm() {
    submitForm.analysisNo = '';
    submitForm.datNos = [];
    clearAllSelection();
  }

  /** 取消归档 */
  function handleCancelArchive() {
    let rows = unarchivedSelectRows.value.filter(it => !isStrBlank(it.analNo));
    if (rows.length === 0) {
      proxy.$modal.alertError(
        'Please select the data that needs to be archived',
      );
    }
    let data = {
      subNo: subNo.value,
      dataNos: rows.map(it => it.datNo),
      type: 'analysisData',
    };
    cancelArchiving(data).then(() => {
      proxy.$refs['unarchivedTableRef'].getDataList();
      proxy.$refs['archivedAnalysisDataTable'].getDataList();
      proxy.$refs['archivedRawDataTable'].getDataList();
      clearAllSelection();
    });
  }
</script>

<style lang="scss" scoped>
  .archiving {
    :deep(.el-tabs--card > .el-tabs__header) {
      border-bottom: none;

      .el-tabs__nav {
        border: none;
      }

      .el-tabs__item {
        margin-left: 3px;
        border-radius: 4px;
        background-color: #edf4fe;
      }

      .el-tabs__item.is-active {
        background-color: #3a78e8;
        color: #fff;
        border-radius: 4px;
      }
    }

    .archived-data,
    .unarchived-data {
      :deep(.el-input__wrapper) {
        border-radius: 12px;
      }

      .date-to {
        margin: 0 0.5rem;
      }

      :deep(.el-table__header .cell) {
        font-weight: 600;
      }

      :deep(.el-radio__input.is-checked) {
        .el-radio__inner {
          border: none;
          background-color: #fe7f2b;
        }

        & + .el-radio__label {
          color: #333333;
        }
      }

      :deep(.el-table td.el-table__cell div) {
        display: flex;
        align-items: center;
      }
    }

    //tag
    .select-item {
      & span {
        font-weight: 600;
        font-size: 16px;
      }
    }

    .newrun {
      transition: all 0.3s linear;

      .links {
        :deep(.el-form-item__label) {
          font-weight: 700;
        }

        :deep(.el-form-item__content) {
          flex-direction: column;
          align-items: flex-start;

          & + .el-form-item__label {
            font-weight: 700;
          }
        }
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;

        .el-select {
          width: 100%;
        }

        .el-radio {
          width: 100%;
          margin-right: 0;

          :deep(.el-radio__label) {
            width: 100%;
          }
        }
      }

      :deep(.el-form-item__label) {
        font-weight: 700;
      }
    }

    :deep(.dialog .el-dialog__body) {
      text-align: center;
      padding: 10px 15px 0 15px;

      a:hover {
        color: #3a78e8;
      }
    }
  }
</style>
