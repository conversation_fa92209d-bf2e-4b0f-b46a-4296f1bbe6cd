<template>
  <div class="w-100">
    <div v-loading="isRequesting" class="card card-container">
      <DownloadTemplate
        :key="'downloadTemp' + currStage"
        v-model="isRequesting"
        :curr-stage="currStage"
        :curr-data-type="dataType"
        @change-ht-table-data="changeHtTableData"
      >
      </DownloadTemplate>
      <ht-table
        :key="'anal-ht-table-' + htTableKey"
        ref="analHtTable"
        v-loading="loading"
        :hot-table-data="hotTableData"
        :hot-columns="hotColumns"
      ></ht-table>
      <result-log
        v-if="resultDialogOpen"
        :log-data="resultDialogData"
        curr-exp-type="Analysis"
      >
      </result-log>
      <div class="text-align-right mt-2 pr-20">
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="continueNext"
          >Continue
        </el-button>
        <el-button
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          :disabled="isRequesting"
          @click="saveData"
          >Check & Save
        </el-button>
        <el-button class="btn-primary btn btn-round" round @click="resetForm"
          >Reset
        </el-button>
        <el-button
          :disabled="!subNo"
          type="danger"
          class="btn"
          plain
          round
          @click="deleteForm"
          >Delete
        </el-button>
      </div>
    </div>
    <DeleteLog ref="deleteLog" curr-type="Multiple Analysis"></DeleteLog>
  </div>
</template>

<script setup>
  import {
    defineEmits,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import { storeToRefs } from 'pinia';
  import useSubmissionStore from '@/store/modules/metadata';
  import { BusEnum } from '@/utils/enums';
  import bus from '@/utils/bus';
  import ResultLog from '@/views/submit/metadata/rawData/common/ResultLog.vue';
  import {
    batchSaveAnalysis,
    deleteAnalysis,
    getMultiAnalysisBySubNo,
  } from '@/api/metadata/analysis';
  import HtTable from '@/components/HtTable/index.vue';
  import { getDicts } from '@/api/system/dict/data';
  import {
    myDropdownValidator,
    myNumericValidator,
    requiredValidator,
  } from '@/utils/ht/validator';
  import DownloadTemplate from '@/views/submit/metadata/rawData/common/DownloadTemplate.vue';
  import DeleteLog from '@/views/submit/metadata/rawData/common/DeleteLog.vue';

  let loading = ref(false);
  let { proxy } = getCurrentInstance();
  // 提交者信息
  const submissionStore = useSubmissionStore();
  // 提交编号
  const { subNo } = storeToRefs(submissionStore);
  const emit = defineEmits(['continueMessage']);
  let htTableKey = ref(0);

  /*********************************************************/
  const isRequesting = ref(false);
  // 当前阶段
  const currStage = ref('analysis');
  let dataType = ref('analysis');

  /**********************************************************/
  function continueNext() {
    let htTbRef = proxy.$refs['analHtTable'];
    if (htTbRef.validateChanged()) {
      sendContinueMessage();
    } else {
      proxy.$modal
        .confirm(
          'The data you filled in has not been saved. Are you sure you want to skip the current step ?',
        )
        .then(function () {
          sendContinueMessage();
        })
        .catch(() => {});
    }
  }

  function sendContinueMessage() {
    emit('continueMessage', 'ArchivingMultiple');
  }

  /*********************************************************/
  let node_analysis_type = reactive([]);
  let hotTableData = reactive([]);
  let hotColumns = reactive([]);

  onMounted(() => {
    let promise1 = getDicts('node_analysis_type').then(response => {
      node_analysis_type = response.data.map(p => ({
        label: p.dictLabel,
        value: p.dictValue,
        elTagType: p.listClass,
        elTagClass: p.cssClass,
      }));
    });

    let promise2;
    if (subNo.value) {
      loading.value = true;
      promise2 = getMultiAnalysisBySubNo(subNo.value)
        .then(response => {
          // 获取数据
          hotTableData = response.data;
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      promise2 = Promise.resolve();
    }

    Promise.all([promise1, promise2])
      .then(() => {
        // 初始化列信息
        initHtColumns();
        // 重新渲染组件
        htTableKey.value++;
      })
      .catch(() => {});
  });

  function initHtColumns() {
    let analysisType = node_analysis_type.map(it => it.value);
    analysisType.push('Other');
    hotColumns = [
      {
        title: 'analysis_id',
        type: 'text',
        isRequired: 'optional',
        data: 'analysis_id',
        des: 'For newly created analyses, the analysis ID can be left blank. When modifying an analysis that has already been assigned an analysis ID, the analysis ID must be filled in. Once the analysis ID is assigned, it cannot be changed.',
        show: true,
      },
      {
        title: 'analysis_name',
        type: 'text',
        data: 'analysis_name',
        isRequired: 'required',
        validator: requiredValidator,
        des: 'Fill in the unique name of the analysis you plan to create or the name of an analysis that has been successfully created and assigned an analysis ID. If the analysis ID and analysis name do not match, it is considered an update to the analysis name.',
        show: true,
      },
      {
        title: 'description',
        type: 'text',
        data: 'description',
        isRequired: 'optional',
        des: 'Description of the analysis.',
        show: true,
      },
      {
        title: 'analysis_type',
        data: 'analysis_type',
        isRequired: 'required',
        type: 'dropdown',
        source: analysisType,
        validator: myDropdownValidator,
        className: 'htCenter',
        des: 'The type of analysis. If the analysis type you are using is not in the selection list, you can select "Other" and fill in the analysis type in other_analysis_type column.',
        show: true,
      },
      {
        title: 'other_analysis_type',
        data: 'other_analysis_type',
        isRequired: 'optional',
        des: 'Fill in the analysis type name in this column if the analysis_type is "Other".',
        type: 'text',
        show: true,
      },
      {
        title: 'index',
        type: 'numeric',
        data: 'index',
        isRequired: 'optional',
        des: 'Fill in the serial number of the analysis steps. e.g. If the analysis process has three steps, the index should be 1, 2, 3.',
        validator: myNumericValidator,
        className: 'htCenter',
        show: true,
      },
      {
        title: 'program',
        type: 'text',
        data: 'program',
        isRequired: 'optional',
        des: 'Tools used in each analysis step.',
        allowInvalid: true,
        show: true,
      },
      {
        title: 'link',
        type: 'text',
        data: 'link',
        des: 'The related link of the tool that is used in each analysis step.',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'version',
        type: 'text',
        data: 'version',
        isRequired: 'optional',
        des: 'The version of the tool that is used in each analysis step.',
        show: true,
      },
      {
        title: 'note',
        type: 'text',
        data: 'note',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'output_file',
        type: 'text',
        data: 'output_file',
        isRequired: 'optional',
        show: true,
      },
      {
        title: 'target_project',
        type: 'text',
        data: 'target_project',
        des: 'Filling the existing project id, if the analysis is related to a project. Separate multiple associations with semicolons, e.g. OEP000001;OEP000002;OEP000003.',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_experiment',
        type: 'text',
        data: 'target_experiment',
        des: 'Filling the existing experiment id, if the analysis is related to an experiment. Separate multiple associations with semicolons, e.g. OEX000001;OEX000002;OEX000003.',
        isRequired: 'recommend',
        show: true,
      },
      {
        title: 'target_sample',
        type: 'text',
        data: 'target_sample',
        isRequired: 'recommend',
        des: 'Filling the existing sample id, if the analysis is related to a sample. Separate multiple associations with semicolons, e.g. OES000001;OES000002;OES000003.',
        show: true,
      },
      {
        title: 'target_analysis',
        type: 'text',
        data: 'target_analysis',
        isRequired: 'recommend',
        des: 'Filling the associated analysis id, if the analysis is related to another analysis. Separate multiple associations with semicolons, e.g. OEZ000001;OEZ000002;OEZ000003.',
        show: true,
      },
      {
        title: 'target_run',
        type: 'text',
        data: 'target_run',
        isRequired: 'recommend',
        des: 'Filling the existing run id, if the analysis is related to a run. Separate multiple associations with semicolons, e.g. OER000001;OER000002;OER000003.',
        show: true,
      },
      {
        title: 'target_data',
        type: 'text',
        data: 'target_data',
        isRequired: 'recommend',
        des: 'Filling the existing data id, if the analysis is related to a data. Separate multiple associations with semicolons, e.g. OED000001;OED000002;OED000003.',
        show: true,
      },
      {
        title: 'target_other_name',
        type: 'text',
        data: 'target_other_name',
        isRequired: 'recommend',
        des: 'Filling the name of target, if the analysis is related to a target from other sources. Separate multiple associations with semicolons, e.g. SRA;GSA.',
        show: true,
      },
      {
        title: 'target_other_link',
        type: 'text',
        data: 'target_other_link',
        isRequired: 'recommend',
        des: 'Filling the link of target, if the analysis is related to a target from other sources. Separate multiple associations with semicolons.',
        show: true,
      },
    ];
  }

  /** 上传excel成功后初始化ht表格 */
  function changeHtTableData(excelData) {
    proxy.$refs['analHtTable'].changeTbData(excelData);
  }

  function changeLoadingFlag(flag) {
    isRequesting.value = flag;
  }

  /*********************************************************/
  const resultDialogOpen = ref(false);
  const resultDialogData = ref([]);
  /** 提交数据 */
  const saveData = () => {
    changeLoadingFlag(true);
    proxy.$modal.loading('Saving data, please wait...');
    if (!subNo.value) {
      // 如果没有subNo代表用户没有保存过Submitter数据，自动保存
      bus.emit(BusEnum.SUBMIT_SUBMITTER, saveForm);
    } else {
      saveForm();
    }
  };

  function saveForm() {
    resultDialogOpen.value = false;
    let htTbRef = proxy.$refs['analHtTable'];
    const { validatePromise, refreshOldData, hotInstance } = htTbRef;
    if (!validatePromise || !refreshOldData) {
      proxy.$modal.closeLoading();
      return;
    }
    validatePromise().then(valid => {
      if (!valid) {
        changeLoadingFlag(false);
        proxy.$modal.closeLoading();
        // 校验失败
        proxy.$modal.msgWarning('Please correct all invalid cells.');
      } else {
        let data = hotInstance.getData();
        let titles = hotInstance.getColHeader();
        let paramData = {
          subNo: subNo.value,
          stage: currStage.value,
          datas: data,
          titles: titles,
        };
        batchSaveAnalysis(paramData)
          .then(response => {
            let data = response.data;
            if (data) {
              // 提交失败，展示错误信息
              resultDialogData.value = data;
              resultDialogOpen.value = true;
            } else {
              refreshOldData();
              proxy.$modal.alertSuccess(
                'Analysis has been saved successfully, you can continue next step now!',
              );
            }
            changeLoadingFlag(false);
          })
          .catch(() => {
            changeLoadingFlag(false);
          })
          .finally(() => {
            proxy.$modal.closeLoading();
          });
      }
    });
  }

  function resetForm() {
    hotTableData.length = 0;
    proxy.$refs['analHtTable'].initHandsontable();
  }

  function deleteForm() {
    proxy.$modal
      .confirm('Are you sure you want to delete all Multiple Analyses?')
      .then(() => {
        let params = {
          subNo: subNo.value,
          single: false,
        };
        deleteAnalysis(params).then(response => {
          if (response.data) {
            proxy.$refs['deleteLog'].openLog(response.data);
            return;
          }
          proxy.$modal.msgSuccess('Delete successful');
          resetForm();
        });
      })
      .catch(() => {});
  }
</script>

<style lang="scss" scoped>
  .exp-type {
    margin: 0;
  }

  .select-column {
    .select-col-btn {
      color: #ffffff;

      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #ffffff;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tag {
      color: #333333;
    }

    .required-tag {
      border: none;
      background-color: #e7e5a5;
    }

    .recommend-tag {
      border: none;
      background-color: #c8e6cb;
    }

    .invalid-tag {
      border: none;
      background-color: #ffbeba;
    }

    .col-radio {
      padding: 2px 6px;
      border-radius: 12px;
    }

    :deep(.el-radio.el-radio--large .el-radio__label) {
      font-size: 12px !important;
    }

    .color-key {
      font-size: 13px;
    }

    .w-85 {
      width: 85%;
    }

    .popover-btn {
      :deep(span) {
        padding-top: 1px;
      }
    }
  }

  .el-checkbox-group {
    :deep(.el-checkbox) {
      width: 25%;
      margin-right: 65px;
      //&:nth-child(odd) {
      //  margin-right: 70px;
      //}
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }
</style>
