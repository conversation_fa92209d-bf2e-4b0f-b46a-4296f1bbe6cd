<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb :bread-item="breadcrumb" />
      <el-row :gutter="20" class="mt-1">
        <el-col :span="4" :xs="24" :md="4" class="mb-1">
          <div class="submitData">
            <div
              v-show="shareStatus === ConfigEnum.Enable"
              class="bubble-right"
              :class="{ active: isActive.includes('Share') }"
            >
              <span class="text-primary font-600">{{
                $t('userCenter.userLeft.sections.share')
              }}</span>
              <div class="ml-05 d-flex flex-column">
                <span
                  class="before-circle"
                  :class="{ active: isActive === 'MyShare' }"
                  @click="changeTab('MyShare')"
                >
                  <router-link to="/userCenter/MyShare">
                    {{ $t('userCenter.userLeft.links.myShares') }}
                  </router-link>
                </span>
                <span
                  class="before-circle"
                  :class="{ active: isActive === 'ShareFromOthers' }"
                  @click="changeTab('ShareFromOthers')"
                >
                  <router-link to="/userCenter/ShareFromOthers"
                    >{{ $t('userCenter.userLeft.links.sharesFromOthers') }}
                  </router-link>
                </span>
              </div>
            </div>
            <el-divider v-show="shareStatus === ConfigEnum.Enable" />
            <div
              v-show="requestStatus === ConfigEnum.Enable"
              class="bubble-right"
              :class="{ active: isActive.includes('Request') }"
            >
              <span class="text-primary font-600">{{
                $t('userCenter.userLeft.sections.requests')
              }}</span>
              <div class="ml-05 d-flex flex-column">
                <span
                  class="before-circle"
                  :class="{ active: isActive === 'MyRequest' }"
                  @click="changeTab('MyRequest')"
                >
                  <router-link to="/userCenter/MyRequest">
                    {{ $t('userCenter.userLeft.links.myRequests') }}
                  </router-link>
                </span>
                <span
                  class="before-circle"
                  :class="{ active: isActive === 'RequestFromOthers' }"
                  @click="changeTab('RequestFromOthers')"
                >
                  <router-link to="/userCenter/RequestFromOthers">
                    {{ $t('userCenter.userLeft.links.requestsFromOthers') }}
                  </router-link>
                </span>
              </div>
            </div>
            <el-divider v-show="reviewStatus === ConfigEnum.Enable" />

            <div
              v-show="reviewStatus === ConfigEnum.Enable"
              class="bubble-right"
              :class="{ active: isActive.includes('Review') }"
            >
              <span class="text-primary font-600">{{
                $t('userCenter.userLeft.sections.reviews')
              }}</span>
              <div class="ml-05 d-flex flex-column">
                <span
                  class="before-circle"
                  :class="{ active: isActive === 'MyReview' }"
                  @click="changeTab('MyReview')"
                >
                  <router-link to="/userCenter/MyReview"
                    >{{ $t('userCenter.userLeft.links.myReviews') }}
                  </router-link></span
                >
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="20" :xs="24" :md="20">
          <div class="card">
            <component
              :is="tabs[activeTab]"
              :key="'activeTab-component-' + key"
              :my-data="myData"
            ></component>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import MyShare from '@/views/userCenter/personal/myShare.vue';
  import MyRequest from '@/views/userCenter/personal/myRequest.vue';
  import MyReview from '@/views/userCenter/personal/myReview.vue';

  import { getCurrentInstance, onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { ConfigEnum } from '@/utils/enums';
  import { useI18n } from 'vue-i18n';

  const route = useRoute();
  const { proxy } = getCurrentInstance();
  let { locale } = useI18n();

  const requestStatus = proxy.getConfigVal(ConfigEnum.Request_Status);
  const shareStatus = proxy.getConfigVal(ConfigEnum.Share_Status);
  const reviewStatus = proxy.getConfigVal(ConfigEnum.Review_Status);

  const isActive = ref('');
  const activeTab = ref('');
  const breadcrumb = ref('');
  const myData = ref(true);
  const key = ref(1);

  const tabs = {
    MyReview,
    MyRequest,
    MyShare,
  };

  onMounted(() => {
    changeTab(route.params.personal);
  });

  function changeTab(tab) {
    myData.value = true;
    activeTab.value = tab;
    isActive.value = activeTab.value;
    if (activeTab.value === 'MyReview') {
      breadcrumb.value =
        locale.value === 'zh'
          ? proxy.$t('userCenter.userLeft.links.myReviews')
          : activeTab.value;
    } else if (activeTab.value === 'MyRequest') {
      breadcrumb.value =
        locale.value === 'zh'
          ? proxy.$t('userCenter.userLeft.links.myRequests')
          : activeTab.value;
    } else if (activeTab.value === 'MyShare') {
      breadcrumb.value =
        locale.value === 'zh'
          ? proxy.$t('userCenter.userLeft.links.myShares')
          : activeTab.value;
    } else if (activeTab.value === 'RequestFromOthers') {
      breadcrumb.value =
        locale.value === 'zh'
          ? proxy.$t('userCenter.userLeft.links.requestsFromOthers')
          : activeTab.value;
    } else if (activeTab.value === 'ShareFromOthers') {
      breadcrumb.value =
        locale.value === 'zh'
          ? proxy.$t('userCenter.userLeft.links.sharesFromOthers')
          : activeTab.value;
    }

    if (activeTab.value === 'RequestFromOthers') {
      activeTab.value = 'MyRequest';
      myData.value = false;
    }

    if (activeTab.value === 'ShareFromOthers') {
      activeTab.value = 'MyShare';
      myData.value = false;
    }
    key.value++;
  }
</script>

<style lang="scss" scoped>
  .submitData {
    .bubble-right {
      padding: 8px 20px !important;
      &.active {
        background-color: #ebf2fd;
        &:after {
          border-color: transparent transparent transparent #ebf2fd !important;
        }
      }
    }
    .before-circle {
      &.active {
        background-color: #d5e4fe;
        border-radius: 14px;
        color: #3a78e8;
        /* padding: 0 20px; */
        border: 1px solid #3a78e8;
        padding: 2px 6px;
        text-align: center;

        &:before {
          display: none;
          background-color: #3a78e8 !important;
        }
      }

      &:before {
        background-color: #999999 !important;
      }
    }

    .el-form {
      .el-form-item {
        width: 30%;
      }
    }
  }

  .drop-down-title {
    & > span {
      font-weight: 600;
    }
    .el-icon {
      position: relative;
      top: 5px;
    }
  }

  .drop-down-content {
    padding: 0 15px;
    :deep(.el-checkbox__label) {
      color: #666666;
      font-size: 14px !important;
    }
    :deep(.el-tree-node__content) {
      background-color: #f4f8fb;
      .el-tree-node__label {
        color: #666666;
        font-weight: 600;
      }
    }
  }

  .drop-down-title {
    font-weight: 600;
    &.active {
      background-color: #feeee4;
      color: #fe7f2b;
      border-color: #fe7f2b;
    }
  }
</style>
