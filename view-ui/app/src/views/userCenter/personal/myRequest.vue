<template>
  <h3
    class="text-main-color mt-05 mb-1"
    v-text="
      myData
        ? $t('userCenter.personal.myRequest.title.myRequests')
        : $t('userCenter.personal.myRequest.title.requestFromOthers')
    "
  ></h3>
  <div
    class="bg-primary d-flex justify-space-between align-items-center sort hidden-xs-only"
  >
    <div>
      <span class="text-secondary-color font-600 mr-1"
        >{{ $t('userCenter.personal.myRequest.filters.sort') }}:</span
      >
      <el-button
        v-for="(item, index) in sortBtn"
        :key="item"
        plain
        class="sort-button"
        :class="{ active: item.highlighted }"
        @click="toggleSortOrder(index)"
        >{{ item.label }}
        <el-icon v-if="item.highlighted">
          <Bottom v-if="item.sortOrder === 'descending'" />
          <Top v-else />
        </el-icon>
      </el-button>
    </div>
    <div class="d-flex align-items-center search">
      <span class="text-secondary-color font-600 mr-1"
        >{{ $t('userCenter.personal.myRequest.filters.resourceType') }}:</span
      >
      <el-select
        v-model="resourceTypeVal"
        :placeholder="$t('userCenter.personal.myRequest.filters.select')"
        clearable
        style="width: 250px"
        @change="getDataList"
      >
        <el-option
          v-for="item in resourceType"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="d-flex align-items-center search">
      <span class="text-secondary-color font-600 mr-1"
        >{{ $t('userCenter.personal.myRequest.filters.year') }}:</span
      >
      <el-select
        v-model="year"
        clearable
        :placeholder="$t('userCenter.personal.myRequest.filters.select')"
        style="width: 250px"
        @change="getDataList"
      >
        <el-option
          v-for="item in yearOpt"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
  </div>
  <div
    v-if="requestList && requestList.length !== 0"
    v-loading="loading"
    class="mt-1"
  >
    <div v-for="(item, index) in requestList" :key="'requestList-' + index">
      <div class="d-flex align-items-center justify-space-between">
        <div class="d-flex align-items-center">
          <el-tag
            type="warning"
            round
            class="tag-proj tag-warning mr-1"
            :class="computedTagClass(item.typeId)"
            >{{ tagType(computedTagClass(item.typeId)) }}
          </el-tag>
          <el-tag
            v-if="item.newData"
            type="danger"
            effect="dark"
            round
            size="small"
            class="mr-05"
            >{{ $t('userCenter.personal.myRequest.status.new') }}
          </el-tag>
          <span
            class="font-600 text-warning mr-1 cursor-pointer"
            @click="toDetail(item)"
          >
            {{ item.typeId }}
          </span>
          <el-tooltip
            :width="20"
            placement="right"
            trigger="hover"
            :content="$t('userCenter.personal.myRequest.actions.dataList')"
          >
            <svg-icon
              icon-class="dataIdList"
              class-name="svg-idList"
              @click="expandDataList(index)"
            ></svg-icon>
          </el-tooltip>
          <el-tag
            :type="statusClass(item.status)"
            round
            class="ml-2"
            effect="plain"
            >{{ statusText(item.status) }}
          </el-tag>
        </div>
        <div class="font-600 text-main-color d-flex align-items-center">
          <div v-if="!myData && item.status === 'Requesting'">
            <el-button
              class="btn-round-success ml-05"
              plain
              type="success"
              round
              size="small"
              @click="changePass(item.id, true)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myRequest.actions.authorized')
              }}</span>
            </el-button>
            <el-button
              class="btn-round-danger mr-05"
              plain
              type="danger"
              round
              size="small"
              @click="changePass(item.id, false)"
            >
              <span class="font-600">{{
                $t('userCenter.personal.myRequest.actions.declined')
              }}</span>
            </el-button>
          </div>
        </div>
      </div>

      <!--data List-->
      <el-collapse-transition>
        <div v-show="item.expand" class="p-15 mt-05">
          <el-table
            :data="item.dataList"
            style="width: 100%; margin-bottom: 20px"
            :header-cell-style="{
              backgroundColor: '#f2f2f2',
              color: '#333333',
              fontWeight: 700,
            }"
            max-height="200"
            border
            :sort-orders="['ascending', 'descending']"
            tooltip-effect="dark"
            @sort-change="
              column => {
                relTableSortChange(column, index);
              }
            "
          >
            <el-table-column
              width="120"
              prop="datNo"
              :label="$t('userCenter.personal.myRequest.table.dataId')"
              sortable
            >
            </el-table-column>
            <el-table-column
              prop="name"
              :label="$t('userCenter.personal.myRequest.table.name')"
              sortable
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column
              v-if="!item.analysisData"
              prop="expName"
              :label="$t('userCenter.personal.myRequest.table.experiment')"
              sort-by="expNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/experiment/detail/${scope.row.expNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.expNo }} ({{ scope.row.expName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!item.analysisData"
              prop="sapName"
              :label="$t('userCenter.personal.myRequest.table.sample')"
              sort-by="sapNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/sample/detail/${scope.row.sapNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.sapNo }} ({{ scope.row.sapName }})
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!item.analysisData"
              prop="runName"
              :label="$t('userCenter.personal.myRequest.table.run')"
              sort-by="runNo"
              sortable
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/run/detail/${scope.row.runNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.runNo }} ({{ scope.row.runName }})
                </router-link>
              </template>
            </el-table-column>

            <el-table-column
              v-if="item.analysisData"
              :label="$t('userCenter.personal.myRequest.table.analysis')"
              sortable
              sort-by="analNo"
              show-overflow-tooltip
            >
              <template #default="scope">
                <router-link
                  :to="`/analysis/detail/${scope.row.analNo}`"
                  class="text-primary"
                  target="_blank"
                >
                  {{ scope.row.analNo }} ({{ scope.row.analName }})
                </router-link>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-if="item.dataList && item.dataList.length > 100"
            v-model:page="requestList[index].pageNum"
            :limit="100"
            layout="total, prev, pager, next"
            class="mb-1 mt-2 justify-center"
            :total="requestList[index].totalCount"
            @pagination="
              pageData => {
                pageDataList(pageData, index);
              }
            "
          />
        </div>
      </el-collapse-transition>

      <div class="mt-05 d-flex">
        <div
          v-if="item.status === 'Authorized' && item.expireDate"
          class="d-flex align-items-center mr-2"
        >
          <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{ $t('userCenter.personal.myRequest.dates.expiryDate') }}:</span
          >
          <span class="text-other-color">{{ item.expireDate }}</span>
        </div>

        <div v-if="item.applyDate" class="d-flex align-items-center mr-2">
          <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{ $t('userCenter.personal.myRequest.dates.requestDate') }}:</span
          >
          <span class="text-other-color">{{ item.applyDate }}</span>
        </div>

        <div
          v-if="item.status !== 'Requesting' && item.replyDate"
          class="d-flex align-items-center mr-2"
        >
          <svg-icon icon-class="request-date" class-name="svg"></svg-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{
              $t('userCenter.personal.myRequest.dates.authorizationDate')
            }}:</span
          >
          <span class="text-other-color">{{ item.replyDate }}</span>
        </div>

        <div v-if="item.ownerName" class="d-flex align-items-center mr-2">
          <el-icon color="#07BCB4">
            <Avatar />
          </el-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{ $t('userCenter.personal.myRequest.info.owner') }}:</span
          >
          <span class="text-other-color">{{ item.ownerName }}</span>
        </div>

        <div v-if="item.requestor" class="d-flex align-items-center mr-2">
          <el-icon color="#07BCB4">
            <Avatar />
          </el-icon>
          <span class="text-other-color mr-05 ml-05"
            >{{ $t('userCenter.personal.myRequest.info.requestor') }}:</span
          >
          <span class="text-other-color">{{ item.requestor }}</span>
        </div>
      </div>

      <div class="bg-gray request-list mt-05 list">
        <div>
          <span class="text-secondary-color count-type font-600 mr-05">
            {{ $t('userCenter.personal.myRequest.content.requestText') }}
          </span>
          <span class="text-secondary-color mr-05">
            {{ item.description }}</span
          >
        </div>
        <el-divider class="mt-05 mb-05"></el-divider>
        <div>
          <span class="text-secondary-color count-type font-600 mr-05">
            {{ $t('userCenter.personal.myRequest.content.replyText') }}</span
          >
          <span class="text-secondary-color mr-05"> {{ item.replyText }}</span>
        </div>
      </div>
      <el-divider></el-divider>
    </div>

    <pagination
      v-show="total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="total"
      class="mb-1"
      @pagination="getDataList"
    />

    <el-dialog
      v-model="declineDialog"
      :title="$t('userCenter.personal.myRequest.dialog.comment')"
      width="600"
      class="radius-14"
    >
      <el-input v-model="passComment" type="textarea" :rows="10"></el-input>
      <template #footer>
        <div class="dialog-footer text-center">
          <el-button type="primary" @click="savePass">
            {{ $t('userCenter.personal.myRequest.dialog.save') }}
          </el-button>
          <el-button @click="declineDialog = false"
            >{{ $t('userCenter.personal.myRequest.dialog.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
  <div v-else v-loading="loading">
    <el-empty></el-empty>
  </div>
</template>

<script setup>
  import { getCurrentInstance, onMounted, reactive, ref, toRefs } from 'vue';
  import {
    getApplyRequestList,
    getRequestDataList,
    saveRequestStatus,
    updateSee,
  } from '@/api/app/request';
  import { useRouter } from 'vue-router';
  import useUserStore from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { useI18n } from 'vue-i18n';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);
  let { locale } = useI18n();

  const { proxy } = getCurrentInstance();
  const router = useRouter();

  const props = defineProps({
    myData: {
      type: Boolean,
      required: true,
      default: true,
    },
  });

  // 判断是我的数据，还是来自他人的数据列表
  const myData = ref(props.myData);

  const resourceTypeVal = ref('');
  const resourceType = reactive([
    {
      value: 'project',
      label: proxy.$t('userCenter.personal.myRequest.resourceTypes.project'),
    },
    {
      value: 'experiment',
      label: proxy.$t('userCenter.personal.myRequest.resourceTypes.experiment'),
    },
    {
      value: 'sample',
      label: proxy.$t('userCenter.personal.myRequest.resourceTypes.sample'),
    },
    {
      value: 'analysis',
      label: proxy.$t('userCenter.personal.myRequest.resourceTypes.analysis'),
    },
  ]);
  const year = ref('');
  const yearOpt = ref([]);

  /** 响应式数据 */
  const data = reactive({
    total: 0,
    queryParams: {
      pageNum: 1,
      pageSize: 10,
    },
  });

  /** 解构 */
  const { total, queryParams } = toRefs(data);

  const sortBtn = reactive([
    {
      label: proxy.$t('userCenter.personal.myRequest.sort.requestDate'),
      field: 'apply_date',
      highlighted: true,
      sortOrder: 'descending',
    },
    {
      label: proxy.$t('userCenter.personal.myRequest.sort.mainId'),
      field: 'type_id',
      highlighted: false,
      sortOrder: 'ascending',
    },
    {
      label: proxy.$t('userCenter.personal.myRequest.sort.status'),
      field: 'status',
      highlighted: false,
      sortOrder: 'ascending',
    },
  ]);

  // 左上角排序
  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
  });

  const toggleSortOrder = index => {
    sortBtn.forEach(btn => {
      btn.highlighted = false;
    });
    sortBtn[index].highlighted = true;
    sortBtn[index].sortOrder =
      sortBtn[index].sortOrder === 'ascending' ? 'descending' : 'ascending';

    // 修改排序字段
    doSortSearch(index);
    getDataList();
  };

  function doSortSearch(index) {
    queryPageAndSort.value.sortKey = sortBtn[index].field;
    queryPageAndSort.value.sortType = sortBtn[index].sortOrder;
  }

  // 展开的表格排序
  function relTableSortChange(column, index) {
    let { prop, order } = column;
    if (order) {
      requestList.value[index].sortKey = prop;
      requestList.value[index].sortType =
        order === 'ascending' ? 'ascending' : 'descending';
      getItemRequestDataList(index);
    }
  }

  // 展示data list
  const expandDataList = index => {
    let item = requestList.value[index];

    // 已经展开，点击则收起
    if (item?.expand) {
      item.expand = false;
      return;
    }

    getItemRequestDataList(index);
  };

  // 查询具体data数据的列表
  function getItemRequestDataList(index) {
    let item = requestList.value[index];
    item.analysisData = item.type === 'analysis';

    const pageNum = item.pageNum || 1;

    let params = {
      requestId: item.id,
      sortKey: item.sortKey,
      sortType: item.sortType,
      pageNum: pageNum,
      pageSize: 100, // 循环多分页，不方便做page size的切换，固定limit
    };

    getRequestDataList(params).then(response => {
      item.dataList = response.data?.dataVos || [];
      item.totalCount = response.data?.total || 0;
      item.pageNum = pageNum;
      item.expand = true;
    });
  }

  /** 展开的data list数据分页 */
  function pageDataList(pageData, index) {
    requestList.value[index].pageSize = pageData.limit;
    requestList.value[index].pageNum = pageData.page;
    getItemRequestDataList(index);
  }

  // 生成最近3年内的查询下拉词
  function getRecentYears() {
    let currentYear = new Date().getFullYear();

    for (let i = 0; i < 3; i++) {
      yearOpt.value.push({ value: currentYear - i, label: currentYear - i });
    }

    const lastYear = yearOpt.value[yearOpt.value.length - 1].value;
    yearOpt.value.push({
      value: '<' + lastYear,
      label:
        proxy.$t('userCenter.personal.myRequest.filters.before') +
        ' ' +
        lastYear,
    });
  }

  const requestList = ref([]);
  const loading = ref(false);

  // 查询外层的请求列表
  function getDataList() {
    const params = {
      sourceMember: member.value.id,
      myRequest: myData.value,
      resourceType: resourceTypeVal.value,
      year: year.value,
      orderByColumn: queryPageAndSort.value.sortKey,
      isAsc: queryPageAndSort.value.sortType,
      pageNum: queryParams.value.pageNum,
      pageSize: queryParams.value.pageSize,
    };
    loading.value = true;
    getApplyRequestList(params)
      .then(response => {
        requestList.value = response.rows;
        total.value = response.total;
      })
      .finally(() => {
        loading.value = false;
      });
  }

  /** 通过或者拒绝请求 */
  const declineDialog = ref(false);
  const requestId = ref('');
  const pass = ref(false);
  const passComment = ref('');

  /** 弹出通过或者拒绝请求的弹框  */
  function changePass(id, passStatus) {
    declineDialog.value = true;
    requestId.value = id;
    pass.value = passStatus;
  }

  function savePass() {
    const params = {
      id: requestId.value,
      status: pass.value,
      comment: passComment.value,
    };

    proxy.$modal.loading(
      proxy.$t('userCenter.personal.myRequest.messages.saving'),
    );
    saveRequestStatus(params)
      .then(response => {
        requestList.value = response.data;
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        declineDialog.value = false;

        getDataList();
      });
  }

  // 跳转到数据详情，并且将数据更新为已读
  function toDetail(item) {
    updateSee(item.id).then(() => {
      router.push(`/${item.type}/detail/${item.typeId}`);
    });
  }

  const computedTagClass = id => {
    if (id.includes('OEP')) {
      return 'tag-proj';
    } else if (id.includes('OES')) {
      return 'tag-samp';
    } else if (id.includes('OEX')) {
      return 'tag-expr';
    } else {
      return 'tag-anal';
    }
  };

  const statusClass = status => {
    if (status === 'Authorized') {
      return 'success';
    } else if (status === 'Requesting') {
      return 'primary';
    } else if (status === 'Declined') {
      return 'danger';
    }
  };

  const statusText = status => {
    if (status === 'Authorized') {
      return proxy.$t('userCenter.personal.myRequest.status.authorized');
    } else if (status === 'Requesting') {
      return proxy.$t('userCenter.personal.myRequest.status.requesting');
    } else if (status === 'Declined') {
      return proxy.$t('userCenter.personal.myRequest.status.declined');
    }
  };

  const tagType = tag => {
    const parts = tag.split('-');
    let type = parts[parts.length - 1].toUpperCase();
    if (type === 'PROJ') {
      return locale.value === 'zh' ? '项目' : type;
    } else if (type === 'EXPR') {
      return locale.value === 'zh' ? '实验' : type;
    } else if (type === 'SAMP') {
      return locale.value === 'zh' ? '样本' : type;
    } else if (type === 'ANAL') {
      return locale.value === 'zh' ? '分析' : type;
    }
    return type;
  };

  onMounted(() => {
    getRecentYears();
    getDataList();
  });
</script>

<style lang="scss" scoped>
  .list {
    .count-type {
      display: inline-block;
      width: 100px;
    }
  }

  .request-list {
    border-left: 2px solid #79ce78;

    .reply-text {
      border-radius: 3px;
      border: 1px solid #eee;
      background-color: #f3f3f3;
    }
  }

  .bg-primary {
    background-color: #f5f8fe;

    .search {
      padding: 6px 10px;
    }
  }

  .tag-success {
    background-color: #cfefed !important;
    color: #07bcb4;
    font-weight: 600;
    border-radius: 8px;
  }

  :deep(.el-tag__content) {
    font-weight: 600;
  }

  :deep(.el-button--small.is-round) {
    padding: 5px 11px !important;
  }

  .svg {
    width: 14px;
    height: 14px;
  }

  .svg-idList {
    width: 13px;
    height: 13px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .bg-gray {
    padding: 6px 15px;
  }

  .qr-code {
    width: 15px;
    height: 15px;
    cursor: pointer;
  }

  .sort {
    :deep(.el-input-group__append) {
      padding: 0;
      border-radius: 0 12px 12px 0;
    }
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }

  @media (max-width: 767px) {
    .d-flex {
      flex-wrap: wrap;
    }
  }
</style>
