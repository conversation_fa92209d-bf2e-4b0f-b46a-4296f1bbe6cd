<template>
  <el-dialog
    :model-value="showDialog"
    title="Delete"
    width="900"
    class="dialog radius-14"
    @close="
      () => {
        showDialog = false;
      }
    "
  >
    <el-row class="mb-1">
      <el-alert
        title="Notice:The following items will be deleted"
        type="error"
        :closable="false"
      />
    </el-row>

    <el-row class="mb-1">
      <div class="d-flex row-gap-10 flex-wrap">
        <div
          v-for="id in deleteCheckResult.projNos"
          :key="'project-' + id"
          class="id-list mr-1"
        >
          <span class="btn-project">P</span>
          <router-link :to="'/project/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.expNos"
          :key="'experiments-' + id"
          class="id-list mr-1"
        >
          <span class="btn-experiment">E</span>
          <router-link :to="'/experiment/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.sapNos"
          :key="'sample-' + id"
          class="id-list mr-1"
        >
          <span class="btn-sample">S</span>
          <router-link :to="'/sample/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.runNos"
          :key="'run-' + id"
          class="id-list mr-1"
        >
          <span class="btn-run">R</span>
          <router-link :to="'/run/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.analNos"
          :key="'analysis-' + id"
          class="id-list mr-1"
        >
          <span class="btn-project">A</span>
          <router-link :to="'/analysis/detail/' + id">
            {{ id }}
          </router-link>
        </div>
        <div
          v-for="id in deleteCheckResult.dataNos"
          :key="'data-' + id"
          class="id-list mr-1"
        >
          <span class="btn-data">D</span>
          {{ id }}
        </div>
      </div>
    </el-row>
    <el-row v-if="deleteCheckResult.shareMap" class="mb-1">
      <el-divider></el-divider>
      <el-alert
        title="The following data is being shared. After deletion, the related sharing will become invalid"
        type="error"
        :closable="false"
      />
    </el-row>
    <el-row v-if="deleteCheckResult.shareMap" class="mb-1">
      <div class="card" style="width: 100%">
        <el-col :span="24" class="mb-1"> SHARE :</el-col>
        <el-col
          v-for="key in Object.keys(deleteCheckResult.shareMap)"
          :key="'share-' + key"
          :span="24"
          class="mb-1"
        >
          <div class="id-list mr-1">
            <span :class="classMap[key.slice(0, 3)].class">{{
              classMap[key.slice(0, 3)].prefix
            }}</span>

            <div v-if="classMap[key.slice(0, 3)].routePath">
              <router-link
                :to="`/${classMap[key.slice(0, 3)].routePath}/detail/` + key"
              >
                {{ key }}
              </router-link>
            </div>
            <div v-else>
              {{ key }}
            </div>
          </div>
          <span>{{ deleteCheckResult.shareMap[key].join('; ') }}</span>
        </el-col>
      </div>
    </el-row>
    <el-row>
      <el-input
        v-model="password"
        show-password
        placeholder="Please verify your password"
      ></el-input>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          :disabled="password.length === 0"
          type="primary"
          class="btn-primary btn btn-s btn-shadow"
          round
          @click="confirmDelete"
          >Confirm</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script setup>
  import { computed, getCurrentInstance, toRefs } from 'vue';

  const { proxy } = getCurrentInstance();
  let props = defineProps({
    showDialog: {
      required: true,
      type: Boolean,
      default: false,
    },
    password: {
      required: true,
      type: String,
    },
    deleteCheckResult: {
      required: true,
      type: Object,
    },
  });
  const password = computed({
    get() {
      return props.password;
    },
    set(val) {
      proxy.$emit('update:password', val);
    },
  });
  const showDialog = computed({
    get() {
      return props.showDialog;
    },
    set(val) {
      proxy.$emit('update:showDialog', val);
    },
  });
  const { deleteCheckResult } = toRefs(props);

  function confirmDelete() {
    proxy.$emit('delete-method');
  }

  let classMap = {
    OEP: {
      class: 'btn-project',
      prefix: 'P',
      routePath: 'project',
    },
    OEX: {
      class: 'btn-experiment',
      prefix: 'E',
      routePath: 'experiment',
    },
    OES: {
      class: 'btn-sample',
      prefix: 'S',
      routePath: 'sample',
    },
    OER: {
      class: 'btn-run',
      prefix: 'R',
    },
    OEZ: {
      class: 'btn-data',
      prefix: 'A',
      routePath: 'analysis',
    },
    OED: {
      class: 'btn-data',
      prefix: 'D',
    },
  };
</script>
<style scoped lang="scss"></style>
