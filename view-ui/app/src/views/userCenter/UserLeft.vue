<template>
  <div class="card">
    <div class="d-flex align-items-center justify-center">
      <svg-icon icon-class="user" class-name="svg-user"></svg-icon>
      <div class="d-flex flex-column ml-05">
        <el-tooltip :width="20" trigger="hover" content="Edit">
          <!--URL后期放入配置文件中-->
          <a @click="memberEditUrlFunc">
            <svg-icon
              icon-class="edit"
              class-name="svg-edit"
              class="cursor-pointer"
            ></svg-icon>
          </a>
        </el-tooltip>
        <el-tooltip :width="50" trigger="hover" content="Change Password">
          <a @click="changePwdUrlFunc">
            <svg-icon
              icon-class="password"
              class="cursor-pointer"
              class-name="svg-password mt-05"
            ></svg-icon>
          </a>
        </el-tooltip>
        <!--        <span class="text-warning font-12">Audit</span>-->
      </div>
    </div>
    <div class="text-secondary-color mt-05 text-center">{{ member.email }}</div>
    <div
      v-if="member.auditStatus === 'pass'"
      class="text-secondary-color text-success text-center"
    >
      Account status: Approved
    </div>
    <div
      v-if="member.auditStatus === 'waiting'"
      class="text-secondary-color text-warning text-center"
    >
      Account status: Awaiting Review
    </div>
    <div
      v-if="member.auditStatus === 'fail'"
      class="text-secondary-color text-danger text-center"
    >
      Account status: Not Approved
    </div>

    <el-divider class="mt-05 mb-05"></el-divider>

    <div class="text-secondary-color font-600 font-13">Name:</div>
    <span class="text-other-color font-13 mb-05"
      >{{ member.firstName }}&nbsp;{{ member.lastName }}</span
    >
    <div class="text-secondary-color font-600 font-13 mt-05">Organization:</div>
    <span class="text-other-color font-13 mb-05">{{ member.orgName }}</span>

    <el-divider class="mt-05 mb-05"></el-divider>

    <div
      v-show="shareStatus === ConfigEnum.Enable"
      class="bg-gray user-fun mt-1"
    >
      <div class="text-secondary-color font-600 mb-05 text-center">Share</div>
      <router-link to="/userCenter/MyShare">
        <div class="drop-down-title plr-0 mb-1 cursor-pointer text-center">
          My Shares
          <el-tag
            v-if="newShareNum > 0"
            type="danger"
            round
            size="small"
            effect="dark"
          >
            {{ newShareNum }}
          </el-tag>
        </div>
      </router-link>
      <router-link to="/userCenter/ShareFromOthers">
        <div class="drop-down-title plr-0 mt-05 cursor-pointer text-center">
          Shares from others
          <el-tag
            v-if="shareFromOtherNum > 0"
            type="danger"
            round
            size="small"
            effect="dark"
          >
            {{ shareFromOtherNum }}
          </el-tag>
        </div>
      </router-link>
    </div>

    <div
      v-show="requestStatus === ConfigEnum.Enable"
      class="bg-gray mt-1-5 user-fun"
    >
      <div class="text-secondary-color mb-05 font-600 text-center">
        Requests
      </div>
      <router-link to="/userCenter/MyRequest">
        <div class="drop-down-title plr-0 mb-1 cursor-pointer text-center">
          My Requests
          <el-tag
            v-if="requestNum.myDataNum && requestNum.myDataNum > 0"
            type="danger"
            round
            size="small"
            effect="dark"
          >
            {{ requestNum.myDataNum || 0 }}
          </el-tag>
        </div>
      </router-link>
      <router-link to="/userCenter/RequestFromOthers">
        <div class="drop-down-title plr-0 mt-05 cursor-pointer text-center">
          Requests from others
          <el-tag
            v-if="
              requestNum.formOtherDataNum && requestNum.formOtherDataNum > 0
            "
            type="danger"
            round
            size="small"
            effect="dark"
          >
            {{ requestNum.formOtherDataNum || 0 }}
          </el-tag>
        </div>
      </router-link>
    </div>

    <div
      v-show="reviewStatus === ConfigEnum.Enable"
      class="bg-gray mt-1-5 user-fun"
    >
      <div class="text-secondary-color mb-05 font-600 text-center">Reviews</div>
      <router-link to="/userCenter/MyReview">
        <div class="drop-down-title plr-0 cursor-pointer text-center">
          My Reviews

          <el-tag
            v-if="reviewNum && reviewNum > 0"
            type="danger"
            round
            size="small"
            effect="dark"
          >
            {{ reviewNum || 0 }}
          </el-tag>
        </div>
      </router-link>
    </div>
  </div>
</template>

<script setup>
  import useUserStore from '@/store/modules/user';
  import { getCurrentInstance, onMounted, ref } from 'vue';
  import { getNewRequestNum } from '@/api/app/request';
  import { getNewReviewNum } from '@/api/app/review';
  import { getNewShareNum } from '@/api/app/share';
  import { ConfigEnum } from '@/utils/enums';

  const { member } = useUserStore();
  const { proxy } = getCurrentInstance();

  const requestStatus = proxy.getConfigVal(ConfigEnum.Request_Status);
  const shareStatus = proxy.getConfigVal(ConfigEnum.Share_Status);
  const reviewStatus = proxy.getConfigVal(ConfigEnum.Review_Status);

  const requestNum = ref({});
  const reviewNum = ref(0);
  const newShareNum = ref(0);
  const shareFromOtherNum = ref(0);

  onMounted(() => {
    getNewRequest();
    getNewReview();
    getNewShare();
  });

  function getNewRequest() {
    getNewRequestNum().then(response => {
      requestNum.value = response.data;
    });
  }

  function getNewReview() {
    getNewReviewNum().then(response => {
      reviewNum.value = response.data;
    });
  }

  function getNewShare() {
    getNewShareNum().then(response => {
      const dataMap = response.data;
      newShareNum.value = intVal(dataMap.myShareNewCount);
      shareFromOtherNum.value = intVal(dataMap.shareOtherNewCount);
    });
  }

  function intVal(val) {
    return val ? Number(val) : 0;
  }

  const bmdcRegisterUrl = import.meta.env.VITE_BMDC_REGISTER_URL;

  // 修改密码
  function changePwdUrlFunc() {
    document.write(
      `<form action='${bmdcRegisterUrl}/changePwdPage' method='post' id='changePwdForm' name='changePwdForm' style='display:none'>`,
    );
    document.write(
      `	<input type='hidden' name='memberId' value='${useUserStore().userId}'>`,
    );
    document.write(
      `<input type='hidden' name='refererUrl' value='${bmdcRegisterUrl.replace(
        '/bmdcRegist',
        '',
      )}/node/logout'>`,
    );
    document.write('</form>');
    document.changePwdForm.submit();
  }

  // 修改用户基本信息
  function memberEditUrlFunc() {
    document.write(
      `<form action='${bmdcRegisterUrl}/setting/member' method='post' id='memberEditForm' name='memberEditForm' style='display:none'>`,
    );
    document.write(
      `	<input type='hidden' name='memberId' value='${useUserStore().userId}'>`,
    );
    document.write(
      `<input type='hidden' name='refererUrl' value='${import.meta.env.VITE_LOGOUT_URL}'>`,
    );
    document.write('</form>');
    document.memberEditForm.submit();
  }
</script>

<style scoped lang="scss">
  .svg-user {
    width: 82px;
    height: 82px;
  }

  .user-fun {
    padding: 5px 10px 15px 10px;
    margin-top: 1rem;
    border-radius: 4px;
  }

  .svg-password,
  .svg-edit {
    width: 20px;
    height: 20px;
  }
</style>
