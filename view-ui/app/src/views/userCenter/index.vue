<template>
  <div class="submit-page">
    <div class="container-fluid">
      <Breadcrumb bread-item="User Center" />
      <el-row :gutter="20" class="mt-1">
        <!--左侧用户基本信息-->
        <el-col :span="5" :xs="24" :md="5" class="mb-1">
          <UserLeft></UserLeft>
        </el-col>

        <!--右侧表格与统计-->
        <el-col :span="18" :xs="24" :md="18">
          <!--密码修改提示-->
          <div v-if="tipChangePwd" class="card" style="padding: 12px 10px">
            <div class="bg-gray notice radius-14 d-flex align-items-center">
              <el-icon color="#3A78E8">
                <BellFilled />
              </el-icon>
              <span class="ml-05">Notice:</span>
              <span class="ml-05"
                >Your password has been modified for more than 90 days. For your
                account security, please click
                <a
                  href="javascript:void 0"
                  class="text-primary"
                  @click="changePwdUrlFunc"
                  >here</a
                >
                to update your password in time</span
              >
            </div>
          </div>

          <div class="card" :class="tipChangePwd ? 'mt-1' : ''">
            <el-tabs v-model="activeName" type="card" class="first-tab">
              <el-tab-pane label="My Data List" name="dataList">
                <el-tabs v-model="activeDataList" class="demo-tabs">
                  <el-tab-pane label="Projects" name="project">
                    <project :has-tip="tipChangePwd"></project>
                  </el-tab-pane>
                  <el-tab-pane label="Experiments" name="experiment">
                    <experiment />
                  </el-tab-pane>
                  <el-tab-pane label="Samples" name="sample">
                    <sample></sample>
                  </el-tab-pane>
                  <el-tab-pane label="Runs" name="run">
                    <run></run>
                  </el-tab-pane>
                  <el-tab-pane label="Analysis" name="analysis">
                    <analysis></analysis>
                  </el-tab-pane>
                  <el-tab-pane label="Data" name="data">
                    <Data></Data>
                  </el-tab-pane>
                  <el-tab-pane label="Publishes" name="publish">
                    <Publish></Publish>
                  </el-tab-pane>
                  <el-tab-pane label="Submissions" name="submission">
                    <my-submission-list :height="390"></my-submission-list>
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <el-tab-pane label="My Data Statistics" name="dataStatistics">
                <DataStatistic
                  v-if="activeName === 'dataStatistics'"
                ></DataStatistic>
              </el-tab-pane>

              <el-tab-pane label="My Data Activity" name="dataActivity">
                <DataActivity
                  v-if="activeName === 'dataActivity'"
                ></DataActivity>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import { ref, watch } from 'vue';
  import UserLeft from '@/views/userCenter/UserLeft.vue';
  import Project from '@/views/userCenter/data/project.vue';
  import DataStatistic from '@/views/userCenter/dataStatistic/index.vue';
  import DataActivity from '@/views/userCenter/dataActivity/index.vue';
  import Experiment from '@/views/userCenter/data/experiment.vue';
  import Sample from '@/views/userCenter/data/sample.vue';
  import Run from '@/views/userCenter/data/run.vue';
  import MySubmissionList from '@/views/submit/components/mySubmissionList.vue';
  import useUserStore from '@/store/modules/user';
  import Analysis from '@/views/userCenter/data/analysis.vue';
  import Data from '@/views/userCenter/data/data.vue';
  import Publish from '@/views/userCenter/data/publish.vue';

  const { member } = useUserStore();

  // 密码是否超过90天未修改
  const tipChangePwd = ref(false);

  watch(
    member,
    () => {
      tipChangePwdHandle();
    },
    { immediate: true, deep: true },
  );

  function tipChangePwdHandle() {
    if (member && member.updateTime) {
      tipChangePwd.value = false;
    }
    const currentDate = new Date();
    const inputDate = new Date(member.updateTime);

    // 计算当前时间和输入日期之间的毫秒数差值
    const timeDiff = currentDate.getTime() - inputDate.getTime();

    // 将毫秒数差值转换为天数
    const daysDiff = timeDiff / (1000 * 3600 * 24);

    tipChangePwd.value = daysDiff > 90;
  }

  const activeName = ref('dataList');
  const activeDataList = ref('project');

  const bmdcRegisterUrl = import.meta.env.VITE_BMDC_REGISTER_URL;

  // 修改密码
  function changePwdUrlFunc() {
    document.write(
      `<form action='${bmdcRegisterUrl}/changePwdPage' method='post' id='changePwdForm' name='changePwdForm' style='display:none'>`,
    );
    document.write(
      `	<input type='hidden' name='memberId' value='${useUserStore().userId}'>`,
    );
    document.write(
      `<input type='hidden' name='refererUrl' value='${bmdcRegisterUrl.replace(
        '/bmdcRegist',
        '',
      )}/node/logout'>`,
    );
    document.write('</form>');
    document.changePwdForm.submit();
  }
</script>

<style lang="scss" scoped>
  .el-divider {
    margin: 10px 0;
  }

  :deep(.first-tab > .el-tabs__header .el-tabs__item) {
    margin-left: 3px !important;
    border: none;
  }

  .first-tab {
    :deep(.el-tabs__item) {
      height: 35px;
    }

    & > :deep(.el-tabs__header) {
      margin-bottom: 5px;
    }

    .select-exptype .el-checkbox {
      width: 150px;
    }

    :deep(.el-checkbox__label) {
      font-weight: 600;
    }

    .el-divider {
      margin: 0 3px;
    }
  }

  .demo-tabs {
    :deep(.el-tabs__item) {
      background-color: transparent !important;

      &.is-active {
        color: #409eff !important;
      }
    }

    .data-tab {
      :deep(.el-tabs__item) {
        background-color: #ffffff !important;
        border: 1px solid #409eff;
        height: 30px;
        border-radius: 0;
        color: #666666;

        &:first-child {
          border-radius: 12px 0 0 12px !important;
          border-right: 0;
        }

        &:nth-child(2) {
          border-right: 0;
          border-radius: 0 !important;
        }

        &:last-child {
          border-radius: 0 12px 12px 0 !important;
        }

        &.is-active {
          background-color: #ebf2fd !important;
          color: #409eff !important;
        }
      }

      :deep(.el-tabs__header) {
        margin-bottom: 10px;
      }
    }
  }

  :deep(.el-popper.is-dark) {
    max-width: 300px;
  }

  .notice {
    padding: 8px;
    border-radius: 20px !important;
  }

  .edit-svg {
    width: 15px;
    height: 15px;
  }

  .waiting-svg {
    width: 14px;
    height: 14px;
  }

  :deep(.el-select__wrapper) {
    border-radius: 12px;
  }
</style>
