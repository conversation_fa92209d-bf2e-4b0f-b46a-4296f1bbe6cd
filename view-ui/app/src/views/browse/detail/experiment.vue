<template>
  <div class="page">
    <div v-if="showContent" class="container-fluid">
      <Breadcrumb
        :bread-item="(userData ? 'My ' : '') + 'Experiment Details'"
      />
      <el-row :gutter="15" class="mt-1 row-gap-15">
        <el-col :span="18" :xs="24" :md="18">
          <div ref="leftHeight">
            <general-info
              :key="'general-info-key-' + generalInfoKey"
              v-loading="generalInfoLoading"
              :general-info="genInfo"
              :creator="creator"
              :visible-status="visibleStatus"
              :publications="publish"
              :related-links="relatedLinks"
              type="experiment"
              :type-id="no"
            ></general-info>
            <attributes
              v-if="attrs.length !== 0"
              v-loading="generalInfoLoading"
              :data="attrs"
            ></attributes>
          </div>
        </el-col>
        <el-col :span="6" :xs="24" :md="6" class="hidden-xs-only">
          <association v-if="showAssociation" :style="rightHeight" />
        </el-col>
      </el-row>
      <stat-detail
        v-loading="statDetailLoading"
        :data="statDetailData"
      ></stat-detail>
      <related-data-list
        ref="dataListRef"
        :key="'exp-data-list-key-' + dataListKey"
        :columns="columns"
        :curr-id="currId"
        :type="'experiment'"
      ></related-data-list>
      <related-data-qc-info-list
        :key="'qc-list-key-' + dataListKey"
        :curr-id="currId"
        :type="'experiment'"
      ></related-data-qc-info-list>
      <related-analysis-list
        v-if="analysisList.length > 0"
        v-loading="analListLoading"
        :data="analysisList"
      />
      <author-info
        v-loading="authorInfoLoading"
        :data="authorData"
      ></author-info>
    </div>
    <div v-if="!showContent" class="container-fluid">
      <div class="card mt-1">
        <h3 class="text-main-color mb-0">Experiment Detail</h3>
        <el-result icon="error" title="Error">
          <template #sub-title>
            <div class="font-600">
              {{ errorContent }}
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import GeneralInfo from '@/views/browse/detail/components/GeneralInfo.vue';
  import StatDetail from '@/views/browse/detail/components/StatDetail.vue';
  import AuthorInfo from '@/views/browse/detail/components/AuthorInfo.vue';
  import RelatedAnalysisList from '@/views/browse/detail/components/RelatedAnalysisList.vue';
  import RelatedDataList from '@/views/browse/detail/components/RelatedDataList.vue';
  import RelatedDataQcInfoList from '@/views/browse/detail/components/RelatedDataQcInfoList.vue';

  import { computed, getCurrentInstance, onMounted, reactive, ref } from 'vue';
  import {
    checkExperimentPermission,
    getAuthorInfo,
    getExperimentGeneralInfo,
    getRelatedAnalysis,
    getStatDetail,
  } from '@/api/app/experiment';
  import Association from '@/views/browse/detail/components/Association.vue';
  import Attributes from '@/views/browse/detail/components/attributes.vue';
  import useUserStore from '@/store/modules/user';

  import { storeToRefs } from 'pinia';
  import { isTokenAccess } from '@/utils/nodeCommon';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  const { proxy } = getCurrentInstance();

  const rightHeight = ref({
    overflowY: 'auto',
    height: '300px',
  });

  let showContent = ref(true);
  let errorContent = ref('');
  let showAssociation = ref(false);

  let no = proxy.$route.params.id;
  let currId = ref(no);
  onMounted(() => {
    checkExperimentPermission(no).then(response => {
      if (response.msg) {
        errorContent.value = response.msg;
        showContent.value = false;
      } else {
        loadGeneralInfo();
        loadStatDetail();
        // loadDataList();
        loadRelatedAnalysisList();
        loadAuthorInfo();
        showAssociation.value = true;
      }
    });
  });

  const userData = computed(() => {
    return creator.value === member.value.id || isTokenAccess();
  });

  /** 加载基本信息 */
  const genInfo = reactive([]);
  const publish = ref([]);
  const creator = ref('');
  const visibleStatus = ref('');
  const relatedLinks = ref([]);
  const generalInfoKey = ref(0);
  const attrsKey = ref(0);
  const generalInfoLoading = ref(false);
  const attrs = ref([]);

  function loadGeneralInfo() {
    generalInfoLoading.value = true;
    getExperimentGeneralInfo(no)
      .then(response => {
        currId.value = response.data.expNo;
        dataListKey.value++;
        genInfo.push({
          label: 'Experiment ID',
          value: response.data.expNo,
        });
        genInfo.push({
          label: 'Experiment Type',
          value: response.data.expType,
        });
        genInfo.push({
          label: 'Experiment Name',
          value: response.data.name,
        });
        genInfo.push({
          label: 'Project No',
          value: response.data.projectNo,
        });
        genInfo.push({
          label: 'Description',
          value: response.data.description,
        });
        // 历史usedIds
        if (response.data?.usedIds) {
          const joinedString = response.data?.usedIds.join(';');
          genInfo.push({
            label: 'Used Ids',
            value: joinedString,
          });
        }
        genInfo.push({
          label: 'Protocol',
          value: response.data.protocol,
        });
        publish.value = response.data.publishes;
        creator.value = response.data.creator;
        visibleStatus.value = response.data.visibleStatus;
        relatedLinks.value = response.data.relatedLinks;
        attrs.value = [response.data.attributes];
      })
      .finally(() => {
        generalInfoKey.value++;
        attrsKey.value++;
        generalInfoLoading.value = false;
        const divHeight = proxy.$refs['leftHeight'].clientHeight;
        rightHeight.value.height = divHeight - 100 + 'px';
      });
  }

  /** 加载统计信息 */
  let statDetailData = reactive([]);
  let statDetailLoading = ref(false);

  function loadStatDetail() {
    statDetailLoading.value = true;
    getStatDetail(no)
      .then(response => {
        statDetailData.push({
          title: 'Cases and File Counts by Organism',
          data: response.data.sapStats,
        });
        statDetailData.push({
          title: 'File types and access restrictions',
          data: response.data.dataStats,
        });
      })
      .finally(() => {
        statDetailLoading.value = false;
      });
  }

  /** 加载data list */
  let dataListKey = ref(0);
  // 列显隐信息
  const columns = ref([
    { prop: 'projNo', minWidth: 120, label: `Project ID`, visible: true },
    { prop: 'projName', minWidth: 200, label: `Project Name`, visible: true },
    {
      prop: 'projDesc',
      minWidth: 170,
      label: `Project Description`,
      visible: false,
      notSort: true,
    },
    { prop: 'sapNo', minWidth: 120, label: `Sample ID`, visible: true },
    { prop: 'sapName', minWidth: 150, label: `Sample Name`, visible: false },
    { prop: 'sapType', minWidth: 170, label: `Sample Type`, visible: true },
    {
      prop: 'sapDesc',
      minWidth: 190,
      label: `Sample Description`,
      visible: false,
      notSort: true,
    },
    { prop: 'runNo', minWidth: 120, label: `Run ID`, visible: true },
    { prop: 'runName', minWidth: 170, label: `Run Name`, visible: true },
    { prop: 'datNo', minWidth: 120, label: `Data ID`, visible: true },
    { prop: 'name', minWidth: 170, label: `Data Name`, visible: true },
    { prop: 'security', minWidth: 140, label: `Data Security`, visible: true },
    {
      prop: 'readableFileSize',
      minWidth: 110,
      label: `Data Size`,
      visible: false,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: `Data Upload Time`,
      visible: false,
    },
  ]);

  /** 加载相关联的analysis */
  let analysisList = reactive([]);
  let analListLoading = ref(false);

  function loadRelatedAnalysisList() {
    analListLoading.value = true;
    getRelatedAnalysis(no)
      .then(response => {
        analysisList.push(...response.data);
      })
      .finally(() => {
        analListLoading.value = false;
      });
  }

  /** 加载用户信息 */
  let authorData = ref({
    createDate: undefined,
    updateDate: undefined,
    submitter: {
      firstName: '',
      middleName: '',
      lastName: '',
      orgName: '',
    },
  });
  let authorInfoLoading = ref(false);

  function loadAuthorInfo() {
    authorInfoLoading.value = true;
    getAuthorInfo(no)
      .then(response => {
        authorData.value = response.data;
      })
      .finally(() => {
        authorInfoLoading.value = false;
      });
  }
</script>

<style lang="scss" scoped>
  :deep(.el-tree-node__content):hover {
    background-color: #f9f9f9;
  }

  :deep(.el-tree-node__content) {
    //justify-content: center;
    border-radius: 12px;
    padding: 16px 0;
  }

  //:deep(.el-tree-node__children) {
  //  .el-tree-node:nth-child(odd) {
  //    .el-tree-node__content {
  //      background-color: #f9f9f9;
  //    }
  //  }
  //}
</style>
