<template>
  <div v-loading="loading" class="card h-100">
    <h3>Association</h3>
    <el-divider class="mt-05 mb-1"></el-divider>
    <el-tree
      v-if="children.length !== 0"
      :data="expNoTree"
      :props="defaultProps"
      default-expand-all
      :style="children.length === 0 ? '' : props.style"
    >
      <template #default="{ node, data }">
        <svg-icon
          v-if="node.level === 2"
          icon-class="document"
          class-name="svg-document"
        ></svg-icon>
        <router-link
          :to="getTreeHref(node.level, data.id)"
          class="font-600 text-secondary-color"
          :class="data.children ? 'text-secondary-color' : 'text-other-color'"
          target="_blank"
        >
          {{ data.id }}
        </router-link>
        <span v-if="data.number" class="font-600 text-other-color ml-05">{{
          `( ${data.number} )`
        }}</span>
      </template>
    </el-tree>
    <div v-else>
      <el-empty :image-size="100" description="No Data" />
    </div>
  </div>
</template>
<script setup>
  import {
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import { getRelatedExpNos } from '@/api/app/experiment';

  const { proxy } = getCurrentInstance();

  let no = proxy.$route.params.id;

  let props = defineProps({
    style: {
      type: Object,
      required: false,
    },
  });

  onMounted(() => {
    loadAssociation();
  });
  /** 加载关联信息 */
  let expNoTree = ref([]);

  const defaultProps = {
    children: 'children',
    label: 'id',
  };
  let children = reactive([]);
  let loading = ref(false);

  function loadAssociation() {
    loading.value = true;
    getRelatedExpNos(no)
      .then(response => {
        let data = response.data;
        for (let it of data) {
          children.push({ id: it, label: it });
        }
        expNoTree.value = [
          {
            id: 'Related Experiments',
            number: data.length,
            children: children,
          },
        ];
      })
      .finally(() => {
        loading.value = false;
      });
  }

  function getTreeHref(level, id) {
    if (level === 2) {
      return `/experiment/detail/${id}`;
    }
    return 'javascript:void(0)';
  }
</script>
<style scoped lang="scss">
  :deep(.el-tree-node__content):hover {
    background-color: #f9f9f9;
  }

  :deep(.el-tree-node__content) {
    //justify-content: center;
    border-radius: 12px;
    padding: 16px 0;
  }

  .svg-document {
    width: 13px;
    height: 14px;
    margin-right: 0.3rem;
  }
</style>
