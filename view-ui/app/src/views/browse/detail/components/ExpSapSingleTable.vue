<template>
  <div v-loading="tableLoading">
    <div class="d-flex align-items-center justify-space-between mb-1">
      <el-tag type="primary" round> {{ tableInfo.type }}</el-tag>
      <div class="d-flex hidden-xs-only">
        <div>
          <el-input
            v-model="queryParams.searchId"
            :placeholder="
              $t('browse.detail.components.expSapSingleTable.searchPlaceholder')
            "
            clearable
            style="width: 200px"
            @keyup.enter="findTableData"
          />
          <el-button
            class="radius-12 ml-1 mr-3"
            type="primary"
            @click="findTableData"
            >{{ $t('browse.detail.components.expSapSingleTable.search') }}
          </el-button>
        </div>

        <div>
          <ToolBar
            :key="`tb-${type}-${tableInfo.type}-${tableLoadingTimes}`"
            :columns="tableColumns"
            :width="470"
            :checkbox-width="200"
          ></ToolBar>

          <el-button
            :icon="Download"
            class="radius-12 ml-1 hidden-xs-only"
            type="primary"
            @click="downloadExpSample"
            >{{ $t('browse.detail.components.expSapSingleTable.export') }}
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      tooltip-effect="light"
      :data="tableData"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-style="{
        position: 'relative',
      }"
      class="data-list mb-2"
      border
      :default-sort="{ prop: genIdColumnProp(), order: 'ascending' }"
      @sort-change="tableSortChange"
    >
      <template v-for="(column, index) in tableColumns" :key="`col_${index}`">
        <el-table-column
          v-if="column.linkFlag && column.visible"
          :sortable="column.sortable"
          :prop="column.prop"
          :label="column.label"
          width="140"
        >
          <template #default="scope">
            <router-link :to="genLinkUrl(scope.row)" class="text-primary">
              {{ scope.row[genIdColumnProp()] }}
            </router-link>
          </template>
        </el-table-column>

        <el-table-column
          v-if="!column.linkFlag && column.visible"
          :sortable="column.sortable"
          :prop="column.prop"
          :label="column.label"
          :show-overflow-tooltip="column.showTooltip"
          :min-width="column.minWidth ? column.minWidth : 180"
        >
          <template #header>
            <span
              :data-cust-with="handleTitleWidth(column)"
              v-text="column.label"
            ></span>
          </template>
        </el-table-column>
      </template>
    </el-table>
    <pagination
      v-show="queryParams.total > 0"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      :total="queryParams.total"
      class="mb-1"
      @pagination="
        () => {
          findTableData();
        }
      "
    />
  </div>
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    ref,
    toRaw,
  } from 'vue';
  import { getExpAndSampleList } from '@/api/app/project';
  import { isArrEmpty, trimStr } from '@/utils';
  import { Download } from '@element-plus/icons-vue';

  const props = defineProps({
    prjNo: {
      type: String,
      required: true,
      default: () => '',
    },
    tableInfo: {
      type: Object,
      required: false,
      default: () => {
        return {
          type: '',
          columnList: [],
        };
      },
    },
    type: {
      type: String,
      required: true,
      default: () => 'exp',
    },
  });

  const { type, prjNo, tableInfo } = props;
  const { proxy } = getCurrentInstance();

  const isExpComp = computed(() => {
    return 'experiment' === type;
  });

  const tableLoading = ref(false);
  const tableLoadingTimes = ref(1);
  const queryParams = ref({
    projectNo: prjNo,
    type: type,
    dataType: '',
    searchId: '',
    total: 0,
    pageNum: 1,
    pageSize: 10,
    sortKey: genIdColumnProp(),
    sortType: 'asc',
  });

  const tableColumns = ref([]);
  //experiment genomic
  const tableData = ref([]);

  function genLinkUrl(row) {
    return isExpComp.value
      ? `/experiment/detail/${row.expNo}`
      : `/sample/detail/${row.sapNo}`;
  }

  function handleTitleWidth(column) {
    // 新建一个 span
    const span = document.createElement('span');
    // 设置表头名称
    span.innerText = column.label;
    // 临时插入 document
    document.body.appendChild(span);

    // 重点：获取 span 最小宽度，设置当前列，注意这里加了 20，字段较多时还是有挤压，且渲染后的 div 内左右 padding 都是 10，所以 +20 。（可能还有边距/边框等值，需要根据实际情况加上）
    let minWidth = span.getBoundingClientRect().width + 50;
    // 修改列最小宽度
    column.minWidth = minWidth;

    // 移除 document 中临时的 span
    document.body.removeChild(span);
    return minWidth;
  }

  function genIdColumnProp() {
    return isExpComp.value ? 'expNo' : 'sapNo';
  }

  // 设置列最小宽度初始值，之后会调用handleTitleWidth方法进一步计算最小宽度
  function initColumnWidth(columns) {
    columns.forEach(item => {
      let number = item.label.length - 20;
      number = number > 0 ? number : 0;
      item.minWidth = 200 + number * 6;
    });
  }

  function tableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryParams.value.sortKey = prop;
      queryParams.value.sortType = order === 'ascending' ? 'asc' : 'desc';
      findTableData();
    }
  }

  function initColumnInfo(prop, label, custom, linkFlag) {
    return {
      prop: prop,
      label: label,
      custom: custom,
      visible: true,
      showTooltip: true,
      sortable: true,
      minWidth: '',
      linkFlag: !!linkFlag,
    };
  }

  // 前端添加的列，其他列都是后端读取属性数据生成的
  function initCustomColumn(prop, label, linkFlag) {
    return initColumnInfo(prop, label, true, linkFlag);
  }

  // 将当前页，整列没有数据的列，设置为不可见
  function initColumnSelector() {
    // 添加当前页自定义属性列
    const tableDataList = tableData.value;
    const custTitle = new Map();
    const custField = 'customAttr';

    // other类型数据key前缀
    const otherFieldPre = 'other_#_';
    if (!isArrEmpty(tableDataList)) {
      tableDataList.forEach(item => {
        // 组学/样本类型为Other类型对应数据
        let customAttrOther = item.customAttrOther;
        if (customAttrOther) {
          for (const customAttrKey in customAttrOther) {
            custTitle.set(otherFieldPre + customAttrKey, customAttrKey);
          }
        }

        // 添加自定义属性列
        let customAttr = item[custField];
        // let customAttrDesc = item['customAttrDesc'];
        if (customAttr) {
          for (const customAttrKey in customAttr) {
            /*let lable;
            /*if (customAttrDesc) {
              lable = customAttrDesc[customAttrKey];
            }*/
            custTitle.set(customAttrKey, customAttrKey);
          }
        }
      });
    }

    if (custTitle.size > 0) {
      let existProps = tableColumns.value.map(x => x.prop);
      custTitle.forEach((value, key) => {
        let prop;
        if (key.startsWith(otherFieldPre)) {
          prop = `attributes.${key.replace(otherFieldPre, '')}`;
        } else {
          prop = `${custField}.${key}`;
        }

        if (!existProps.includes(prop)) {
          tableColumns.value.push(initColumnInfo(prop, value, false));
        }
      });
    }

    // 隐藏当前页整列都没有数据的列
    let len = tableColumns.value.length;
    for (let i = 0; i < len; i++) {
      let item = tableColumns.value[i];
      let dataLength = tableData.value.length;
      for (let j = 0; j < dataLength; j++) {
        if (!item.custom) {
          item.visible = false;
          let prop = item.prop;
          let row = tableData.value[j];
          let attrIndex = prop.indexOf('.');
          if (attrIndex > -1) {
            let key1 = item.prop.substring(0, attrIndex);
            let key2 = item.prop.substring(attrIndex + 1);
            if (trimStr(row[key1][key2])) {
              item.visible = true;
              break;
            }
          } else if (trimStr(row[item.prop])) {
            item.visible = true;
            break;
          }
        }
      }
    }
  }

  function findTableData() {
    let dataType = tableInfo.type;
    if (dataType) {
      queryParams.value.dataType = dataType;
      tableLoading.value = true;
      tableData.value = [];
      getExpAndSampleList(queryParams.value)
        .then(res => {
          if (res.code === 200) {
            let data = res.data;
            let tablePageData;
            if (isExpComp.value) {
              tablePageData = data.expTableData;
            } else {
              tablePageData = data.sapTableData;
            }
            queryParams.value.total = tablePageData.totalElements;
            tableData.value = tablePageData.content;
          } else {
            queryParams.value.total = 0;
          }
          initColumnSelector();
          tableLoadingTimes.value++;
          tableLoading.value = false;
        })
        .catch(() => {
          tableLoadingTimes.value++;
          tableLoading.value = false;
        });
    }
  }

  function downloadExpSample() {
    let dataType = tableInfo.type;
    if (dataType) {
      queryParams.value.dataType = dataType;
      proxy.download(
        '/app/project/exportExpAndSample',
        queryParams.value,
        `${prjNo}_${isExpComp.value ? 'Experiment' : 'Sample'}_${dataType}_${new Date().getTime()}.xlsx`,
      );
    }
  }

  onMounted(() => {
    const isExp = isExpComp.value;
    let columnList = [
      initCustomColumn(
        genIdColumnProp(),
        isExp ? 'Experiment ID' : 'Sample ID',
        true,
      ),
      initCustomColumn('name', isExp ? 'Experiment Name' : 'Sample Name'),
    ];
    /*if (isExp) {
      columnList.push(initCustomColumn('protocol', 'Experiment Protocol'));
    } else {
      columnList.push(initCustomColumn('tissue', 'Tissue'));
      columnList.push(initCustomColumn('organism', 'Organism'));
    }*/

    let toRawCols = toRaw(tableInfo.columnList);
    if (toRawCols) {
      columnList.push(...proxy.$_.cloneDeep(toRawCols));
    }
    initColumnWidth(columnList);
    tableColumns.value = columnList;
    nextTick(() => {
      findTableData();
    });
  });
</script>

<style scoped lang="scss">
  :deep(.el-tag__content) {
    font-size: 14px;
    font-weight: 600;
  }
</style>
