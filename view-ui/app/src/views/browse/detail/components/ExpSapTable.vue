<template>
  <div v-show="!isArrEmpty(allTables)" class="card mt-1">
    <span class="text-main-color font-600 font-18">
      {{ isExpComp ? 'Experiment List' : 'Sample List' }}
    </span>
    <template
      v-for="(item, index) in allTables"
      :key="`exp-sap-single-tb-${index}`"
    >
      <el-divider></el-divider>
      <ExpSapSingleTable
        :prj-no="prjNo"
        :type="type"
        :table-info="item"
      ></ExpSapSingleTable>
    </template>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue';
  import ExpSapSingleTable from '@/views/browse/detail/components/ExpSapSingleTable.vue';
  import { isArrEmpty } from '@/utils';

  const props = defineProps({
    prjNo: {
      type: String,
      required: true,
      default: () => '',
    },
    allTables: {
      type: Array,
      required: true,
      default: () => [],
    },
    type: {
      type: String,
      required: true,
      default: () => 'exp',
    },
  });

  const type = ref(props.type);
  const prjNo = ref(props.prjNo);
  const isExpComp = computed(() => {
    return 'experiment' === type.value;
  });
</script>

<style scoped lang="scss"></style>
