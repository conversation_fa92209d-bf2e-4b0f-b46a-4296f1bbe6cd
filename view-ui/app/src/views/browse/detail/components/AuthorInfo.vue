<template>
  <div class="card mt-1">
    <h3 class="text-main-color mr-1">
      {{ $t('browse.detail.components.authorInfo.title') }}
    </h3>
    <el-divider class="mt-05"></el-divider>
    <div class="d-flex flex-wrap gap-12">
      <div class="item">
        <span class="label">{{
          $t('browse.detail.components.authorInfo.submission')
        }}</span>
        <span class="content bg-gray">{{ name }}</span>
      </div>
      <div class="item">
        <span class="label">{{
          $t('browse.detail.components.authorInfo.affiliatedUnit')
        }}</span>
        <span class="content bg-gray" v-html="data.submitter.orgName"></span>
      </div>
      <div class="item">
        <span class="label">{{
          $t('browse.detail.components.authorInfo.createDate')
        }}</span>
        <span class="content bg-gray">{{
          parseTime(data.createDate, '{y}-{m}-{d}')
        }}</span>
      </div>
      <div class="item">
        <span class="label">{{
          $t('browse.detail.components.authorInfo.lastModified')
        }}</span>
        <span class="content bg-gray">{{
          parseTime(data.updateDate, '{y}-{m}-{d}')
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, defineProps, toRefs } from 'vue';
  import { parseTime } from '@/utils/nodeCommon';

  let props = defineProps({
    data: {
      type: Object,
      required: true,
      default: () => ({
        createDate: undefined,
        updateDate: undefined,
        submitter: {
          firstName: '',
          middleName: '',
          lastName: '',
          orgName: '',
        },
      }),
    },
  });

  let { data } = toRefs(props);

  const name = computed(() => {
    let userName = data.value.submitter.firstName;
    // if (data.value.submitter.middleName) {
    //   userName += ' ' + data.value.submitter.middleName;
    // }
    userName += ' ' + data.value.submitter.lastName;
    return userName;
  });
</script>

<style lang="scss" scoped>
  .item {
    margin-top: 0.5rem;
    width: 47%;
    display: flex;

    .label {
      color: #666666;
      font-weight: 600;
      display: inline-block;
      min-width: 120px;
    }

    .bg-gray {
      width: 100%;
      padding: 4px 15px;
      border-radius: 6px;
    }

    &:nth-child(odd) {
      margin-right: 50px;
    }
  }

  .bg-success {
    margin-left: 12px;

    &:hover {
      background-color: #4ec5bf;
    }
  }

  @media (max-width: 767px) {
    .item {
      width: 100%;
      flex-direction: column;
    }
  }
</style>
