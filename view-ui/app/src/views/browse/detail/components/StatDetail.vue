<template>
  <div v-show="showCardFlag" class="card mt-1">
    <span class="text-main-color font-600 font-16">Statistical Details</span>
    <el-divider class="mt-05 mb-05"></el-divider>
    <el-row :gutter="10" class="mb-1">
      <el-col
        v-for="(item, cidx) in data"
        :key="'card-' + cidx"
        :span="8"
        :xs="24"
        :sm="8"
      >
        <div v-if="item.data.length > 0">
          <div class="font-600 text-main-color line-before mb-1 mt-1">
            {{ item.title }}
          </div>
          <div class="d-flex gap-30 flex-wrap statistic-box">
            <div
              v-for="(it, index) in item.data"
              :key="index"
              class="statistic radius-12 bg-gray"
            >
              <p class="title font-16 text-main-color">
                <span
                  class="circle"
                  :style="{ 'background-color': getColor(index) }"
                ></span>
                {{ it.name }}
              </p>
              <div class="d-flex count-it">
                <div v-if="it.total != null">
                  <p class="text-secondary-color font-13">Counts</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.total)
                  }}</span>
                </div>
                <div v-if="it.sampleNum != null">
                  <p class="text-secondary-color font-13">Sample</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.sampleNum)
                  }}</span>
                </div>
                <div v-if="it.runNum != null">
                  <p class="text-secondary-color font-13">Run</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.runNum)
                  }}</span>
                </div>
                <div v-if="it.dataNum != null">
                  <p class="text-secondary-color font-13">Files</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.dataNum)
                  }}</span>
                </div>
                <div v-if="it.publicNum != null">
                  <p class="text-secondary-color font-13">Public</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.publicNum)
                  }}</span>
                </div>
                <div v-if="it.restrictedNum != null">
                  <p class="text-secondary-color font-13">Restricted</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.restrictedNum)
                  }}</span>
                </div>
                <div v-if="it.privateNum != null">
                  <p class="text-secondary-color font-13">Private</p>
                  <span class="font-24 font-600 text-main-color mr-05">{{
                    formatNumber(it.privateNum)
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup>
  import { ref, toRefs, watch } from 'vue';
  import { formatNumber } from '@/utils/nodeCommon';

  const colors = ['#E98BAD', '#A296B1', '#2680EB', '#88AFD9', '#8DCCDE'];

  let showCardFlag = ref(false);

  let props = defineProps({
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
  });

  let { data } = toRefs(props);
  const getColor = cardIndex => {
    return `${colors[cardIndex]}`;
  };
  // 监听data
  watch(
    data,
    newVal => {
      showCardFlag.value = newVal.some(item => item?.data.length > 0);
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style lang="scss" scoped>
  .statistic-box {
    max-height: 280px;
    overflow-y: scroll;
    background-color: #f9f9f9;
    border-radius: 12px 18px 18px 12px;
  }

  .statistic {
    //width: 340px;
    width: 97%;
    //box-shadow: 0 0 3px rgba(60, 72, 88, 0.2);
    padding: 10px 20px;

    .title {
      position: relative;
      padding-left: 15px;
      color: #333333 !important;
      margin-bottom: 0.5rem;

      .circle {
        position: absolute;
        left: 0;
        top: 53%;
        transform: translateY(-50%);
        display: inline-block;
        width: 7px;
        height: 7px;
        border-radius: 50%;
      }
    }

    .count-it {
      gap: 30px;
      margin-left: 14px;

      .font-13 {
        font-size: 13px;
      }
    }
  }

  :deep(.el-empty) {
    padding: 5px 0;
  }

  :deep(.el-empty__image) {
    height: 80px;
  }

  @media (max-width: 1750px) {
    .font-24 {
      font-size: 16px;
    }
    .count-it {
      gap: 25px;
    }
  }
</style>
