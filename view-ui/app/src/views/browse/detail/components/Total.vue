<template>
  <div class="card h-100">
    <h3>
      {{ $t('browse.detail.components.total.title') }}
      <span v-if="showTip" class="font-14 float-right">{{
        $t('browse.detail.components.total.tip')
      }}</span>
    </h3>
    <el-divider class="mt-05"></el-divider>
    <div v-for="it in data" :key="it.name" class="mt-1">
      <span class="total-name w-30 bg-gray">{{ it.name }}</span>
      <span class="total-number w-70 bg-gray text-primary font-600">{{
        formatNumber(it.number)
      }}</span>
    </div>
  </div>
</template>

<script setup>
  import { defineProps } from 'vue';
  import { formatNumber } from '@/utils/nodeCommon';

  defineProps({
    data: {
      type: Array,
      required: true,
    },
    showTip: {
      type: Boolean,
      required: true,
      default: true,
    },
  });
</script>

<style lang="scss" scoped>
  @media (max-width: 1750px) {
    .total-number,
    .total-name {
      padding: 8px !important;
      font-size: 16px !important;
    }
  }

  @media (max-width: 1450px) {
    .total-number,
    .total-name {
      padding: 8px !important;
      font-size: 14px !important;
    }
  }

  @media (max-width: 1250px) {
    .total-number,
    .total-name {
      padding: 8px !important;
      font-size: 12px !important;
    }
  }

  .total-name {
    display: inline-block;
    border-radius: 10px;
    font-size: 16px;
    padding: 11px 15px;
  }

  .total-number {
    display: inline-block;
    text-align: right;
    font-size: 20px;
    border-radius: 10px;
    padding: 8px 15px;
  }
</style>
