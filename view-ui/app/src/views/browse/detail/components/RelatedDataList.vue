<template>
  <div v-show="showCardFlag" v-loading="dataListLoading" class="card mt-1">
    <span class="text-main-color font-600 font-16">Data List</span>
    <div class="float-right">
      <tool-bar
        :columns="columns"
        :width="360"
        :checkbox-width="150"
      ></tool-bar>

      <el-button
        v-if="'project' === type"
        :icon="Download"
        class="radius-12 ml-1"
        type="primary"
        @click="downloadData"
        >Export
      </el-button>
    </div>
    <el-divider class="mb-1 mt-1"></el-divider>
    <el-table
      tooltip-effect="light"
      :data="dataList"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      :row-style="{
        position: 'relative',
      }"
      max-height="550"
      class="data-list"
      border
      @sort-change="relTableSortChange"
    >
      <template v-for="(item, index) in columns">
        <el-table-column
          v-if="item.visible"
          :key="'table-column' + index"
          :sortable="!item.notSort ? 'custom' : false"
          :sort-orders="['ascending', 'descending']"
          :sort-by="item.prop === 'readableFileSize' ? 'fileSize' : item.prop"
          :prop="item.prop"
          :label="item.label"
          show-overflow-tooltip
          :min-width="item.minWidth"
        >
          <template v-if="isRouteNo(item.prop)" #default="scope">
            <router-link
              :to="`/${routePath[item.prop]}/detail/${scope.row[item.prop]}`"
              class="text-primary"
              target="_blank"
            >
              {{ scope.row[item.prop] }}
            </router-link>
          </template>
          <template v-if="item.prop === 'security'" #default="scope">
            {{ scope.row.security }}
            <el-tooltip
              effect="light"
              :content="
                'Public Date: ' + parseTime(scope.row.publicDate, '{y}-{m}-{d}')
              "
              placement="bottom"
            >
              <span>
                <el-icon
                  v-if="
                    scope.row.security === 'Restricted' &&
                    scope.row.publicDate != null
                  "
                  class="ml-05 cursor-pointer"
                  color="#07BBB3"
                >
                  <Timer />
                </el-icon>
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
      </template>
      <!--<el-table-column label="One-click Analysis" min-width="160" fixed="right">
        <template #default="scope">
          <el-button icon="Promotion" link type="primary">{{ scope.row.analysis }}</el-button>
        </template>
      </el-table-column>-->
      <el-table-column label="Operate" width="110" fixed="right">
        <template #default="scope">
          <div v-if="scope.row.accessible" class="download-btn text-center">
            <el-tooltip content="html download">
              <img
                src="@/assets/images/btn-ico-h.png"
                alt=""
                class="download mr-05"
                @click="showHttpDownloadModal(scope.row)"
              />
            </el-tooltip>
            <el-tooltip content="sftp download">
              <img
                src="@/assets/images/btn-ico-f.png"
                alt=""
                class="download"
                @click="showSftpDownloadModal(scope.row)"
              />
            </el-tooltip>
          </div>
          <div v-else class="download-btn text-center">
            <img
              src="@/assets/images/btn-key.png"
              alt=""
              class="download mr-05"
              @click="openRequestToDialog(scope.row)"
            />
          </div>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="queryPageAndSort.totalCount > 0"
      v-model:page="queryPageAndSort.pageNum"
      v-model:limit="queryPageAndSort.pageSize"
      :page-sizes="[5, 10, 20, 50, 100]"
      class="mb-1 mt-2 justify-center"
      :total="queryPageAndSort.totalCount"
      @pagination="pageDataList"
    />
    <http-download-dialog ref="httpDownloadDialog"></http-download-dialog>
    <sftp-download-dialog ref="sftpDownloadDialog"></sftp-download-dialog>
    <request-to ref="requestToRef"></request-to>
  </div>
</template>
<script setup>
  import ToolBar from '@/components/toolBar.vue';
  import {
    defineExpose,
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import { parseTime } from '@/utils/nodeCommon';
  import { getDetailDataList } from '@/api/app/browseDetail';
  import RequestTo from '@/components/DataShare/RequestTo.vue';
  import HttpDownloadDialog from '@/views/browse/detail/components/HttpDownloadDialog.vue';
  import SftpDownloadDialog from '@/views/browse/detail/components/SftpDownloadDialog.vue';
  import { Download } from '@element-plus/icons-vue';
  import { isLogin } from '@/api/login';
  import { ElMessageBox } from 'element-plus';
  import useUserStore from '@/store/modules/user';

  const { proxy } = getCurrentInstance();

  let props = defineProps({
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    currId: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
  });

  let showCardFlag = ref(true);

  onMounted(() => {
    loadDataList();
  });

  const { currId, type, columns } = props;

  const dataListLoading = ref(false);

  const dataList = reactive([]);

  const queryPageAndSort = ref({
    sortKey: '',
    sortType: '',
    pageNum: 1,
    pageSize: 10,
    totalCount: 0,
  });

  /** 数据分页 */
  function pageDataList(pageData) {
    queryPageAndSort.value.pageSize = pageData.limit;
    queryPageAndSort.value.pageNum = pageData.page;
    loadDataList();
  }

  defineExpose({
    pageDataList,
  });

  // prop 可以悬挑
  function isRouteNo(prop) {
    let arr = ['projNo', 'expNo', 'sapNo'];
    return arr.indexOf(prop) !== -1;
  }

  let routePath = {
    projNo: 'project',
    expNo: 'experiment',
    sapNo: 'sample',
  };

  function relTableSortChange(column) {
    let { prop, order } = column;
    if (order) {
      queryPageAndSort.value.sortKey = prop;
      queryPageAndSort.value.sortType = order === 'ascending' ? 'asc' : 'desc';
      loadDataList();
    }
  }

  function loadDataList() {
    dataListLoading.value = true;
    let param = {
      type: type,
      typeNo: currId,
    };
    let pagePram = queryPageAndSort.value;
    // 请求参数和分页信息
    param = { ...param, ...pagePram };
    getDetailDataList(param)
      .then(response => {
        dataList.length = 0;
        let pageInfo = response.data;
        showCardFlag.value = pageInfo?.list.length > 0 || false;
        if (pageInfo) {
          queryPageAndSort.value.totalCount = pageInfo.total;
          dataList.push(...pageInfo.list);
        } else {
          queryPageAndSort.value.totalCount = 0;
        }
      })
      .finally(() => {
        dataListLoading.value = false;
      });
  }

  function downloadData() {
    let param = {
      type: type,
      typeNo: currId,
    };
    let pagePram = queryPageAndSort.value;
    // 请求参数和分页信息
    param = { ...param, ...pagePram };
    proxy.download(
      '/app/browseDetail/downloadData',
      param,
      `${currId}_Data_${new Date().getTime()}.xlsx`,
    );
  }

  /** http下载 */
  function showHttpDownloadModal(row) {
    proxy.$refs['httpDownloadDialog'].showHttpDownloadModal(row);
  }

  /** sftp下载 */
  function showSftpDownloadModal(row) {
    proxy.$refs['sftpDownloadDialog'].showSftpDownloadModal(row);
  }

  const openRequestToDialog = row => {
    isLogin().then(response => {
      // 判断用户当前是否登录
      if (!response.data) {
        ElMessageBox.confirm(
          'This data is restricted and requires prior approval for download. Please log in to check if you have the necessary permissions.',
          'Tip',
          {
            confirmButtonText: 'Login',
            cancelButtonText: 'Cancel',
            dangerouslyUseHTMLString: true,
          },
        ).then(() => {
          useUserStore().casLogin();
        });
      } else {
        proxy.$refs['requestToRef'].init('data', row.datNo);
      }
    });
  };
</script>

<style lang="scss" scoped>
  .downloadTable {
    :deep(.el-table td.el-table__cell div) {
      display: flex;
      align-items: center;
    }
  }

  .download-svg {
    width: 20px;
    height: 20px;
  }

  .el-icon {
    margin-right: 0.5rem;
  }

  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }

  :deep(.el-table td.el-table__cell:last-child div) {
    justify-content: center !important;
  }

  .download {
    width: 20px;
    cursor: pointer;
  }
</style>
