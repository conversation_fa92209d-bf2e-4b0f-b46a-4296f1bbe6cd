<template>
  <div class="card" :class="type === 'project' ? 'h-100' : ''">
    <div class="d-flex justify-space-between align-items-center">
      <div class="d-flex align-items-center">
        <h3 class="text-main-color mr-2">General Information</h3>
      </div>
      <div
        class="d-flex align-items-center pos-relative popover-btn hidden-xs-only"
      >
        <svg-icon
          icon-class="cite"
          class-name="svg-cite cursor-pointer"
          @click="dialogVisible = true"
        ></svg-icon>

        <el-tooltip
          placement="bottom"
          trigger="hover"
          content="Export Data Links"
        >
          <svg-icon
            icon-class="export-data-links"
            class-name="svg svg-export"
            @click="exportDataLink"
          ></svg-icon>
        </el-tooltip>

        <el-tooltip
          v-if="type === 'project' && (creator === member.id || !showOperation)"
          placement="bottom"
          trigger="hover"
          content="Gsa Data Export"
        >
          <svg-icon
            icon-class="gsa"
            class-name="svg svg-export"
            style="width: 30px; margin-left: 4px"
            @click="openGsaExportDialog"
          ></svg-icon>
        </el-tooltip>

        <el-tooltip
          v-if="type === 'project' && (creator === member.id || !showOperation)"
          placement="bottom"
          trigger="hover"
          content="Sra Data Export"
        >
          <svg-icon
            icon-class="sra"
            class-name="svg svg-export"
            style="
              width: 30px;
              margin-left: 4px;
              margin-right: 4px;
              padding-top: 2.5px;
            "
            @click="openSraExportDialog"
          ></svg-icon>
        </el-tooltip>

        <el-tooltip content="Edit">
          <svg-icon
            v-if="
              creator === member.id &&
              visibleStatus === 'Unaccessible' &&
              showOperation
            "
            icon-class="usercenterEdit"
            class-name="svg svg-edit"
            @click="
              () => router.push({ path: `/submit/metadata/${type}/edit/${no}` })
            "
          ></svg-icon>
        </el-tooltip>

        <el-tooltip
          v-if="creator === member.id && showOperation"
          placement="bottom"
          :width="200"
          trigger="hover"
          content="Security"
        >
          <svg-icon
            icon-class="security"
            class-name="svg svg-security"
            @click="openSecurityDialog"
          ></svg-icon>
        </el-tooltip>

        <el-tooltip
          v-if="
            creator === member.id &&
            shareStatus === ConfigEnum.Enable &&
            showOperation
          "
          placement="bottom"
          trigger="hover"
          content="Share To"
        >
          <svg-icon
            icon-class="shareTo"
            class-name="svg svg-share"
            @click="openShareToDialog"
          ></svg-icon>
        </el-tooltip>

        <el-tooltip
          v-if="
            btnShow.reviewBtn &&
            reviewStatus === ConfigEnum.Enable &&
            showOperation
          "
          placement="bottom"
          trigger="hover"
          content="Review To"
        >
          <svg-icon
            icon-class="review"
            class-name="svg svg-review"
            @click="openReviewToDialog"
          ></svg-icon>
        </el-tooltip>

        <el-tooltip
          v-if="
            btnShow.requestBtn &&
            requestStatus === ConfigEnum.Enable &&
            showOperation
          "
          placement="bottom"
          :width="200"
          trigger="hover"
          content="Request Restricted Datas"
        >
          <svg-icon
            icon-class="request"
            class-name="svg svg-request"
            @click="openRequestToDialog"
          ></svg-icon>
        </el-tooltip>
        <Security ref="securityRef"></Security>
        <ShareTo ref="shareToRef"></ShareTo>
        <ReviewTo ref="reviewToRef"></ReviewTo>
        <RequestTo ref="requestToRef"></RequestTo>
        <GsaExport ref="gsaExportRef"></GsaExport>
        <SraExport ref="sraExportRef"></SraExport>
      </div>
    </div>
    <el-divider class="mt-05 mb-1"></el-divider>
    <div class="d-flex justify-space-between flex-wrap">
      <template
        v-for="(item, index) in generalInfo"
        :key="'generalInfo-' + index"
      >
        <div
          v-if="item.value"
          class="item"
          :class="
            !$route.path.includes('/project') && (index === 0 || index === 1)
              ? 'w-48'
              : 'w-100'
          "
        >
          <template v-if="item.label === 'Project No'">
            <span class="label">Project ID</span>
            <div class="content bg-gray" style="text-align: justify">
              <router-link
                :to="`/project/detail/${item.value}`"
                class="text-primary"
                target="_blank"
              >
                {{ item.value }}
              </router-link>
            </div>
          </template>
          <template v-else>
            <span class="label">{{ item.label }}</span>
            <div
              class="content bg-gray"
              style="text-align: justify"
              v-html="item.value"
            ></div>
          </template>
        </div>
      </template>

      <div v-if="relatedLinks && relatedLinks.length !== 0" class="item w-100">
        <span class="label">Related Links</span>
        <div class="content bg-gray">
          <div v-for="(item, idx) in relatedLinks" :key="'relatedLinks' + idx">
            <a class="text-primary" :href="item" target="_blank">{{ item }} </a>
          </div>
        </div>
      </div>
    </div>
    <div v-if="publications.length !== 0" class="item">
      <span
        class="label align-items-center justify-space-between"
        style="display: flex !important"
        >Publications
      </span>

      <PreviewPublish
        :publish-data="publications"
        :show-title="false"
        :show-journal="false"
        :padding="false"
      ></PreviewPublish>
    </div>

    <el-dialog v-model="dialogVisible" width="780" title="How to Cite?">
      <div class="pointStatus" style="font-size: 16px">
        <span class="firstPoint">
          After successfully submitting your data to NODE, we recommend using
          the following wording to describe the data deposition in your
          manuscript: <br />
          <span style="font-style: italic"
            >All data are accessible in NODE (<a
              class="text-primary"
              target="_blank"
              :href="originUrl"
              >{{ originUrl }}</a
            >
            ) with the accession number {{ no }} or through the URL:
            <a class="text-primary" target="_blank" :href="hrefUrl">{{
              hrefUrl
            }}</a></span
          >
        </span>
        <div class="mt-1 secondPoint">
          Please cite the following publication:
        </div>
        <div class="mt-05">
          <!--          <svg-icon-->
          <!--            icon-class="document"-->
          <!--            class-name="svg svg-document"-->
          <!--          ></svg-icon>-->
          <strong
            >Advances in multi-omics big data sharing platform research.</strong
          >
          <span style="font-style: italic">
            Chinese Bulletin of Life Sciences.</span
          ><br />
          2023, 35(12): 1553-1560. DOI:
          <a
            target="_blank"
            href="https://lifescience.sinh.ac.cn/article.php?id=3716"
            class="text-primary cursor-pointer"
            >10.13376/j.cbls/2023169</a
          >
        </div>
        <div>
          <el-text v-if="visibleStatus === 'Unaccessible'" type="danger">
            * Tip: This {{ type }} is Unaccessible
          </el-text>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="copyText(hrefUrl)">
            Copy
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
  import {
    defineProps,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import PreviewPublish from '@/views/submit/metadata/rawData/common/PreviewPublish.vue';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { storeToRefs } from 'pinia';
  import useUserStore from '@/store/modules/user';
  import Security from '@/components/DataShare/Security.vue';
  import ShareTo from '@/components/DataShare/ShareTo.vue';
  import ReviewTo from '@/components/DataShare/ReviewTo.vue';
  import RequestTo from '@/components/DataShare/RequestTo.vue';
  import { showRequestButton } from '@/api/app/request';
  import { ConfigEnum } from '@/utils/enums';
  import { useRouter } from 'vue-router';
  import { getUrlQueryString } from '@/utils/nodeCommon';
  import { isStrBlank } from '@/utils';
  import GsaExport from '@/views/browse/detail/components/GsaExport.vue';
  import SraExport from '@/views/browse/detail/components/SraExport.vue';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  const { proxy } = getCurrentInstance();

  const requestStatus = proxy.getConfigVal(ConfigEnum.Request_Status);
  const shareStatus = proxy.getConfigVal(ConfigEnum.Share_Status);
  const reviewStatus = proxy.getConfigVal(ConfigEnum.Review_Status);

  const router = useRouter();

  let no = proxy.$route.params.id;
  let showOperation = ref(isStrBlank(getUrlQueryString('access-token')));

  const dialogVisible = ref(false);
  const props = defineProps({
    generalInfo: {
      type: Object,
    },
    creator: {
      type: String,
    },
    visibleStatus: {
      type: String,
      required: true,
    },
    publications: {
      type: Array,
    },
    relatedLinks: {
      type: Array,
      required: false,
    },
    type: {
      type: String,
      required: true,
    },
    typeId: {
      type: String,
      required: true,
    },
  });

  const generalInfo = reactive(props.generalInfo);
  const publications = reactive(props.publications);
  const relatedLinks = reactive(props.relatedLinks);

  const creator = ref(props.creator);
  let originUrl = ref('');
  let hrefUrl = ref('');

  const btnShow = ref({ requestBtn: false, reviewBtn: false });

  function copyText(text) {
    // 添加一个input元素放置需要的文本内容
    const input = document.createElement('input');
    input.value = text;
    document.body.appendChild(input);
    // 选中并复制文本到剪切板
    input.select();
    document.execCommand('copy');
    // 移除input元素
    document.body.removeChild(input);
    proxy.$modal.msgSuccess('Copy successfully');
  }

  onMounted(() => {
    let publicPath = import.meta.env.VITE_APP_PUBLIC_PATH;
    if (publicPath === '/' || !publicPath) {
      publicPath = '';
    }
    originUrl.value = window.location.origin + publicPath;
    hrefUrl.value = window.location.origin + window.location.pathname;

    initBtn();

    console.log(
      btnShow.value.reviewBtn,
      reviewStatus === ConfigEnum.Enable,
      showOperation,
    );
  });

  function initBtn() {
    showRequestButton({ type: props.type, typeNo: props.typeId }).then(
      response => {
        console.log(111, response.data);
        btnShow.value = response.data;
      },
    );
  }

  const openSecurityDialog = () => {
    proxy.$refs['securityRef'].init(props.type, props.typeId);
  };
  const openShareToDialog = () => {
    proxy.$refs['shareToRef'].init(props.type, props.typeId);
  };
  const openReviewToDialog = () => {
    proxy.$refs['reviewToRef'].init(props.type, props.typeId);
  };
  const openRequestToDialog = () => {
    proxy.$refs['requestToRef'].init(props.type, props.typeId);
  };
  const openGsaExportDialog = () => {
    proxy.$refs['gsaExportRef'].init(props.type, props.typeId);
  };
  const openSraExportDialog = () => {
    proxy.$refs['sraExportRef'].init(props.type, props.typeId);
  };

  /** 导出页面中data的下载链接 */
  function exportDataLink() {
    proxy.download(
      `/download/node/exportDownloadLink/${props.type}/${props.typeId}`,
      null,
      `${props.type}_${props.typeId}_data_download_link.zip`,
    );
  }
</script>

<style lang="scss" scoped>
  .top-1 {
    top: 1px;
  }

  .popover-btn {
    top: 8px;
  }

  .item {
    margin-top: 0.5rem;
    width: 100%;
    display: flex;

    .label {
      color: #666666;
      font-weight: 600;
      display: inline-block;
      min-width: 135px;
    }

    .bg-gray {
      width: 100%;
      padding: 4px 15px;
      border-radius: 6px;
    }
  }

  .bg-success {
    margin-left: 12px;

    &:hover {
      background-color: #4ec5bf;
    }
  }

  :deep(.el-dialog__body) {
    padding: 15px 20px;
    line-height: 2;
  }

  .w-48 {
    width: 48% !important;
  }

  .reference-name {
    color: #878787;
    font-style: italic;
  }

  .icon-link {
    transform: translateY(1px);
    margin-right: 0.3rem;
    margin-left: 0.3rem;
  }

  .reference {
    margin: 5px 0;
  }

  .svg {
    width: 35px;
    height: 35px;
    cursor: pointer;

    &:focus {
      outline: none;
    }
  }

  .svg-cite {
    width: 63px;
    height: 35px;
  }

  .svg-export {
    width: 29px;
    height: 30px;
    position: relative;
    top: -2px;
  }

  .svg-review {
    position: relative;
    top: -2px;
    width: 24px;
    margin-left: 0.2rem;
  }

  .svg-share {
    position: relative;
    top: -2px;
    width: 28px;
  }

  .svg-edit {
    top: -2px;
    margin-left: 0.2rem;
    position: relative;
    width: 23.5px;
  }

  .svg-security {
    position: relative;
    width: 34px;
  }

  .svg-request {
    position: relative;
    top: -2px;
    width: 29px;
    height: 28px;
    margin-left: 0.2rem;
  }

  .svg-document {
    color: gray;
    width: 20px;
    height: 20px;
  }

  .pointStatus {
    position: relative;
    .firstPoint::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      left: -16px;
      top: 13px;
      border-radius: 50%;
      background-color: #fe7f2b;
    }
    .secondPoint::before {
      content: '';
      width: 8px;
      height: 8px;
      position: absolute;
      left: -16px;
      top: 156px;
      border-radius: 50%;
      background-color: #fe7f2b;
    }
  }

  @media (max-width: 767px) {
    .item {
      flex-direction: column;
    }
  }
</style>
