<template>
  <div class="card mt-1">
    <h3 class="text-main-color font-600">
      {{ $t('browse.detail.components.relatedAnalysisList.title') }}
    </h3>
    <el-divider class="mt-05 mb-1"></el-divider>
    <el-table
      tooltip-effect="dark"
      :data="data"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      max-height="500px"
      border
    >
      <el-table-column
        prop="analNo"
        :label="
          $t('browse.detail.components.relatedAnalysisList.table.analysisId')
        "
        width="120"
        sortable
      >
        <template #default="scope">
          <router-link
            :to="`/analysis/detail/${scope.row.analNo}`"
            class="text-primary"
            target="_blank"
          >
            {{ scope.row.analNo }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="analName"
        :label="
          $t('browse.detail.components.relatedAnalysisList.table.analysisName')
        "
        sortable
        show-overflow-tooltip
      />
      <el-table-column
        prop="analType"
        :label="
          $t('browse.detail.components.relatedAnalysisList.table.analysisType')
        "
        sortable
      />
      <el-table-column
        prop="dataInfo"
        :label="$t('browse.detail.components.relatedAnalysisList.table.data')"
      >
        <template #default="scope">
          <el-tooltip
            :content="
              $t(
                'browse.detail.components.relatedAnalysisList.tooltip.clickDisplayDataInfo',
              )
            "
          >
            <el-tag
              type="success"
              class="cursor-pointer"
              @click="showTableInfoDialog(scope.row.analysisRelatedDatas)"
            >
              {{ scope.row.analysisRelatedDatas.length }}
            </el-tag>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="submissionDate"
        :label="
          $t(
            'browse.detail.components.relatedAnalysisList.table.submissionDate',
          )
        "
        width="160"
        sortable
      />
    </el-table>
    <el-dialog
      v-model="dialogTableVisible"
      :title="$t('browse.detail.components.relatedAnalysisList.dialog.title')"
      class="dialog radius-14"
      append-to-body
    >
      <el-divider class="mb-1 mt-1"></el-divider>
      <el-table
        tooltip-effect="dark"
        :data="analysisRelatedDatas"
        :header-cell-style="{
          backgroundColor: '#EDF3FD',
          color: '#333333',
          fontWeight: 700,
        }"
        max-height="500px"
        border
      >
        <el-table-column
          prop="datNo"
          :label="
            $t(
              'browse.detail.components.relatedAnalysisList.dialog.table.dataId',
            )
          "
          width="120"
          sortable
        >
        </el-table-column>
        <el-table-column
          prop="name"
          :label="
            $t(
              'browse.detail.components.relatedAnalysisList.dialog.table.dataName',
            )
          "
          sortable
          show-overflow-tooltip
        />
        <el-table-column
          prop="security"
          :label="
            $t(
              'browse.detail.components.relatedAnalysisList.dialog.table.dataSecurity',
            )
          "
          sortable
        />
        <el-table-column
          prop="fileSize"
          :label="
            $t(
              'browse.detail.components.relatedAnalysisList.dialog.table.dataSize',
            )
          "
          sortable
        >
          <template #default="scope">
            {{ scope.row.readableFileSize }}
          </template>
        </el-table-column>
        <el-table-column
          prop="uploadTime"
          :label="
            $t(
              'browse.detail.components.relatedAnalysisList.dialog.table.dataUploadTime',
            )
          "
          sortable
        >
          <template #default="scope">
            {{ parseTime(scope.row.uploadTime, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
  import { defineProps, getCurrentInstance, ref, toRefs } from 'vue';
  import { parseTime } from '../../../../utils/nodeCommon';

  const { proxy } = getCurrentInstance();

  let analysisRelatedDatas = ref([]);

  let dialogTableVisible = ref(false);

  let props = defineProps({
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
  });
  let { data } = toRefs(props);

  function showTableInfoDialog(dataList) {
    if (dataList.length <= 0) {
      proxy.$modal.msgWarning(
        proxy.$t(
          'browse.detail.components.relatedAnalysisList.messages.noDataFound',
        ),
      );
      return;
    }
    analysisRelatedDatas.value = dataList;
    dialogTableVisible.value = true;
  }
</script>

<style lang="scss" scoped>
  :deep(.customClass) {
    & > div {
      text-align: center !important;
    }
  }
</style>
