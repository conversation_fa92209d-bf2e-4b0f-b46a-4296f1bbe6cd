<template>
  <div class="card mt-1">
    <h3 class="text-main-color font-600">Related Analysis Information</h3>
    <el-divider class="mt-05 mb-1"></el-divider>
    <el-table
      tooltip-effect="dark"
      :data="data"
      :header-cell-style="{
        backgroundColor: '#EDF3FD',
        color: '#333333',
        fontWeight: 700,
      }"
      max-height="500px"
      border
    >
      <el-table-column prop="analNo" label="Analysis ID" width="120" sortable>
        <template #default="scope">
          <router-link
            :to="`/analysis/detail/${scope.row.analNo}`"
            class="text-primary"
            target="_blank"
          >
            {{ scope.row.analNo }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column
        prop="analName"
        label="Analysis Name"
        sortable
        show-overflow-tooltip
      />
      <el-table-column prop="analType" label="Analysis Type" sortable />
      <el-table-column prop="dataInfo" label="Data">
        <template #default="scope">
          <el-tooltip content="Click display Data Information">
            <el-tag
              type="success"
              class="cursor-pointer"
              @click="showTableInfoDialog(scope.row.analysisRelatedDatas)"
            >
              {{ scope.row.analysisRelatedDatas.length }}
            </el-tag>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        prop="submissionDate"
        label="Submission Date"
        width="160"
        sortable
      />
    </el-table>
    <el-dialog
      v-model="dialogTableVisible"
      title="Data Information"
      class="dialog radius-14"
      append-to-body
    >
      <el-divider class="mb-1 mt-1"></el-divider>
      <el-table
        tooltip-effect="dark"
        :data="analysisRelatedDatas"
        :header-cell-style="{
          backgroundColor: '#EDF3FD',
          color: '#333333',
          fontWeight: 700,
        }"
        max-height="500px"
        border
      >
        <el-table-column prop="datNo" label="Data ID" width="120" sortable>
        </el-table-column>
        <el-table-column
          prop="name"
          label="Data Name"
          sortable
          show-overflow-tooltip
        />
        <el-table-column prop="security" label="Data Security" sortable />
        <el-table-column prop="fileSize" label="Data Size" sortable>
          <template #default="scope">
            {{ scope.row.readableFileSize }}
          </template>
        </el-table-column>
        <el-table-column prop="uploadTime" label="Data Upload Time" sortable>
          <template #default="scope">
            {{ parseTime(scope.row.uploadTime, '{y}-{m}-{d}') }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
  import { defineProps, getCurrentInstance, ref, toRefs } from 'vue';
  import { parseTime } from '../../../../utils/nodeCommon';

  const { proxy } = getCurrentInstance();

  let analysisRelatedDatas = ref([]);

  let dialogTableVisible = ref(false);

  let props = defineProps({
    data: {
      type: Array,
      required: true,
      default: () => [],
    },
  });
  let { data } = toRefs(props);

  function showTableInfoDialog(dataList) {
    if (dataList.length <= 0) {
      proxy.$modal.msgWarning('No data is found!');
      return;
    }
    analysisRelatedDatas.value = dataList;
    dialogTableVisible.value = true;
  }
</script>

<style lang="scss" scoped>
  :deep(.customClass) {
    & > div {
      text-align: center !important;
    }
  }
</style>
