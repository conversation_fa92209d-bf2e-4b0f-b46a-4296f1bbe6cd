<template>
  <el-dialog
    v-model="sftpDialogShow"
    title="Ftp Download"
    width="600"
    class="radius-14"
  >
    <div class="integrity-body mb-1">
      <div class="d-flex align-items-center">
        <el-icon style="margin-right: 0.5rem" color="#8A6D3B" size="large">
          <WarnTriangleFilled />
        </el-icon>
        <span class="note font-600">Tips:</span>
        <span class="font-600 ml-05"
          >You can also use your own NODE account and password to download
          data</span
        >
      </div>
    </div>
    <div class="mb-05 text-center">
      <span class="text-main-color font-600 mr-1">via FTP address:</span>
      <span class="text-main-color" v-text="sftpContent"></span>
    </div>
    <div class="mb-05 text-center">
      <span class="text-main-color font-600 mr-1">via FTP filepath:</span>
      <span class="text-main-color" v-text="sftpFilePath"></span>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button type="primary" round @click="copyText(sftpFilePath)"
          >Copy Path
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
  import {
    defineExpose,
    getCurrentInstance,
    onMounted,
    reactive,
    ref,
  } from 'vue';

  import { noToFilePath } from '@/utils/nodeCommon';
  import { getConfigKey } from '@/api/system/config';

  const { proxy } = getCurrentInstance();
  defineExpose({
    showSftpDownloadModal,
  });
  onMounted(() => {
    getConfigKey('sftp.download.host').then(response => {
      let data = response.msg;
      if (data) {
        sftpData.host = data;
      }
    });
    getConfigKey('sftp.download.port').then(response => {
      let data = response.msg;
      if (data) {
        sftpData.port = data;
      }
    });
  });

  const sftpData = reactive({
    host: '',
    port: '',
  });
  /** sftp下载 */
  let sftpDialogShow = ref(false);
  let sftpContent = ref('');
  let sftpFilePath = ref('');

  function showSftpDownloadModal(row) {
    if (row.runNo) {
      // 能访问这个按钮一定会有权限
      if (row.security === 'Private') {
        sftpFilePath.value = '/Private/byRun' + '/' + row.runNo;
      } else {
        let filePath = noToFilePath(row.runNo);
        sftpFilePath.value = '/Public/byRun' + filePath;
      }
    }
    if (row.analNo) {
      if (row.security === 'Private') {
        sftpFilePath.value = '/Private/byAnalysis' + '/' + row.analNo;
      } else {
        let filePath = noToFilePath(row.analNo);
        sftpFilePath.value = '/Public/byAnalysis' + filePath;
      }
    }
    sftpContent.value = `sftp://${sftpData.host}:${sftpData.port}`;
    sftpDialogShow.value = true;
  }

  function copyText(text) {
    // 添加一个input元素放置需要的文本内容
    const input = document.createElement('input');
    input.value = text;
    document.body.appendChild(input);
    // 选中并复制文本到剪切板
    input.select();
    document.execCommand('copy');
    // 移除input元素
    document.body.removeChild(input);
    proxy.$modal.msgSuccess('Copy successfully');
  }
</script>

<style scoped lang="scss">
  .integrity-body {
    background-color: #fcf8e3;
    border: 1px solid #efe8c5;
    padding: 10px 15px;
    border-radius: 8px;
    color: #8f7443;
    text-align: justify;
    font-size: 14px;

    .note {
      color: #8a6d3b;
    }
  }
</style>
