<template>
  <div class="page">
    <div v-if="showContent" class="container-fluid">
      <Breadcrumb
        :bread-item="
          (userData ? $t('browse.detail.run.breadcrumb.my') + ' ' : '') +
          $t('browse.detail.run.breadcrumb.title')
        "
      />
      <div v-loading="loading" class="card mt-1">
        <el-result
          icon="warning"
          :title="
            showContent ? $t('browse.detail.run.content.accessPrompt') : ''
          "
        >
          <template #extra>
            <div class="d-flex row-gap-10 flex-wrap">
              <div class="id-list mr-1">
                <span class="btn-experiment">E</span>
                <router-link :to="'/experiment/detail/' + run.expNo">
                  {{ run.expNo }}
                </router-link>
              </div>
              <div class="id-list mr-1">
                <span class="btn-sample">S</span>
                <router-link :to="'/sample/detail/' + run.sapNo">
                  {{ run.sapNo }}
                </router-link>
              </div>
            </div>
          </template>
        </el-result>
      </div>
    </div>
    <div v-if="!showContent" v-loading="loading" class="container-fluid">
      <div class="card mt-1" style="height: 253px">
        <el-result
          v-if="!loading"
          icon="error"
          :title="$t('browse.detail.run.error.title')"
        >
          <template #sub-title>
            <div class="font-600">
              {{ errorContent }}
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import { computed, getCurrentInstance, onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { checkRunPermission, findRunByNo } from '@/api/app/run';
  import { isTokenAccess } from '@/utils/nodeCommon';
  import { storeToRefs } from 'pinia';
  import useUserStore from '@/store/modules/user';

  const { proxy } = getCurrentInstance();
  const route = useRoute();
  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);

  let showContent = ref(false);
  let errorContent = ref('');

  const runNo = ref('');

  onMounted(() => {
    runNo.value = route.params.id;

    getData();
  });

  const run = ref({});
  const creator = ref('');

  const userData = computed(() => {
    return creator.value === member.value.id || isTokenAccess();
  });

  const loading = ref(false);

  // 查询外层的请求列表
  function getData() {
    loading.value = true;
    checkRunPermission(runNo.value).then(response => {
      if (response.msg) {
        errorContent.value = response.msg;
        showContent.value = false;
        loading.value = false;
      } else {
        findRunByNo(runNo.value)
          .then(response => {
            run.value = response.data;
            creator.value = response.data.creator;
            showContent.value = true;
          })
          .finally(() => {
            loading.value = false;
          });
      }
    });
  }
</script>

<style lang="scss" scoped></style>
