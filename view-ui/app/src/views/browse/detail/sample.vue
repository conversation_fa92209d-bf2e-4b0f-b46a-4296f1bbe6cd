<template>
  <div class="page">
    <div v-if="showContent" class="container-fluid">
      <Breadcrumb :bread-item="(userData ? 'My ' : '') + 'Sample Details'" />
      <el-row :gutter="15" class="mt-1 row-gap-15">
        <el-col :span="18" :xs="24" :md="18">
          <div ref="leftHeight">
            <general-info
              :key="'general-info-key-' + generalInfoKey"
              v-loading="generalInfoLoading"
              :general-info="genInfo"
              :creator="creator"
              :visible-status="visibleStatus"
              :publications="publish"
              :related-links="relatedLinks"
              type="sample"
              :type-id="no"
            ></general-info>
            <attributes
              v-if="attrs.length !== 0"
              v-loading="generalInfoLoading"
              :data="attrs"
            ></attributes>
          </div>
        </el-col>
        <el-col :span="6" :xs="24" :md="6" class="hidden-xs-only">
          <div v-loading="associationLoading" class="card">
            <h3>Association</h3>
            <el-divider class="mt-05 mb-1"></el-divider>
            <el-tree
              v-if="
                treeData &&
                treeData.length === 1 &&
                treeData[0].children.length !== 0
              "
              :data="treeData"
              :props="defaultProps"
              default-expand-all
              :style="rightHeight"
            >
              <template #default="{ node, data }">
                <svg-icon
                  v-if="node.level === 3"
                  icon-class="document"
                  class-name="svg-document"
                ></svg-icon>

                <router-link
                  :to="getTreeHref(node.level, data.id)"
                  class="font-600"
                  :class="
                    node.level === 3
                      ? 'text-other-color'
                      : 'text-secondary-color'
                  "
                  target="_blank"
                  >{{ data.id }}
                </router-link>
                <span
                  v-if="data.number"
                  class="font-600 text-other-color ml-05"
                  >{{ `( ${data.number} )` }}</span
                >
              </template>
            </el-tree>
            <div v-else :style="rightHeight">
              <el-empty :image-size="100" description="No Data" />
            </div>
          </div>
        </el-col>
      </el-row>
      <stat-detail
        v-loading="statDetailLoading"
        :data="statDetailData"
      ></stat-detail>
      <related-data-list
        ref="dataListRef"
        :key="'sap-data-list-key-' + dataListKey"
        :columns="columns"
        :curr-id="currId"
        :type="'sample'"
      ></related-data-list>
      <related-data-qc-info-list
        :key="'qc-list-key-' + dataListKey"
        :curr-id="currId"
        :type="'sample'"
      ></related-data-qc-info-list>
      <related-analysis-list
        v-if="analysisList.length > 0"
        v-loading="analListLoading"
        :data="analysisList"
      />
      <author-info
        v-loading="authorInfoLoading"
        :data="authorData"
      ></author-info>
    </div>
    <div v-if="!showContent" class="container-fluid">
      <div class="card mt-1">
        <el-result icon="error" title="Error">
          <template #sub-title>
            <div class="font-600">
              {{ errorContent }}
            </div>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '../../../components/breadcrumb.vue';
  import GeneralInfo from './components/GeneralInfo.vue';
  import Attributes from './components/attributes.vue';

  import AuthorInfo from './components/AuthorInfo.vue';
  import RelatedDataQcInfoList from '@/views/browse/detail/components/RelatedDataQcInfoList.vue';

  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    reactive,
    ref,
  } from 'vue';
  import {
    checkSamplePermission,
    getAuthorInfo,
    getRelatedAnalysis,
    getRelatedSapNos,
    getSampleGeneralInfo,
    getStatDetail,
  } from '@/api/app/sample';
  import RelatedDataList from '@/views/browse/detail/components/RelatedDataList.vue';
  import StatDetail from '@/views/browse/detail/components/StatDetail.vue';
  import RelatedAnalysisList from '@/views/browse/detail/components/RelatedAnalysisList.vue';
  import useUserStore from '@/store/modules/user';

  import { storeToRefs } from 'pinia';
  import { isTokenAccess } from '@/utils/nodeCommon';

  const userStore = useUserStore();
  const { member } = storeToRefs(userStore);
  const { proxy } = getCurrentInstance();

  const rightHeight = ref({
    overflowY: 'auto',
    height: '500px',
  });
  let showContent = ref(true);
  let errorContent = ref('');

  let no = proxy.$route.params.id;
  let currId = ref(no);
  onMounted(() => {
    checkSamplePermission(no).then(response => {
      if (response.msg) {
        errorContent.value = response.msg;
        // console.log(errorContent.value);
        showContent.value = false;
      } else {
        loadGeneralInfo();
        loadAssociation();
        loadStatDetail();
        loadRelatedAnalysisList();
        loadAuthorInfo();
      }
    });
  });

  const userData = computed(() => {
    return creator.value === member.value.id || isTokenAccess();
  });

  /** 加载基本信息 */
  let genInfo = reactive([]);
  const publish = ref([]);
  let creator = ref('');
  const visibleStatus = ref('');
  let relatedLinks = ref([]);
  let attrs = ref([]);
  let generalInfoKey = ref(0);
  let attrsKey = ref(0);
  let generalInfoLoading = ref(false);

  function loadGeneralInfo() {
    generalInfoLoading.value = true;
    getSampleGeneralInfo(no)
      .then(response => {
        currId.value = response.data.sapNo;
        dataListKey.value++;
        genInfo.push({
          label: 'Sample ID',
          value: response.data.sapNo,
        });
        genInfo.push({
          label: 'Sample Type',
          value: response.data.subjectType,
        });
        genInfo.push({
          label: 'Sample Name',
          value: response.data.name,
        });
        genInfo.push({
          label: 'Organism',
          value: response.data.organism,
        });
        genInfo.push({
          label: 'Tissue',
          value: response.data.tissue,
        });
        genInfo.push({
          label: 'Subject Id',
          value: response.data.subjectId,
        });
        genInfo.push({
          label: 'Description',
          value: response.data.description,
        });
        // 历史usedIds
        if (response.data?.usedIds) {
          const joinedString = response.data?.usedIds.join(';');
          genInfo.push({
            label: 'Used Ids',
            value: joinedString,
          });
        }
        genInfo.push({
          label: 'Protocol',
          value: response.data.protocol,
        });
        publish.value = response.data.publishes;
        creator.value = response.data.creator;
        visibleStatus.value = response.data.visibleStatus;
        relatedLinks.value = response.data.relatedLinks;
        attrs.value = response.data.attributes;
      })
      .finally(() => {
        generalInfoKey.value++;
        attrsKey.value++;

        nextTick().then(() => {
          const divHeight = proxy.$refs['leftHeight'].clientHeight;
          rightHeight.value.height = divHeight - 95 + 'px';
        });
        generalInfoLoading.value = false;
      });
  }

  /** 加载关联信息 */
  let treeData = ref([]);
  let associationLoading = ref(false);
  const defaultProps = {
    children: 'children',
    label: 'id',
  };

  function loadAssociation() {
    associationLoading.value = true;
    getRelatedSapNos(no)
      .then(response => {
        let data = response.data;
        let children = [];
        for (let key of Object.keys(data)) {
          let id = key;
          let subChild = data[key].map(it => {
            return { id: it };
          });
          children.push({
            id: id,
            number: subChild.length,
            children: subChild,
          });
        }

        treeData.value = [
          {
            id: 'Related Project',
            number: data.length,
            children: children,
          },
        ];
      })
      .finally(() => {
        // console.log(treeData.value[0].children.length);
        associationLoading.value = false;
      });
  }

  function getTreeHref(level, id) {
    if (level === 3) {
      return `/sample/detail/${id}`;
    }
    if (level === 2) {
      return `/project/detail/${id}`;
    }
    return 'javascript:void(0)';
  }

  /** 加载统计信息 */
  let statDetailData = reactive([]);
  let statDetailLoading = ref(false);

  function loadStatDetail() {
    statDetailLoading.value = true;
    getStatDetail(no)
      .then(response => {
        statDetailData.push({
          title: 'Cases and File Counts by Experimental Type',
          data: response.data.expStats,
        });
        statDetailData.push({
          title: 'File types and access restrictions',
          data: response.data.dataStats,
        });
      })
      .finally(() => {
        statDetailLoading.value = false;
      });
  }

  /** 加载data list */
  let dataListKey = ref(0);
  // 列显隐信息
  const columns = ref([
    { prop: 'projNo', minWidth: 120, label: `Project ID`, visible: true },
    { prop: 'projName', minWidth: 170, label: `Project Name`, visible: true },
    {
      prop: 'projDesc',
      minWidth: 170,
      label: `Project Description`,
      visible: false,
    },
    { prop: 'expNo', minWidth: 140, label: `Experiment ID`, visible: true },
    {
      prop: 'expName',
      minWidth: 170,
      label: `Experiment Name`,
      visible: false,
    },
    { prop: 'expType', minWidth: 170, label: `Experiment Type`, visible: true },
    {
      prop: 'expDesc',
      minWidth: 200,
      label: `Experiment Description`,
      visible: false,
    },
    { prop: 'runNo', minWidth: 120, label: `Run ID`, visible: true },
    { prop: 'runName', minWidth: 170, label: `Run Name`, visible: true },
    { prop: 'datNo', minWidth: 120, label: `Data ID`, visible: true },
    { prop: 'name', minWidth: 170, label: `Data Name`, visible: true },
    { prop: 'security', minWidth: 140, label: `Data Security`, visible: true },
    {
      prop: 'readableFileSize',
      minWidth: 110,
      label: `Data Size`,
      visible: false,
    },
    {
      prop: 'uploadTime',
      minWidth: 170,
      label: `Data Upload Time`,
      visible: false,
    },
  ]);

  /** 加载相关联的analysis */
  let analysisList = reactive([]);
  let analListLoading = ref(false);

  function loadRelatedAnalysisList() {
    analListLoading.value = true;
    getRelatedAnalysis(no)
      .then(response => {
        analysisList.push(...response.data);
      })
      .finally(() => {
        analListLoading.value = false;
      });
  }

  /** 加载用户信息 */
  let authorData = ref({
    createDate: undefined,
    updateDate: undefined,
    submitter: {
      firstName: '',
      middleName: '',
      lastName: '',
      orgName: '',
    },
  });
  let authorInfoLoading = ref(false);

  function loadAuthorInfo() {
    authorInfoLoading.value = true;
    getAuthorInfo(no)
      .then(response => {
        authorData.value = response.data;
      })
      .finally(() => {
        authorInfoLoading.value = false;
      });
  }
</script>

<style lang="scss" scoped>
  :deep(.el-tree-node__content):hover {
    background-color: #f9f9f9;
  }

  :deep(.el-tree-node__content) {
    border-radius: 12px;
    padding: 16px 0;
  }

  .svg-document {
    width: 13px;
    height: 14px;
    margin-right: 0.3rem;
  }
</style>
