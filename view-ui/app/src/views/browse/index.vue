<template>
  <div class="page browse-outer-dev">
    <div class="container-fluid">
      <Breadcrumb
        :bread-item="
          toAdvanced
            ? $t('browse.index.breadcrumb.advancedSearch')
            : $t('browse.index.breadcrumb.browse')
        "
      ></Breadcrumb>
      <transition name="animation" mode="out-in">
        <div v-show="!toAdvanced" class="mb-2 mt-1">
          <div class="d-flex align-items-center justify-space-between">
            <div class="browse-search w-75 mr-2">
              <el-input
                v-model="searchInput"
                :placeholder="$t('browse.index.search.placeholder')"
                @keyup.enter="fullTextSearch"
              />
              <el-icon
                size="large"
                class="cursor-pointer"
                @click="searchInput = ''"
              >
                <Close />
              </el-icon>
              <el-divider direction="vertical"></el-divider>
              <el-button
                type="warning"
                round
                class="bg-round-warning ml-05"
                @click="fullTextSearch"
              >
                <el-icon size="large">
                  <Search />
                </el-icon>
              </el-button>
            </div>
            <el-button
              type="primary"
              round
              class="advanced-btn hidden-xs-only"
              @click="toAdvanced = true"
              >{{ $t('browse.index.search.advancedSearch') }}
            </el-button>
          </div>
          <div class="mt-1 eg">
            <span class="mr-1">{{ $t('browse.index.search.example') }}</span>
            <span
              v-for="item in example"
              :key="item"
              class="text-primary font-600 mr-2 cursor-pointer hidden-xs-only"
              @click="searchInput = item"
              >{{ item }}</span
            >
            <span
              v-for="(item, index) in example"
              v-show="index < 2"
              :key="item"
              class="text-primary font-600 mr-2 hidden-sm-and-up cursor-pointer"
              @click="searchInput = item"
              >{{ item }}</span
            >
          </div>
        </div>
      </transition>
      <transition name="animation" mode="out-in">
        <div v-show="toAdvanced" class="card mb-1 mt-1">
          <h3 class="text-main-color mb-1">
            {{ $t('browse.index.advancedSearch.title') }}
          </h3>
          <div
            v-show="!searchEditing"
            class="bg-gray search-edit"
            v-text="searchBuilderStr ? searchBuilderStr : ''"
          ></div>
          <el-input
            v-show="searchEditing"
            v-model="searchEditStr"
            :rows="4"
            :disabled="!searchEditing"
            type="textarea"
            :placeholder="$t('browse.index.advancedSearch.builderPlaceholder')"
          ></el-input>
          <div class="d-flex justify-space-between mt-1">
            <el-button
              type="primary"
              class="btn-primary btn btn-s btn-shadow"
              round
              @click="editOrCancel"
              >{{
                searchEditing
                  ? $t('browse.index.advancedSearch.cancel')
                  : $t('browse.index.advancedSearch.edit')
              }}
            </el-button>
            <el-button
              class="btn-primary btn btn-round"
              round
              @click="clearBuilder"
              >{{ $t('browse.index.advancedSearch.clear') }}
            </el-button>
          </div>
          <div
            v-show="!searchEditing"
            class="radius-12 plr-20 builder pos-relative mb-1 mt-1"
            :class="{
              'bg-gray': !searchEditing,
              'bg-disable': searchEditing,
            }"
          >
            <h3 class="text-secondary-color mb-1">
              {{ $t('browse.index.advancedSearch.builder') }}
            </h3>
            <div
              v-for="(it, index) in buildValues"
              :key="'bld_' + index"
              :class="[
                index === 0 ? '' : 'mt-1',
                'd-flex',
                'align-items-center',
              ]"
            >
              <div v-if="index === 0" class="w-30 mr-1"></div>
              <el-select
                v-else
                v-model="it.relation"
                :teleported="false"
                class="w-30 radius-12 m-2 mr-1"
              >
                <el-option
                  :key="'rela' + index + 'AND'"
                  label="AND"
                  value="AND"
                />
                <el-option :key="'rela' + index + 'OR'" label="OR" value="OR" />
                <el-option
                  :key="'rela' + index + 'NOT'"
                  label="NOT"
                  value="NOT"
                />
              </el-select>

              <!-- 字段下拉框 -->
              <el-select
                v-model="it.queryField"
                :teleported="false"
                :filterable="true"
                class="m-2 w-40"
                :placeholder="$t('browse.index.advancedSearch.pleaseSelect')"
                @change="cleanValFiled($event, index)"
              >
                <el-option-group
                  v-for="(group, groupIndex) in builderOpt"
                  :key="'fd_gr_' + index + groupIndex + group.label"
                  :label="$t(group.labelKey)"
                >
                  <el-option
                    v-for="(item, itemIndex) in group.options"
                    :key="
                      'fd_opt_' + index + groupIndex + itemIndex + item.value
                    "
                    :label="item.label || $t(item.labelKey)"
                    :value="item.value"
                  />
                </el-option-group>
              </el-select>

              <!-- 值域框 start -->
              <el-date-picker
                v-if="it.type === 'date'"
                v-model="it.inputValue"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                type="daterange"
                :range-separator="
                  $t('browse.index.advancedSearch.datePicker.to')
                "
                :start-placeholder="
                  $t('browse.index.advancedSearch.datePicker.startDate')
                "
                :end-placeholder="
                  $t('browse.index.advancedSearch.datePicker.endDate')
                "
                class="ml-1"
              />

              <el-select
                v-else-if="it.type === 'access'"
                v-model="it.inputValue"
                class="ml-1"
                :placeholder="$t('browse.index.advancedSearch.pleaseSelect')"
              >
                <el-option
                  v-for="(item, itemIndex) in accessTypes"
                  :key="'acc' + index + itemIndex + item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>

              <el-select
                v-else-if="it.type === 'level'"
                v-model="it.inputValue"
                :teleported="false"
                class="ml-1"
                :placeholder="$t('browse.index.advancedSearch.pleaseSelect')"
              >
                <el-option
                  v-for="(item, itemIndex) in levelTypes"
                  :key="'lvl' + index + itemIndex + item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>

              <el-select
                v-else-if="it.type === 'bool'"
                v-model="it.inputValue"
                :teleported="false"
                class="ml-1"
                :placeholder="$t('browse.index.advancedSearch.pleaseSelect')"
              >
                <el-option
                  :key="'bool_' + index + 'true'"
                  :label="$t('browse.index.advancedSearch.boolOptions.yes')"
                  value="Yes"
                />
                <el-option
                  :key="'bool_' + index + 'false'"
                  :label="$t('browse.index.advancedSearch.boolOptions.no')"
                  value="No"
                />
              </el-select>

              <div
                v-else-if="it.type === 'numRange'"
                class="d-flex justify-space-between ml-1"
                style="width: 100%"
              >
                <el-input-number
                  v-model="it.rangeValue1"
                  :min="0"
                  style="width: 46%"
                  :controls="false"
                />
                <div style="padding: 3px">-</div>
                <el-input-number
                  v-model="it.rangeValue2"
                  :min="0"
                  style="width: 46%"
                  :controls="false"
                />
              </div>

              <!--值域框 默认为自动补全输入框-->
              <div v-else class="ml-1" style="width: 100%">
                <el-autocomplete
                  v-model="it.inputValue"
                  :teleported="false"
                  :fetch-suggestions="
                    (queryString, cb) => {
                      queryFieldSearch(queryString, cb, index);
                    }
                  "
                  clearable
                  style="width: 100%"
                />
              </div>
              <!-- 值域框 end -->

              <div style="width: 220px">
                <el-button
                  :class="index === 0 ? 'icon-minus ml-1' : 'ml-1'"
                  circle
                  type="warning"
                  plain
                  @click="removeParam(index)"
                >
                  <el-icon>
                    <Minus />
                  </el-icon>
                </el-button>

                <el-button
                  v-if="index === buildValues.length - 1"
                  type="primary"
                  class="ml-2"
                  circle
                  plain
                  @click="addParam"
                >
                  <el-icon>
                    <Plus />
                  </el-icon>
                </el-button>
              </div>

              <!--<el-button class="btn-round-primary" round>
                <span class="integrity">Show Index List</span>
              </el-button>-->
            </div>
          </div>
          <div class="text-center">
            <el-button
              type="warning"
              round
              plain
              class="builder-search mb-05 mt-05"
              @click="doAdvSearch"
              >{{ $t('browse.index.advancedSearch.search') }}
            </el-button>
            <el-button
              round
              plain
              class="btn-primary go-back btn btn-round"
              @click="toAdvanced = false"
              >{{ $t('browse.index.advancedSearch.close') }}
            </el-button>
          </div>
        </div>
      </transition>
      <div class="d-flex mt-1">
        <div class="w-25 hidden-xs-only pos-relative mb-1 mr-1">
          <div class="card">
            <h3 class="text-main-color mb-0 mt-0">
              {{ $t('browse.index.filters.access') }}
            </h3>
            <el-checkbox-group
              :key="'access_cb_gr'"
              v-model="accessRadio"
              class="d-flex flex-column w-100 align-flex-start ml-4"
              @change="accessChange"
            >
              <el-checkbox
                v-for="(item, index) in accessItems"
                :key="'access_cb' + index + item.name"
                :label="item.name"
                size="large"
                class="w-100 access-radio"
              >
                <div class="d-flex justify-space-between">
                  <span
                    class="upper-first text-secondary-color font-600"
                    v-text="item.name"
                  ></span>
                  <span
                    class="text-other-color font-600"
                    v-text="item.number"
                  ></span>
                </div>
              </el-checkbox>
            </el-checkbox-group>
          </div>
          <el-divider class="mb-05 mt-1"></el-divider>
          <div
            v-for="(item, index) in leftStatData"
            :key="'left_stat_data' + index"
            :class="index === 0 ? 'mt-1' : 'mt-1-5'"
          >
            <div v-show="item.isShow">
              <div
                v-if="item.data && item.data.length > 0"
                class="drop-down-title cursor-pointer"
                @click="expandLeftTree(index, initTreeRef(index, item.name))"
              >
                <span class="mr-05">{{ item.name }}</span>
                <span
                  v-if="item.number !== null && item.number !== undefined"
                  >{{ `(${item.number})` }}</span
                >
                <el-icon class="float-right cursor-pointer">
                  <ArrowDown v-if="item.isExpand" />
                  <ArrowRight v-else />
                </el-icon>
              </div>
              <el-collapse-transition v-if="item.data && item.data.length > 0">
                <div v-show="item.isExpand" class="drop-down-content plr-20">
                  <el-tree
                    :ref="initTreeRef(index, item.name)"
                    :key="'leftStatTree_' + index"
                    style="overflow-y: auto; max-height: 300px"
                    :data="trimmedData(item.data, index)"
                    :default-checked-keys="item.defaultCheckedKeys"
                    :default-expanded-keys="item.defaultExpandedKeys"
                    show-checkbox
                    node-key="id"
                    :props="treeProps"
                    @check="
                      (data, checkInfo) =>
                        handleTreeCheck(
                          data,
                          checkInfo,
                          initTreeRef(index, item.name),
                        )
                    "
                  >
                    <template #default="{ data }">
                      <exp-type
                        v-if="item.name === 'Experiment Type'"
                        :key="'left-exp-type-' + index"
                        :type="lowCaseExpIconInfo[data.name]?.iconName"
                        :color="lowCaseExpIconInfo[data.name]?.iconColor"
                        :full-name="lowCaseExpIconInfo[data.name]?.name"
                      ></exp-type>
                      <span
                        :class="{ 'upper-first': item.upperFirst }"
                        class="font-600 text-secondary-color"
                        >{{ $text(data.name, 30) }}</span
                      >
                      <span
                        v-if="data.number !== null && data.number !== undefined"
                        class="font-600 text-other-color ml-05"
                        >{{ `(${formatNumber(data.number)})` }}</span
                      >
                    </template>
                  </el-tree>
                  <div
                    v-if="item.data.length > 5"
                    class="more font-14 text-align-right text-warning cursor-pointer"
                    style="min-width: 100px"
                    @click="toggleExpand(item.data, index)"
                  >
                    {{
                      Expanded(item.data, index)
                        ? $t('browse.index.filters.more')
                        : $t('browse.index.filters.less')
                    }}
                  </div>
                </div>
              </el-collapse-transition>
            </div>
          </div>
          <el-popover
            :visible="visible"
            :width="300"
            trigger="click"
            placement="right"
          >
            <template #reference>
              <div
                class="drop-down-title main-btn-waring mt-1-5 cursor-pointer"
                @click="visible = !visible"
              >
                <span class="mr-05">{{
                  $t('browse.index.filters.showAdditionalFilters')
                }}</span>
              </div>
            </template>
            <div class="text-align-center text-main-color font-600">
              {{ $t('browse.index.filters.additionalFilters') }}
            </div>
            <el-checkbox-group
              :key="'addFil_gr'"
              v-model="checkList"
              class="d-flex flex-wrap"
            >
              <template v-for="item in leftStatData">
                <el-checkbox
                  v-if="item.data && item.data.length > 0"
                  :key="'addFil' + item.name"
                  :label="item.name"
                  >{{ item.name }}
                </el-checkbox>
              </template>
            </el-checkbox-group>
            <div class="d-flex mt-1 justify-center">
              <el-button
                type="primary"
                class="popover-btn"
                :size="'small'"
                @click="selectCondition"
                >{{ $t('browse.index.filters.confirm') }}
              </el-button>
            </div>
          </el-popover>
        </div>
        <div class="card w-75 self-flex-start browse-list mb-1">
          <el-tabs v-model="mode" type="card">
            <el-tab-pane
              :label="$t('browse.index.tabs.verboseMode')"
              name="verbose"
              class="verbose"
            >
              <div class="mb-2">
                <!--排序按钮-->
                <div class="bg-primary sort hidden-xs-only">
                  <template
                    v-for="(item, index) in sortBtn"
                    :key="'sort_btn' + item.field"
                  >
                    <el-button
                      v-if="item.show"
                      plain
                      :class="{ active: item.highlighted }"
                      @click="toggleSortOrder(index)"
                      >{{ $t(item.labelKey) }}
                      <el-icon v-if="item.highlighted">
                        <Bottom v-if="item.sortOrder === 'desc'" />
                        <Top v-else />
                      </el-icon>
                    </el-button>
                  </template>
                </div>

                <el-divider class="mb-1 mt-1"></el-divider>

                <div
                  v-for="(item, index) in browseListData"
                  :key="'browse_card_' + index"
                >
                  <div>
                    <el-row :gutter="10">
                      <el-col :span="20" class="d-flex align-items-center">
                        <!--类型-->
                        <el-tag
                          type="warning"
                          round
                          class="mr-05 tag-warning"
                          :class="computedTagClass(item.type)"
                          >{{ computedTagText(item.type) }}
                        </el-tag>
                        <!--标题-->
                        <a
                          :href="toDetailUrl(item.id, item.typeEnum)"
                          target="_blank"
                          class="font-600 text-warning name cursor-pointer"
                        >
                          {{ $text(item.name) }}
                        </a>
                      </el-col>

                      <!--ID 和 Used ID-->
                      <el-col
                        :span="4"
                        class="d-flex align-items-center justify-end"
                      >
                        <el-tooltip
                          v-if="item.usedIds && item.usedIds.length > 0"
                          :content="
                            $t('browse.index.content.usedId') +
                            item.usedIds.join('; ')
                          "
                          placement="left"
                          effect="light"
                        >
                          <svg-icon
                            icon-class="udesID"
                            class-name="svg-usedID"
                          ></svg-icon>
                          <el-icon color="#3A78E8" class="cursor-pointer"
                            ><Connection
                          /></el-icon>
                        </el-tooltip>
                        <span class="font-600 text-main-color float-right">
                          {{ item.id }}</span
                        >
                      </el-col>
                    </el-row>

                    <el-row class="mt-05 mb-05">
                      <el-col :span="16" class="d-flex" :xs="24" :sm="16">
                        <div class="d-flex justify-space-between mt-05">
                          <div class="d-flex">
                            <div class="d-flex align-items-center">
                              <el-icon color="#07BCB4">
                                <Avatar />
                              </el-icon>
                              <span class="text-other-color ml-05">{{
                                truncStr(item.submitter)
                              }}</span>
                            </div>
                            <div class="d-flex align-items-center ml-2">
                              <svg-icon
                                icon-class="calendar"
                                class-name="svg-data"
                              ></svg-icon>
                              <span class="text-other-color ml-05">{{
                                truncDate(item.lastModified)
                              }}</span>
                            </div>
                            <div
                              v-if="
                                item.publishVOs &&
                                item.publishVOs.length > 0 &&
                                item.publishVOs[0].doi
                              "
                              class="d-flex align-items-center ml-2"
                            >
                              <svg-icon
                                icon-class="link"
                                class-name="svg-data"
                              ></svg-icon>
                              <span class="mr-05 ml-05">DOI</span>
                              <a
                                :href="
                                  'https://doi.org/' + item.publishVOs[0].doi
                                "
                                class="text-other-color"
                                target="_blank"
                                >({{ item.publishVOs[0].doi }})
                              </a>
                              <el-popover
                                v-if="
                                  item.publishVOs &&
                                  item.publishVOs.length > 1 &&
                                  item.publishVOs[0].doi
                                "
                                placement="right"
                                :width="230"
                                trigger="hover"
                              >
                                <template #reference>
                                  <span style="margin-left: 5px">
                                    <el-icon
                                      color="#07bcb4"
                                      style="vertical-align: middle"
                                    >
                                      <Expand />
                                    </el-icon>
                                  </span>
                                </template>
                                <ul class="near-divider">
                                  <li
                                    v-for="(
                                      pubItem, pubItemIndex
                                    ) in item.publishVOs"
                                    :key="'pub_' + index + '_' + pubItemIndex"
                                  >
                                    <a
                                      :href="'https://doi.org/' + pubItem.doi"
                                      class="text-other-color"
                                      target="_blank"
                                      >{{ pubItem.doi }}
                                    </a>
                                    <el-divider border-style="dashed" />
                                  </li>
                                </ul>
                              </el-popover>
                            </div>
                          </div>
                          <!--                      <div class="d-flex align-items-center">-->
                          <!--                        <span-->
                          <!--                          v-if="item.usedIds && item.usedIds.length > 0"-->
                          <!--                          class="text-secondary-color font-600"-->
                          <!--                          >Used ID :</span-->
                          <!--                        >-->
                          <!--                        <span-->
                          <!--                          v-if="item.usedIds && item.usedIds.length > 0"-->
                          <!--                          class="text-other-color ml-05"-->
                          <!--                          v-text="item.usedIds.join(', ')"-->
                          <!--                        ></span>-->
                          <!--                      </div>-->
                        </div>
                      </el-col>
                      <el-col
                        :span="8"
                        class="text-align-right"
                        :xs="24"
                        :sm="8"
                      >
                        <span
                          v-if="
                            item.typeEnum === 'project' ||
                            item.typeEnum === 'sample'
                          "
                          class="mr-2"
                        >
                          <span
                            class="text-secondary-color text-blue font-600 font-18 mr-05"
                            >{{ item.expNum }}
                          </span>
                          <span class="text-other-color">{{
                            $t('browse.index.content.experiments')
                          }}</span>
                        </span>
                        <span
                          v-if="
                            item.typeEnum === 'project' ||
                            item.typeEnum === 'experiment'
                          "
                          class="mr-2"
                        >
                          <span
                            class="text-secondary-color text-blue font-600 font-18 mr-05"
                            >{{ item.sapNum }}
                          </span>
                          <span class="text-other-color">{{
                            $t('browse.index.content.samples')
                          }}</span>
                        </span>
                        <span>
                          <span
                            class="text-secondary-color text-blue font-600 font-18 mr-05"
                            >{{ item.fileNum }}
                          </span>
                          <span class="text-other-color">{{
                            $t('browse.index.content.files')
                          }}</span>
                        </span>
                      </el-col>
                    </el-row>
                    <div class="radius-12 bg-gray p-15 mt-05 pos-relative list">
                      <div class="download-icon cursor-pointer">
                        <svg-icon
                          icon-class="download"
                          class-name="svg-data-lg"
                          @click="exportDataLink(item.typeEnum, item.id)"
                        ></svg-icon>
                      </div>
                      <div class="d-flex flex-wrap gap-12">
                        <div
                          v-if="item.expTypes && item.expTypes.length > 0"
                          class="item"
                        >
                          <span class="title">{{
                            $t('browse.index.content.experimentType')
                          }}</span>
                          <ExpType
                            v-for="(type, index) in item.expTypes"
                            :key="'exp-type-' + index"
                            :type="expIconInfo[type]?.iconName"
                            :color="expIconInfo[type]?.iconColor"
                            :full-name="type"
                          ></ExpType>
                        </div>

                        <div
                          v-if="item.sapTypes && item.sapTypes.length > 0"
                          class="item"
                        >
                          <span class="title">{{
                            $t('browse.index.content.sampleType')
                          }}</span>
                          <span
                            class="content"
                            v-text="getFirst(item.sapTypes)"
                          ></span>
                          <el-popover
                            v-if="item.sapTypes.length > 1"
                            placement="right"
                            :width="150"
                            trigger="hover"
                          >
                            <template #reference>
                              <span style="margin-left: 5px">
                                <el-icon
                                  color="#07bcb4"
                                  style="vertical-align: middle"
                                >
                                  <Expand />
                                </el-icon>
                              </span>
                            </template>
                            <ul>
                              <li
                                v-for="(
                                  typeItem, typeItemIndex
                                ) in item.sapTypes"
                                :key="'sap_' + index + '_' + typeItemIndex"
                              >
                                {{ typeItem }}
                              </li>
                            </ul>
                          </el-popover>
                        </div>

                        <div v-if="item.anaType" class="item">
                          <span class="title">{{
                            $t('browse.index.content.analysisType')
                          }}</span>
                          <span class="content" v-text="item.anaType"></span>
                        </div>

                        <div v-if="item.organism" class="item">
                          <span class="title">{{
                            $t('browse.index.content.organism')
                          }}</span>
                          <span class="content" v-text="item.organism"></span>
                        </div>

                        <div v-if="item.tissue" class="item">
                          <span class="title">{{
                            $t('browse.index.content.tissue')
                          }}</span>
                          <span class="content" v-text="item.tissue"></span>
                        </div>

                        <div
                          v-if="item.description"
                          class="item"
                          style="position: relative; padding: 3px 0 3px 100px"
                        >
                          <span
                            class="title"
                            style="position: absolute; left: 0"
                            >{{ $t('browse.index.content.description') }}</span
                          >
                          <span class="content description">
                            <el-tooltip
                              :teleported="false"
                              :content="item.description"
                              effect="light"
                              placement="top"
                              trigger="hover"
                              popper-class="cus-el-popper"
                              raw-content
                            >
                              {{ $text(item.description) }}
                            </el-tooltip>
                          </span>
                        </div>
                      </div>
                    </div>
                    <el-divider></el-divider>
                  </div>
                </div>
                <div v-if="browseListData.length === 0">
                  <el-empty
                    :description="$t('browse.index.content.noDataMessage')"
                  ></el-empty>
                </div>
              </div>
              <pagination
                v-model:page="queryPageAndSort.pageNum"
                v-model:limit="queryPageAndSort.pageSize"
                class="mb-1 justify-center"
                background
                :total="browseListTotal"
              />
            </el-tab-pane>
            <el-tab-pane
              :label="$t('browse.index.tabs.conciseList')"
              name="concise"
            >
              <el-table
                ref="browseTableRef"
                tooltip-effect="light"
                :data="browseListData"
                :default-sort="tableDefaultSort"
                stripe
                style="width: 100%; margin-bottom: 20px"
                :header-cell-style="{
                  backgroundColor: '#f2f2f2',
                  color: '#333333',
                  fontWeight: 700,
                }"
                :row-key="row => row.id"
                border
                :empty-text="$t('browse.index.content.noDataMessage')"
                @sort-change="tableSortChange"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  type="selection"
                  :reserve-selection="true"
                  width="40"
                />
                <el-table-column
                  prop="typeId"
                  width="120"
                  :label="$t('browse.index.table.columns.id')"
                  :sort-orders="['ascending', 'descending']"
                  :sortable="'custom'"
                >
                  <template #default="scope">
                    <span class="text-primary cursor-pointer">
                      <a
                        :href="toDetailUrl(scope.row.id, scope.row.typeEnum)"
                        target="_blank"
                      >
                        {{ scope.row.id }}
                      </a>
                    </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="type"
                  :label="$t('browse.index.table.columns.type')"
                  :sort-orders="['ascending', 'descending']"
                  :sortable="'custom'"
                  width="100"
                >
                  <template #default="scope">
                    <el-tag
                      type="warning"
                      round
                      class="mr-05 tag-warning"
                      :class="computedTagClass(scope.row.type)"
                      >{{ computedTagText(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="name"
                  :label="$t('browse.index.table.columns.name')"
                  :sort-orders="['ascending', 'descending']"
                  :sortable="'custom'"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="description"
                  :label="$t('browse.index.table.columns.description')"
                  :sort-orders="['ascending', 'descending']"
                  :sortable="false"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="subName"
                  :label="$t('browse.index.table.columns.submitter')"
                  width="150"
                  :sort-orders="['ascending', 'descending']"
                  :sortable="'custom'"
                  show-overflow-tooltip
                />
                <el-table-column
                  prop="modifiedDate"
                  :label="$t('browse.index.table.columns.modifiedDate')"
                  width="150"
                  :sort-orders="['ascending', 'descending']"
                  :sortable="'custom'"
                >
                  <template #default="scope">
                    <span v-text="truncDate(scope.row.lastModified)"></span>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-model:page="queryPageAndSort.pageNum"
                v-model:limit="queryPageAndSort.pageSize"
                class="mb-1 justify-center"
                background
                :total="browseListTotal"
              />
              <el-divider></el-divider>
              <el-button
                type="primary"
                class="float-right"
                round
                :icon="Download"
                :disabled="multipleSelection.length === 0"
                @click="batchExportDataLink"
                >{{ $t('browse.index.actions.exportDataLinks') }}
              </el-button>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import Breadcrumb from '@/components/breadcrumb.vue';
  import { Download } from '@element-plus/icons-vue';
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    onUnmounted,
    reactive,
    ref,
    watch,
  } from 'vue';
  import {
    browseSearch,
    getExpIconInfo,
    searchSelectData,
  } from '@/api/app/browse';
  import { isArrEmpty, trimStr } from '@/utils';
  import SvgIcon from '@/components/SvgIcon/index.vue';
  import { useRoute } from 'vue-router';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import { formatNumber } from '@/utils/nodeCommon';
  import ExpType from '@/views/browse/expType.vue';
  import { useI18n } from 'vue-i18n';

  const route = useRoute();

  // 高级检索--构建的条件字符串
  const searchBuilderStr = ref('');
  // 高级检索--可编辑的条件字符串
  const searchEditStr = ref('');
  // 高级检索--当前是否正在手动编辑查询条件
  const searchEditing = ref(false);
  const toAdvanced = ref(false);
  // const route = useRoute();

  const { proxy } = getCurrentInstance();
  const { locale } = useI18n();

  let advanceVal = history.state.advanceVal; // params参数跳转
  if (advanceVal) {
    toAdvanced.value = advanceVal;
  }

  function toDetailUrl(id, type) {
    let publicPath = import.meta.env.VITE_APP_PUBLIC_PATH;
    if (!publicPath) {
      publicPath = '';
    }
    if (type === 'project') {
      publicPath += '/project/detail/' + id;
    } else if (type === 'experiment') {
      publicPath += '/experiment/detail/' + id;
    } else if (type === 'sample') {
      publicPath += '/sample/detail/' + id;
    } else if (type === 'analysis') {
      publicPath += '/analysis/detail/' + id;
    }
    return publicPath;
  }

  function getFirst(arr) {
    return arr && arr.length > 0 ? arr[0] : '';
  }

  // 全文检索关键字
  const searchInput = ref('');
  const example = ref([
    'OEP00000073',
    'NODEP00000033',
    '10.1016/j.cell.2020.07.023',
    'Genomic',
    'PD-1',
    'lung cancer',
  ]);

  // 单行查询条件
  const queryItem = {
    relation: 'AND',
    queryField: 'ID',
    inputValue: '',
    type: '',
    rangeValue1: null,
    rangeValue2: null,
  };

  // 所有高级查询条件
  const buildValues = reactive([
    {
      ...queryItem,
    },
    {
      ...queryItem,
    },
  ]);

  // 编辑或者取消编辑构建条件
  function editOrCancel() {
    searchEditing.value = !searchEditing.value;
    searchEditStr.value = trimStr(searchBuilderStr.value);
  }

  // 清空查询条件
  function clearBuilder() {
    buildValues.length = 0;
    buildValues.push(...[{ ...queryItem }, { ...queryItem }]);
    searchEditing.value = false;
  }

  // 值域框自动补全远程加载下拉框
  function queryFieldSearch(queryStr, cb, index) {
    queryStr = trimStr(queryStr);
    if (!queryStr) {
      cb([]);
    } else {
      // buildValues[index].loading = true;
      const field = buildValues[index].queryField;
      let param = {
        field: field,
        keyword: queryStr,
      };
      searchSelectData(param)
        .then(response => {
          if (response.code === 200) {
            let data = response.data;
            let result = [];
            if (!isArrEmpty(data)) {
              for (let i = 0; i < data.length; i++) {
                result.push({
                  label: data[i],
                  value: data[i],
                });
              }
            }
            cb(result);
          } else {
            cb([]);
          }
        })
        .catch(() => {
          cb([]);
        });
    }
  }

  const accessTypes = reactive([
    {
      label: 'Controlled',
      value: 'Controlled',
    },
    {
      label: 'Public',
      value: 'Public',
    },
  ]);

  const levelTypes = reactive([
    {
      label: 'Project',
      value: 'Project',
    },
    {
      label: 'Experiment',
      value: 'Experiment',
    },
    {
      label: 'Sample',
      value: 'Sample',
    },
    {
      label: 'Analysis',
      value: 'Analysis',
    },
  ]);

  // 高级搜索字段下拉框
  const builderOpt = reactive([
    {
      label: ' ',
      labelKey: 'browse.builderOptions.general.label',
      options: [
        {
          value: 'ID',
          labelKey: 'browse.builderOptions.general.id',
        },
        {
          value: 'Name',
          labelKey: 'browse.builderOptions.general.name',
        },
        {
          value: 'Description',
          labelKey: 'browse.builderOptions.general.description',
        },
        {
          value: 'Used Ids',
          labelKey: 'browse.builderOptions.general.usedIds',
        },
        {
          value: 'Modified Date',
          labelKey: 'browse.builderOptions.general.modifiedDate',
          type: 'date',
        },
      ],
    },
    /*{
label: 'Project',
options: [
{
value: 'Multiple Omics',
type: 'bool',
},
],
},*/
    {
      label: 'Experiment',
      labelKey: 'browse.builderOptions.experiment.label',
      options: [
        {
          value: 'Protocol',
          labelKey: 'browse.builderOptions.experiment.protocol',
        },
        {
          value: 'Experiment Type',
          labelKey: 'browse.builderOptions.experiment.experimentType',
        },
        {
          value: 'Library Strategy',
          labelKey: 'browse.builderOptions.experiment.libraryStrategy',
        },
        {
          value: 'Library Layout',
          labelKey: 'browse.builderOptions.experiment.libraryLayout',
        },
        {
          value: 'Library Selection',
          labelKey: 'browse.builderOptions.experiment.librarySelection',
        },
        {
          value: 'Library Name',
          labelKey: 'browse.builderOptions.experiment.libraryName',
        },
        {
          value: 'Platform',
          labelKey: 'browse.builderOptions.experiment.platform',
        },
      ],
    },
    {
      label: 'Sample',
      labelKey: 'browse.builderOptions.sample.label',
      options: [
        {
          value: 'Sample Type',
          labelKey: 'browse.builderOptions.sample.sampleType',
        },
        {
          value: 'Organism',
          labelKey: 'browse.builderOptions.sample.organism',
        },
        {
          value: 'Tissue',
          labelKey: 'browse.builderOptions.sample.tissue',
        },
        {
          value: 'Subject Id',
          labelKey: 'browse.builderOptions.sample.subjectId',
        },
        {
          value: 'Biomaterial Provider',
          labelKey: 'browse.builderOptions.sample.biomaterialProvider',
        },
        {
          value: 'Disease',
          labelKey: 'browse.builderOptions.sample.disease',
        },
        {
          value: 'Dis Phenotype',
          labelKey: 'browse.builderOptions.sample.disPhenotype',
        },
        {
          value: 'Mutation Type',
          labelKey: 'browse.builderOptions.sample.mutationType',
        },
        {
          value: 'Sample Loc',
          labelKey: 'browse.builderOptions.sample.sampleLoc',
        },
        {
          value: 'Gender',
          labelKey: 'browse.builderOptions.sample.gender',
        },
        {
          value: 'Extracted Mol Type',
          labelKey: 'browse.builderOptions.sample.extractedMolType',
        },
        {
          value: 'Dev Stage',
          labelKey: 'browse.builderOptions.sample.devStage',
        },
        {
          value: 'Biome',
          labelKey: 'browse.builderOptions.sample.biome',
        },
        {
          value: 'Env Biome',
          labelKey: 'browse.builderOptions.sample.envBiome',
        },
        {
          value: 'Env Material',
          labelKey: 'browse.builderOptions.sample.envMaterial',
        },
        {
          value: 'Env Feature',
          labelKey: 'browse.builderOptions.sample.envFeature',
        },
      ],
    },
    {
      label: 'Analysis',
      labelKey: 'browse.builderOptions.analysis.label',
      options: [
        {
          value: 'Analysis Type',
          labelKey: 'browse.builderOptions.analysis.analysisType',
        },
        {
          value: 'Pipeline Program',
          labelKey: 'browse.builderOptions.analysis.pipelineProgram',
        },
      ],
    },
    {
      label: 'Data',
      labelKey: 'browse.builderOptions.data.label',
      options: [
        {
          value: 'File Type',
          labelKey: 'browse.builderOptions.data.fileType',
        },
      ],
    },
    {
      label: 'Publish',
      labelKey: 'browse.builderOptions.publish.label',
      options: [
        {
          value: 'DOI',
          labelKey: 'browse.builderOptions.publish.doi',
        },
        {
          value: 'PMID',
          labelKey: 'browse.builderOptions.publish.pmid',
        },
        {
          value: 'Article Name',
          labelKey: 'browse.builderOptions.publish.articleName',
        },
      ],
    },
    {
      label: 'Submitter',
      labelKey: 'browse.builderOptions.submitter.label',
      options: [
        {
          value: 'Submitter',
          labelKey: 'browse.builderOptions.submitter.submitter',
        },
        {
          value: 'Organization',
          labelKey: 'browse.builderOptions.submitter.organization',
        },
        {
          value: 'Country',
          labelKey: 'browse.builderOptions.submitter.country',
        },
      ],
    },
    {
      label: 'Fast QC',
      labelKey: 'browse.builderOptions.fastQc.label',
      options: [
        {
          value: 'num_seqs',
          labelKey: 'browse.builderOptions.fastQc.numSeqs',
          type: 'numRange',
        },
        {
          value: 'bases',
          labelKey: 'browse.builderOptions.fastQc.bases',
          type: 'numRange',
        },
        {
          value: 'Q20(%)',
          labelKey: 'browse.builderOptions.fastQc.q20',
          type: 'numRange',
        },
        {
          value: 'Q30(%)',
          labelKey: 'browse.builderOptions.fastQc.q30',
          type: 'numRange',
        },
      ],
    },
  ]);

  // 字段下拉框change方法
  function cleanValFiled(newVal, index) {
    buildValues[index].inputValue = '';
    const length = builderOpt.length;
    let type = '';
    for (let i = 0; i < length; i++) {
      let options = builderOpt[i].options;
      let len = options.length;
      for (let j = 0; j < len; j++) {
        let opt = options[j];
        if (opt.value === newVal) {
          type = trimStr(opt.type);
          break;
        }
      }
    }
    buildValues[index].type = type;
  }

  // 删除一行查询条件
  function removeParam(index) {
    if (buildValues.length <= 2) {
      return false;
    }
    buildValues.splice(index, 1);
  }

  // 添加一行查询条件
  function addParam() {
    if (buildValues.length >= 30) {
      return false;
    }
    buildValues.push({
      ...queryItem,
    });
  }

  const accessRadio = ref([]);
  const accessItems = ref([]);
  const mode = ref('verbose');
  const displayedCounts = ref([]); // 初始显示条数

  // Access勾选
  function accessChange() {
    keywordSearch(false);
  }

  const sortBtn = reactive([
    {
      label: 'Modified Date',
      labelKey: 'browse.sortButtons.modifiedDate',
      field: 'modifiedDate',
      highlighted: true,
      sortOrder: 'desc',
      show: true,
    },
    {
      label: 'Name',
      labelKey: 'browse.sortButtons.name',
      field: 'name',
      highlighted: false,
      sortOrder: 'desc',
      show: true,
    },
    {
      label: 'Submitter',
      labelKey: 'browse.sortButtons.submitter',
      field: 'subName',
      highlighted: false,
      sortOrder: 'desc',
      show: true,
    },
    {
      label: 'ID',
      labelKey: 'browse.sortButtons.id',
      field: 'typeId',
      highlighted: false,
      sortOrder: 'desc',
      show: false,
    },
    {
      label: 'Type',
      labelKey: 'browse.sortButtons.type',
      field: 'type',
      highlighted: false,
      sortOrder: 'desc',
      show: false,
    },
    {
      label: 'Description',
      labelKey: 'browse.sortButtons.description',
      field: 'description',
      highlighted: false,
      sortOrder: 'desc',
      show: false,
    },
  ]);

  const tableDefaultSort = computed(() => {
    let data = {};
    let length = sortBtn.length;
    for (let i = 0; i < length; i++) {
      let item = sortBtn[i];
      if (item.highlighted) {
        data['prop'] = item.field;
        data['order'] = item.sortOrder === 'desc' ? 'descending' : 'ascending';
        break;
      }
    }
    return data;
  });

  function initTableDefaultSort() {
    let browseTableRef = proxy.$refs['browseTableRef'];
    if (browseTableRef) {
      browseTableRef.sort(
        tableDefaultSort.value.prop,
        tableDefaultSort.value.order,
      );
    }
  }

  function tableSortChange(column) {
    let { prop, order } = column;
    if (order && 'concise' === mode.value) {
      let sortIndex = 0;
      let length = sortBtn.length;
      for (let i = 0; i < length; i++) {
        let item = sortBtn[i];
        if (item.field === prop) {
          sortIndex = i;
          item.highlighted = true;
          item.sortOrder = order === 'descending' ? 'desc' : 'asc';
        } else {
          item.highlighted = false;
        }
      }
      doSortSearch(sortIndex);
    }
  }

  function doSortSearch(index) {
    // 修改排序字段，触发watch方法
    queryPageAndSort.value.sortKey = sortBtn[index].field;
    queryPageAndSort.value.sortType = sortBtn[index].sortOrder;
  }

  const browseListData = ref([]);
  const treeProps = {
    children: 'data',
    label: 'name',
  };

  // 左侧选中的树ref集合
  const leftCheckedTrees = reactive([]);
  const leftStatData = reactive([]);

  const visible = ref(false);
  const checkList = ref([]);

  function initLeftData() {
    checkList.value = [];
    leftStatData.forEach(it => {
      if (it.isShow && it.data && it.data.length > 0) {
        checkList.value.push(it.name);
      }
    });
    displayedCounts.value = leftStatData.map(() => 5);
  }

  let expIconInfo = ref({});
  let lowCaseExpIconInfo = ref({});

  onMounted(() => {
    getExpIconInfo().then(response => {
      expIconInfo.value = response.data;

      // 左侧图标处理
      let obj = {};
      Object.keys(response.data).forEach(key => {
        obj[key.toLowerCase()] = response.data[key];
      });
      lowCaseExpIconInfo.value = obj;
    });
    const keyword = trimStr(route.query.keyword);
    if (keyword) {
      topFullTextSearch(keyword);
    } else {
      keywordSearch(true);
    }
    bus.on(BusEnum.FULLTEXT_SEARCH, word => topFullTextSearch(word));
  });

  onUnmounted(() => {
    bus.off(BusEnum.FULLTEXT_SEARCH);
  });

  const queryPageAndSort = ref({
    sortKey: sortBtn[0].field,
    sortType: sortBtn[0].sortOrder,
    pageNum: 1,
    pageSize: 10,
  });

  const toggleSortOrder = index => {
    sortBtn.forEach(btn => {
      btn.highlighted = false;
    });
    sortBtn[index].highlighted = true;
    sortBtn[index].sortOrder =
      sortBtn[index].sortOrder === 'asc' ? 'desc' : 'asc';

    // 修改排序字段，触发watch方法
    doSortSearch(index);
    initTableDefaultSort();
  };

  const selectCondition = () => {
    leftStatData.forEach((it, index) => {
      const name = it.name;
      if (checkList.value.includes(name)) {
        it.isShow = true;
      } else {
        it.isShow = false;
        const treeRef = initTreeRef(index, name);
        let treeObjs = proxy.$refs[treeRef];
        if (treeObjs && treeObjs.length > 0) {
          treeObjs[0].setCheckedKeys([]);
        }
        changeCheckedTreeRefs(false, treeRef);
      }
    });
    /*leftStatData.forEach(it => (it.isShow = false));
const showItem = checkList.value.map(item =>
leftStatData.find(obj => obj.name === item),
);
showItem.forEach(it => {
it.isShow = true;
});*/
    visible.value = false;
  };

  function trimmedData(data, index) {
    /*if (data) {
return data.slice(0, displayedCounts.value[index]);
} else {
return null;
}*/
    return data.slice(0, displayedCounts.value[index]);
  }

  const Expanded = (data, index) => {
    return displayedCounts.value[index] < data.length;
  };

  const toggleExpand = (data, index) => {
    if (Expanded(data, index)) {
      displayedCounts.value[index] = data.length; // 展开全部
    } else {
      displayedCounts.value[index] = 5;
    }
  };

  const computedTagClass = type => {
    return `tag-${type.toLowerCase()}`;
  };

  const computedTagText = type => {
    if (type === 'PROJ') {
      return locale.value === 'zh'
        ? proxy.$t('dataStatistic.types.project')
        : type;
    } else if (type === 'EXPR') {
      return locale.value === 'zh'
        ? proxy.$t('dataStatistic.types.experiment')
        : type;
    } else if (type === 'SAMP') {
      return locale.value === 'zh'
        ? proxy.$t('dataStatistic.types.sample')
        : type;
    } else if (type === 'ANAL') {
      return locale.value === 'zh'
        ? proxy.$t('dataStatistic.types.analysis')
        : type;
    }
    return type;
  };

  const browseListTotal = ref(0);

  // 查询条件
  const queryParam = ref({
    queryWord: '',
    advQueryWord: '',
  });

  // 全文检索
  function keywordSearch(reSearch) {
    proxy.$modal.loading('Loading');
    searchInput.value = trimStr(searchInput.value);
    queryParam.value.queryWord = searchInput.value;
    let param = queryParam.value;
    let pagePram = queryPageAndSort.value;
    // 请求参数和分页信息
    param = { ...param, ...pagePram };

    if (!reSearch) {
      // 左侧树选中节点信息
      let leftStatQueries = leftCheckedData.value;
      if (leftStatQueries && leftStatQueries.length > 0) {
        param.leftStatQueries = leftStatQueries;
      }

      // 左侧Access信息
      const accessQueries = accessRadio.value;
      if (accessQueries && accessQueries.length > 0) {
        param.accessQueries = accessQueries;
      }
    } else {
      let leftStatQueries = leftCheckedData.value;
      if (leftStatQueries && leftStatQueries.length > 0) {
        let len = leftStatQueries.length;
        for (let i = 0; i < len; i++) {
          let item = leftStatQueries[i];
          const treeRefElement = proxy.$refs[item.treeRefId];
          if (treeRefElement && treeRefElement[0]) {
            treeRefElement[0].setCheckedKeys([]);
          }
        }
        leftCheckedData.value = [];
      }
    }

    browseSearch(param)
      .then(response => {
        browseListData.value = [];
        if (response.code === 200) {
          let { pageInfo, accessData, treeItems } = response.data;
          browseListData.value = pageInfo.content;
          browseListTotal.value = pageInfo.totalElements;
          if (reSearch) {
            accessItems.value = accessData?.data;
            accessRadio.value = [];

            leftStatData.length = 0;
            leftStatData.push(...treeItems);
            initLeftData();
          }
        }
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }

  // 全文检索
  function fullTextSearch() {
    queryParam.value.advQueryWord = '';
    keywordSearch(true);
    // topFullTextSearch();
  }

  // 顶部全文检索
  function topFullTextSearch(urlParam) {
    queryParam.value.advQueryWord = '';
    if (urlParam !== null && urlParam !== undefined) {
      searchInput.value = trimStr(urlParam);
    }
    keywordSearch(true);
  }

  // 高级检索
  function doAdvSearch() {
    let advQueryWord = '';
    if (searchEditing.value) {
      advQueryWord = searchEditStr.value;
    } else {
      advQueryWord = searchBuilderStr.value;
    }
    searchInput.value = '';
    queryParam.value.advQueryWord = advQueryWord;
    keywordSearch(true);
  }

  // 截断日期
  function truncDate(str = '') {
    str = trimStr(str);
    return str.split(' ')[0];
  }

  // 截断字符串
  function truncStr(str = '') {
    str = trimStr(str);
    return str.length > 40 ? str.substring(0, 40) + '...' : str;
  }

  function expandLeftTree(index, treeRef) {
    //item.isExpand = !item.isExpand
    let expanded = !leftStatData[index].isExpand;
    leftStatData[index].isExpand = expanded;
    if (expanded === false) {
      changeCheckedTreeRefs(expanded, treeRef);
    }
  }

  const leftCheckedData = ref([]);
  const treeNameConn = '__';
  const treeNameStart = '_lsTree';

  // 生成树组件ref
  function initTreeRef(index, treeName) {
    return index + treeNameStart + treeNameConn + treeName;
  }

  function handleTreeCheck(data, checkInfo, treeRef) {
    let checked = checkInfo.checkedKeys.length > 0;
    changeCheckedTreeRefs(checked, treeRef);
  }

  function changeCheckedTreeRefs(checked, treeRef) {
    if (checked) {
      if (!leftCheckedTrees.includes(treeRef)) {
        leftCheckedTrees.push(treeRef);
      }
    } else {
      let i = leftCheckedTrees.indexOf(treeRef);
      if (i > -1) {
        leftCheckedTrees.splice(i, 1);
      }
    }
    searchByLeft();
  }

  function searchByLeft() {
    getCheckedData().then(checkedData => {
      // console.log(checkedData);
      let oldLeftDataStr = JSON.stringify(leftCheckedData.value);
      leftCheckedData.value = checkedData;
      if (oldLeftDataStr !== JSON.stringify(leftCheckedData.value)) {
        keywordSearch(false);
      }
    });
  }

  function getCheckedData() {
    return new Promise((resolve, reject) => {
      nextTick(() => {
        const allTreeDataLength = leftStatData.length;
        const checkedData = [];
        for (let index = 0; index < allTreeDataLength; index++) {
          let nodeName = leftStatData[index].name;
          const treeRef = initTreeRef(index, nodeName);
          let treeRefInVue = proxy.$refs[treeRef];
          if (!treeRefInVue) {
            continue;
          }
          const treeRefElement = treeRefInVue[0];
          if (!treeRefElement) {
            continue;
          }
          if (leftCheckedTrees.includes(treeRef)) {
            const treeGroup = treeRef.substring(
              treeRef.indexOf(treeNameConn) + treeNameConn.length,
            );
            let checkedNodes = treeRefElement.getCheckedNodes();
            let length = checkedNodes.length;
            for (let j = 0; j < length; j++) {
              checkedData.push({
                type: treeGroup,
                name: checkedNodes[j].fieldName,
                treeRefId: treeRef,
              });
            }
            const defaultCheckedKeys = treeRefElement.getCheckedKeys();
            const defaultExpandedKeys = [
              ...defaultCheckedKeys,
              ...treeRefElement.getHalfCheckedKeys(),
            ];
            treeRefElement.setCheckedKeys(defaultCheckedKeys);
            leftStatData[index].defaultCheckedKeys = defaultCheckedKeys;
            leftStatData[index].defaultExpandedKeys = defaultExpandedKeys;
          } else {
            leftStatData[index].defaultCheckedKeys = [];
            treeRefElement.setCheckedKeys([]);
            // leftStatData[index].defaultExpandedKeys = [];
          }
        }
        resolve(checkedData);
      });
    });
  }

  // 分页排序参数变化时，重新搜索
  watch(
    queryPageAndSort,
    (newValue, oldValue) => {
      keywordSearch(false);
    },
    {
      immediate: false,
      deep: true,
    },
  );

  const rangeFlag = ',';
  // 高级查询构造器变化时，跟新上方检索字符串
  watch(
    buildValues,
    (newValue, oldValue) => {
      const arr = [];
      newValue.forEach(item => {
        let rangeValue1 = trimStr(item.rangeValue1);
        let rangeValue2 = trimStr(item.rangeValue2);
        const relation = item.relation;
        const queryField = item.queryField;
        if (rangeValue1 || rangeValue2) {
          // 范围取值
          let fieldStr;
          if (arr.length === 0) {
            fieldStr = `${rangeValue1}${rangeFlag}${rangeValue2}[${queryField}]`;
          } else {
            fieldStr = `${relation} ${rangeValue1}${rangeFlag}${rangeValue2}[${queryField}]`;
          }
          arr.push(fieldStr);
        } else {
          // 其它取值
          let val = trimStr(item.inputValue);
          if (val) {
            let fieldStr;
            if (arr.length === 0) {
              fieldStr = `${val}[${queryField}]`;
            } else {
              fieldStr = `${relation} ${val}[${queryField}]`;
            }
            arr.push(fieldStr);
          }
        }
      });
      let length = arr.length;
      const queryArr = [];
      if (length > 0) {
        const last = length - 1;
        for (let i = 0; i < length; i++) {
          if (i === 0) {
            if (length === 1) {
              queryArr.push(`${arr[i]}`);
            } else {
              queryArr.push(`(${arr[i]})`);
            }
          } else {
            let pre = queryArr[i - 1];
            if (i === last) {
              queryArr.push(`${pre} ${arr[i]}`);
            } else {
              queryArr.push(`(${pre} ${arr[i]})`);
            }
          }
        }
      }
      searchBuilderStr.value = trimStr(queryArr[queryArr.length - 1]);
    },
    {
      immediate: false,
      deep: true,
    },
  );

  let multipleSelection = ref([]);

  function handleSelectionChange(val) {
    multipleSelection.value = val;
  }

  /** 导出页面中data的下载链接 */
  function exportDataLink(type, id) {
    proxy.download(
      `/download/node/exportDownloadLink/${type}/${id}`,
      null,
      `${type}_${id}_data_download_link.zip`,
    );
  }

  /** batch Export data downloadlink */
  function batchExportDataLink() {
    let data = {};
    multipleSelection.value.forEach((it, idx) => {
      data[`params[${idx}].type`] = it.typeEnum;
      data[`params[${idx}].typeNo`] = it.id;
    });
    proxy.download(
      `/download/node/batchExportDownloadLink`,
      data,
      `batch_export_data_download_link.zip`,
    );
  }

  /*watch(
sortBtn,
() => {
initTableDefaultSort();
},
{
immediate: true,
deep: true,
},
);*/
</script>

<style>
  .cus-el-popper {
    max-width: 600px;
  }
</style>

<style lang="scss" scoped>
  .browse-search {
    padding: 4px;
    border: 2px solid #e4e4e4;
    background-color: #fff;
    border-radius: 20px;
    position: relative;
    width: 90%;
    display: flex;
    align-items: center;

    .bg-round-warning {
      width: 60px;
    }

    :deep(.el-input__wrapper) {
      border-color: transparent;
      box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color))
        inset;
    }

    & + .advanced-btn {
      padding: 18px 15px;
      box-shadow: 0 3px 5px 0 rgba(47, 85, 212, 0.3);
    }
  }

  .builder {
    padding: 15px 30px;

    .icon-minus {
      margin-right: 2.7rem;
    }

    :deep(.el-select__wrapper) {
      border-radius: 12px;
    }

    :deep(.el-range-editor.el-input__wrapper) {
      width: 98%;
    }
  }

  .builder-search {
    width: 10%;
    margin-left: 32px;
    background-color: #ffebde;
    color: #fe7f2b;
    box-shadow: 0 3px 5px 0 rgb(255 235 222);
  }

  .access-radio {
    :deep(.el-checkbox__label) {
      width: 100%;
      position: relative;

      span:last-child {
        position: absolute;
        right: 0;
      }
    }
  }

  .drop-down-title {
    font-weight: 600;
    padding: 2px 15px;
    background-color: #d6e5ff;
    border-radius: 14px;
    border: 1px solid #3a78e8;
    color: #3a78e8;
    transition: all 0.3s ease-in;

    &:hover {
      background-color: #bad1f8;
    }

    & > span {
      font-weight: 600;
    }

    .el-icon {
      position: relative;
      top: 5px;
    }
  }

  .drop-down-content {
    padding: 0 15px;

    :deep(.el-checkbox__label) {
      color: #666666;
      font-size: 14px !important;
    }

    :deep(.el-tree-node__content) {
      background-color: #f4f8fb;

      .el-tree-node__label {
        color: #666666;
        font-weight: 600;
      }
    }
  }

  .more:hover {
    color: #3a78e8;
  }

  .verbose {
    .tag-warning {
      background-color: #ffebde;
      color: #fe812b;
      font-size: 14px;
      border: none;
    }

    .name {
      font-size: 18px;
      font-weight: 700;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: calc(100vw - 900px);
      display: inline-block;
      min-width: 140px;
    }

    .download-icon {
      position: absolute;
      top: 6px;
      right: 8px;
    }

    .tag-success {
      background-color: #cfefed !important;
      color: #07bcb4;
      font-weight: 600;
      border-radius: 8px;

      :deep(.el-tag__content) {
        font-weight: 600;
        font-size: 14px;
      }
    }
  }

  .sort {
    background-color: #f5f8fe;
    border-radius: 4px;
    padding: 8px;

    .el-button {
      margin: 0;
      border: 1px solid #95cdff;
      border-radius: 0;

      &:first-child {
        border-radius: 12px 0 0 12px;
      }

      &:last-child {
        border-radius: 0 12px 12px 0;
      }

      &.active {
        background-color: #ebf2fd;
        color: #3a78e8;
      }
    }
  }

  .list {
    gap: 15px;

    .item {
      display: flex;
      min-width: 30%;
      margin-right: 1rem;
      transition: all 3s linear;
      align-items: center;
    }

    .title {
      display: inline-block;
      color: #333333;
      font-weight: 600;
      margin-right: 20px;
    }

    .content {
      color: #606266;
      white-space: normal; /* 允许内容自动换行 */
      word-break: break-all; /* 在单词内部换行 */
    }
  }

  .el-checkbox-group {
    :deep(.el-checkbox) {
      width: 25%;
      margin-right: 65px;
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  a:hover {
    color: #fe7f2b;
  }

  .el-tag {
    border: none;

    :deep(.el-tag__content) {
      font-weight: 600;
    }
  }

  .browse-list {
    :deep(.el-popper) {
      font-size: 14px;
      max-width: 600px;
    }
  }

  .near-divider {
    :deep(.el-divider--horizontal) {
      margin: 3px 0;
    }
  }

  .svg-data {
    width: 14px;
    height: 14px;
  }

  .svg-data-lg {
    width: 24px;
    height: 24px;
  }

  .bg-disable {
    pointer-events: none;
    background-color: rgba(207, 207, 207, 0.7);
  }

  .search-edit {
    padding: 5px;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
    min-height: 36px;
  }

  //树节点首字母大写
  .browse-outer-dev {
    :deep(span.upper-first::first-letter) {
      text-transform: uppercase !important;
    }
  }

  .description {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 设置最大显示行数 */
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
  }

  .svg-usedID {
    width: 23px;
    height: 23px;
    cursor: pointer;
  }

  @media (max-width: 767px) {
    .list .item {
      min-width: 100%;
    }
    .w-75 {
      width: 100% !important;
    }
    .eg {
      text-align: justify;
      word-break: break-all;
    }
  }
</style>
