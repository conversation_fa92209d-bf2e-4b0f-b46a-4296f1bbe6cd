<template>
  <div class="app-header">
    <nav class="navbar">
      <div class="contaniner">
        <router-link class="me-auto hidden-xs-only" to="/">
          <img src="@/assets/images/logo.png" alt="" />
        </router-link>
        <router-link class="me-auto hidden-sm-and-up small-logo" to="/">
          <img src="@/assets/images/small-logo.png" alt="" />
        </router-link>
        <div style="width: 50%; margin-left: 45px" class="header-menu">
          <el-menu
            active-text-color="#333333"
            class="el-menu-demo"
            mode="horizontal"
            text-color="#333333"
            :default-active="activeMenu"
            :router="true"
          >
            <el-menu-item index="/home"
              >{{ $t('header.menu.home') }}
            </el-menu-item>
            <el-menu-item index="/submit/rawdata"
              >{{ $t('header.menu.submit') }}
            </el-menu-item>
            <el-menu-item index="/browse"
              >{{ $t('header.menu.browse') }}
            </el-menu-item>
            <el-menu-item index="/statistic"
              >{{ $t('header.menu.statistic') }}
            </el-menu-item>
            <el-menu-item index="/download"
              >{{ $t('header.menu.download') }}
            </el-menu-item>
            <el-menu-item index="/help"
              >{{ $t('header.menu.help') }}
            </el-menu-item>
            <el-menu-item
              v-if="!isPC && !useUserStore().logged"
              class="hidden-lg-only hidden-md-only"
              @click="useUserStore().casLogin()"
              >{{ $t('header.auth.login') }}
            </el-menu-item>
            <el-menu-item
              v-if="!isPC && !useUserStore().logged"
              class="hidden-lg-only hidden-md-only"
              ><a :href="bmdcRegisterUrl + '/register'">{{
                $t('header.auth.register')
              }}</a>
            </el-menu-item>
          </el-menu>
        </div>
        <div class="header-right-section d-flex align-items-center">
          <div class="hidden-xs-only">
            <el-input
              v-model="search"
              clearable
              class="search"
              :placeholder="$t('header.search.placeholder')"
              :prefix-icon="Search"
              @keyup.enter="doSearch"
            />
          </div>
          <div class="language-dropdown hidden-xs-only">
            <el-dropdown @command="handleLanguageChange">
              <span class="el-dropdown-link">
                {{ currentLanguage }}
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="en"
                    >{{ $t('header.language.english') }}
                  </el-dropdown-item>
                  <el-dropdown-item command="zh"
                    >{{ $t('header.language.chinese') }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div
            class="navbar-right"
            :class="useUserStore().logged ? '' : 'hidden-xs-only'"
          >
            <el-divider direction="vertical" />
            <div v-if="!useUserStore().logged" class="d-flex hidden-xs-only">
              <el-button class="border-0" @click="useUserStore().casLogin()">
                <span class="font-600">{{ $t('header.auth.login') }}</span>
              </el-button>
              <el-button type="primary">
                <a :href="bmdcRegisterUrl + '/register'">{{
                  $t('header.auth.register')
                }}</a>
              </el-button>
            </div>
            <div v-else class="d-flex cursor-pointer">
              <el-dropdown :width="200" @command="handleCommand">
                <div class="d-flex align-items-center">
                  <el-icon>
                    <User />
                  </el-icon>
                  <span class="ml-05" v-text="useUserStore().name"></span>
                  <el-icon>
                    <caret-bottom />
                  </el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item>
                      <router-link to="/userCenter"
                        >{{ $t('header.userMenu.userCenter') }}
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <router-link to="/submit/submission/list"
                        >{{ $t('header.userMenu.mySubmission') }}
                      </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item divided command="setting"
                      >{{ $t('header.userMenu.setting') }}
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout"
                      >{{ $t('header.userMenu.logout') }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </nav>
  </div>
</template>

<script setup>
  import { ArrowDown, Search } from '@element-plus/icons-vue';
  import { computed, onMounted, ref, watch } from 'vue';
  import useUserStore from '@/store/modules/user';
  import useRouteStore from '@/store/modules/route';
  import { storeToRefs } from 'pinia';
  import { isLogin } from '@/api/login';
  import { getToken } from '@/utils/auth';
  import { matches } from '@/permission';
  import { useRouter } from 'vue-router';
  import { trimStr } from '@/utils';
  import bus from '@/utils/bus';
  import { BusEnum } from '@/utils/enums';
  import { useI18n } from 'vue-i18n';
  import { setLocale } from '@/main.js';

  const router = useRouter();
  let routeStore = useRouteStore();
  const { locale } = useI18n();

  const activeMenu = ref('/submit');
  const search = ref('');

  const bmdcRegisterUrl = import.meta.env.VITE_BMDC_REGISTER_URL;

  // 当前语言显示
  const currentLanguage = computed(() => {
    return locale.value === 'zh' ? '中文' : 'English';
  });

  const { path } = storeToRefs(routeStore);

  // 检测设备类型函数
  const isPC = ref(true);

  onMounted(() => {
    // 通过检测设备的userAgent来判断是否为PC端
    const checkPC = () => {
      const userAgent = navigator.userAgent;
      isPC.value = !(
        userAgent.includes('Mobile') ||
        userAgent.includes('Android') ||
        userAgent.includes('iPhone')
      );
    };
    checkPC();

    // 监听窗口大小变化，实时更新设备类型
    window.onresize = () => {
      checkPC();
    };
  });

  watch(
    path,
    newValue => {
      if (newValue === '') {
        return;
      }
      isLogin().then(res => {
        if (res.data && (!getToken() || !useUserStore().name)) {
          useUserStore().casLogin();
        }
        if (
          (!res.data || !getToken() || !useUserStore().name) &&
          !matches(newValue)
        ) {
          useUserStore().casLogin();
        }
      });
    },
    { immediate: true },
  );

  function handleCommand(command) {
    switch (command) {
      case 'setting':
        // 转跳到bmdc的用户设置页面
        memberEditUrlFunc();
        break;
      case 'logout':
        logout();
        break;
      default:
        break;
    }
  }

  // 处理语言切换
  function handleLanguageChange(language) {
    setLocale(language);
  }

  function logout() {
    useUserStore()
      .casLogout()
      .then(() => {
        // 登出成功后重定向到首页
        location.href = '/';
      });
  }

  // 修改用户基本信息
  function memberEditUrlFunc() {
    document.write(
      `<form action='${bmdcRegisterUrl}/setting/member' method='post' id='memberEditForm' name='memberEditForm' style='display:none'>`,
    );
    document.write(
      `	<input type='hidden' name='memberId' value='${useUserStore().userId}'>`,
    );
    document.write(
      `<input type='hidden' name='refererUrl' value='${import.meta.env.VITE_LOGOUT_URL}'>`,
    );

    document.write('</form>');
    document.memberEditForm.submit();
  }

  // 顶部全文检索
  function doSearch() {
    const pathVal = trimStr(path.value);
    const browsePath = '/browse';
    const searchVal = trimStr(search.value);
    if (pathVal === browsePath || pathVal.startsWith(browsePath + '?')) {
      // 当前正在browse页面，则发送检索事件
      bus.emit(BusEnum.FULLTEXT_SEARCH, searchVal);
    }
    router.push({
      path: browsePath,
      query: { keyword: searchVal },
    });
  }
</script>

<style lang="scss" scoped>
  .app-header {
    top: 30px;
    right: 0;
    left: 0;
    z-index: 1030;

    .navbar {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      transition: all 0.5s ease;
      padding: 0;
      background-color: #fff;
      box-shadow: 0 3px 10px 0 rgba(49, 64, 71, 0.08);

      .contaniner {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 80%;
        padding: 0 10px;
        margin: 0 auto;
        max-width: 86%;

        .navbar-right {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        :deep(.el-menu--horizontal.el-menu) {
          justify-content: start;
          border-bottom: none !important;
        }

        .me-auto {
          padding: 5px 0;

          img {
            vertical-align: middle;
          }
        }

        .el-menu--horizontal > .el-menu-item {
          font-weight: 600;
          font-size: 16px;

          &.is-active {
            border-bottom: none !important;

            &:after {
              transform: scaleX(1);
            }
          }

          &:after {
            background-color: #3a78e8;
            bottom: -4px;
            content: '';
            height: 2px;
            left: 20%;
            position: absolute;
            transform: scaleX(0);
            transition: transform 0.3s ease 0s;
            width: 60%;
          }

          &:hover:after {
            transform: scaleX(1);
          }
        }

        :deep(.el-menu--horizontal > .el-sub-menu .el-sub-menu__title) {
          font-weight: 600;
        }

        :deep(.search .el-input__wrapper) {
          border-radius: 8px !important;
        }

        :deep(.search .el-input__prefix) {
          color: #666666 !important;
        }

        .language {
          :deep(.el-input__inner) {
            color: #666666;
            font-weight: 600;
          }

          :deep(.el-input__inner::placeholder),
          :deep.el-select .el-input .el-select__caret.el-icon {
            color: #666666;
            font-weight: 600;
          }

          :deep(.el-input__wrapper) {
            max-width: 110px !important;
            box-shadow: none !important;
          }
        }

        .el-divider--vertical {
          height: 58px;
        }

        .header-right-section {
          gap: 0;
          align-items: center;
        }

        .language-dropdown {
          width: 100px;
          display: flex;
          justify-content: center;
          margin-left: 12px;
          margin-right: 12px;
        }

        .el-dropdown-link {
          cursor: pointer;
          display: flex;
          align-items: center;
          font-weight: 600;
          white-space: nowrap;
          outline: none !important;
        }
      }
    }
  }

  .el-menu--horizontal .el-menu-item:not(.is-disabled):hover,
  .el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
    background-color: transparent !important;
  }

  .no-border:focus-visible {
    outline: none;
  }
  @media (max-width: 767px) {
    .small-logo img {
      width: 60px !important;
      max-width: 40px !important;
    }
    .header-menu {
      margin-left: 10px !important;
    }
    header .top-header .logo-bmdc-other {
      display: none;
    }
  }
</style>
