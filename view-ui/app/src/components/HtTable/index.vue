<template>
  <div v-loading="isLoading">
    <div
      class="mb-05 d-flex justify-space-between align-items-center select-column pos-relative plr-20 mt-1"
    >
      <div>
        <el-popover :teleported="false" :width="250">
          <template #reference>
            <el-tag
              effect="dark"
              class="font-600 select-col-btn"
              size="large"
              @click="openPopover"
            >
              <el-icon>
                <Menu />
              </el-icon>
              {{ t('htTable.selectColumn.title') }}
            </el-tag>
          </template>
          <div class="text-align-center text-main-color font-600">
            {{ t('htTable.columnVisibility.title') }}
          </div>
          <el-radio-group
            v-model="colShow"
            class="ml-4"
            @change="colVisibility"
          >
            <el-radio label="all" size="large">
              <span class="text-main-color col-radio">{{
                t('htTable.columnVisibility.allColumns')
              }}</span>
            </el-radio>
            <el-radio label="required" size="large">
              <span class="required-tag text-main-color col-radio">{{
                t('htTable.columnVisibility.requiredColumns')
              }}</span>
            </el-radio>
            <el-radio label="recommend" size="large">
              <span class="recommend-tag text-main-color col-radio">{{
                t('htTable.columnVisibility.recommendedColumns')
              }}</span>
            </el-radio>
            <el-radio label="optional" size="large">
              <span class="optional-tag text-main-color col-radio">{{
                t('htTable.columnVisibility.optionalColumns')
              }}</span>
            </el-radio>
          </el-radio-group>
          <el-divider class="mt-05 mb-05"></el-divider>
          <div class="d-flex">
            <el-checkbox
              v-model="checkAll"
              @change="handleCheckAllChange"
            ></el-checkbox>
            <el-input
              v-model.trim="filterText"
              class="w-85 ml-05"
              :placeholder="t('htTable.search.placeholder')"
              clearable
              @clear="clearable"
            />
          </div>
          <el-checkbox-group v-model="checkList" class="d-flex flex-wrap">
            <template v-for="item in hotColumns" :key="item.title">
              <el-checkbox
                v-if="isNotHide(item)"
                v-show="item.show"
                :label="item.title"
                @change="changeRadioStatus"
                >{{ item.title }}
              </el-checkbox>
            </template>
          </el-checkbox-group>
          <div class="d-flex mt-1 justify-center">
            <el-button
              type="primary"
              class="popover-btn"
              :size="'small'"
              @click="selectColumn"
              >{{ t('htTable.buttons.confirm') }}
            </el-button>
          </div>
        </el-popover>

        <el-tag
          v-if="sampleType"
          effect="dark"
          type="warning"
          size="large"
          class="font-600 select-col-btn ml-1"
          @click="showSampleDialog"
        >
          <el-icon>
            <Fold />
          </el-icon>
          {{ t('htTable.selectExisting', [sampleType]) }}
          {{ t('htTable.samples') }}
        </el-tag>
      </div>
      <div>
        <span class="text-secondary-color font-600 mr-05 color-key"
          >{{ t('htTable.colorKey.title') }}:</span
        >
        <el-tag round class="mr-05 required-tag"
          >{{ t('htTable.colorKey.requiredField') }}
        </el-tag>
        <el-tag round class="mr-05 recommend-tag"
          >{{ t('htTable.colorKey.recommendedField') }}
        </el-tag>
        <el-tag round class="mr-05 optional-tag"
          >{{ t('htTable.colorKey.optionalField') }}
        </el-tag>

        <el-tooltip
          effect="light"
          placement="left"
          :content="t('htTable.colorKey.invalidCellTooltip')"
          raw-content
        >
          <el-tag round class="invalid-tag"
            >{{ t('htTable.colorKey.invalidCell') }}
          </el-tag>
        </el-tooltip>
      </div>
    </div>
    <div class="submit-ht plr-20 mt-05">
      <div id="hotTable" class="pos-relative text-center"></div>
    </div>

    <el-dialog
      v-model="renameDiaOpen"
      :title="t('htTable.dialog.title')"
      width="880px"
      class="exp-dialog"
    >
      <el-form
        ref="renameRef"
        :model="form"
        label-width="130px"
        :inline="false"
        :rules="rules"
      >
        <el-form-item :label="t('htTable.form.name')" prop="name">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">{{ t('htTable.form.name') }}</span>
              <el-tooltip
                placement="top"
                :content="t('htTable.form.nameTooltip')"
                :teleported="false"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>

          <el-input v-model="form.name" style="width: 600px"></el-input>
        </el-form-item>

        <el-form-item :label="t('htTable.form.description')" prop="description">
          <template #label>
            <div class="d-flex align-items-center">
              <span class="font-700">{{ t('htTable.form.description') }}</span>
              <el-tooltip
                placement="top"
                :content="t('htTable.form.descriptionTooltip')"
                :teleported="false"
              >
                <el-icon class="ml-03 cursor-pointer">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>

          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            style="width: 600px"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="text-center">
          <el-button type="primary" @click="doRename"
            >{{ t('htTable.buttons.rename') }}
          </el-button>
          <el-button @click="renameDiaOpen = false"
            >{{ t('htTable.buttons.cancel') }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script setup>
  import {
    computed,
    defineEmits,
    defineModel,
    getCurrentInstance,
    nextTick,
    onMounted,
    ref,
    toRaw,
    watch,
  } from 'vue';
  import { useI18n } from 'vue-i18n';
  import Handsontable from 'handsontable';
  import { isArrEmpty, sleep, trimStr } from '@/utils';
  import sha256 from 'crypto-js/sha256';
  import { chineseValidator } from '@/utils/ht/validator';

  const { t } = useI18n();

  const emits = defineEmits(['openSampleDialog']);
  const { proxy } = getCurrentInstance();

  let props = defineProps({
    hotTableData: {
      type: Array,
      required: true,
      default: () => [],
    },
    hotColumns: {
      type: Array,
      required: true,
      default: () => [],
    },
    hideColIndex: {
      type: Array,
      required: false,
      default: () => [],
    },
    stretchH: {
      type: String,
      required: false,
      default: null,
    },
    sampleType: {
      type: String,
      required: false,
      default: null,
    },
    maxRows: {
      type: Number,
      required: false,
      default: 5000,
    },
    customColumn: {
      type: Boolean,
      required: false,
      default: () => false,
    },
    uploadTemp: {
      required: false,
      default: () => {
        return {
          uploadFlag: false,
          data: null,
        };
      },
    },
  });
  // 双向绑定属性
  const isLoading = defineModel({ default: false, required: false });

  const {
    hotTableData,
    hotColumns,
    maxRows,
    hideColIndex,
    customColumn,
    uploadTemp,
  } = props;

  const renameDiaOpen = ref(false);
  const renameColIndex = ref(-1);
  const form = ref({
    name: '',
    description: '',
  });
  const rules = ref({
    name: [
      {
        pattern: /^(?!\s+$)[-()/\w ]{1,60}$/,
        required: true,
        message: computed(() =>
          t('htTable.validation.invalidColumnNameFormat'),
        ),
        trigger: 'blur',
      },
    ],
    description: [
      {
        pattern: /^.{0,300}$/,
        required: false,
        message: computed(() =>
          t('htTable.validation.invalidDescriptionFormat'),
        ),
        trigger: 'blur',
      },
    ],
  });

  onMounted(() => {
    initHandsontable();
  });

  /** 重新加载数据，数据变更后调用此方法，不会销毁再重新渲染表格*/
  function updateHtData() {
    if (hotInstance.value && hotInstance.value.updateData) {
      // updateData会保留当前表格状态，比如排序、只读设置
      hotInstance.value.updateData(hotTableData);
      // loadData不会保留当前表格状态，将清除排序、只读设置
      // hotInstance.value.loadData(hotTableData);
      deleteEmptyRow();
    }
  }

  /** 通过excel模版上传后，添加模版文件中的自定义属性*/
  function addCustomColFromUpload() {
    // 如果是通过excel上传，则删除已保存的自定义属性，以excel内容为准
    if (uploadTemp.uploadFlag) {
      let baseCols = [];
      let baseTitles = [];
      hotColumns.forEach((item, index) => {
        if (!item.customAttrFlag) {
          baseCols.push(toRaw(item));
          baseTitles.push(item.title);
        }
      });
      // 添加excel模版中的自定义属性
      let uploadTempData = uploadTemp.data;
      if (uploadTempData) {
        for (let key in uploadTempData) {
          let title = trimStr(key);
          if (title && !baseTitles.includes(title)) {
            let val = trimStr(uploadTempData[key]);
            baseCols.push(colConstructor(key, val));
          }
        }
      }
      hotColumns.length = 0;
      hotColumns.push(...baseCols);
    }
  }

  // ht表格初始化
  function initHandsontable() {
    // 通过excel模版上传后，添加模版文件中的自定义属性
    addCustomColFromUpload();

    if (hotInstance.value && hotInstance.value.destroy) {
      hotInstance.value.destroy();
    }
    if (hotColumns.length === 0) {
      isLoading.value = false;
      hotTableData.length = 0;
      return false;
    }

    const dataLength = hotTableData.length;
    if (dataLength === 0) {
      // 补充空行
      const minRowCount = 10;
      if (dataLength < minRowCount) {
        for (let i = 0; i < minRowCount - dataLength; i++) {
          hotTableData.push({});
        }
      }
    }

    let tbHeight = 50 + hotTableData.length * 23;
    if (tbHeight <= 280) {
      // 设置最小高度，防止下拉框无法正常显示
      tbHeight = 280;
    } else if (tbHeight > 330) {
      tbHeight = 700;
    }
    const container = document.getElementById('hotTable');
    if (!container) {
      isLoading.value = false;
      return false;
    }
    for (let i = 0; i < hotColumns.length; i++) {
      let item = hotColumns[i];
      if (!item.validator) {
        item.validator = chineseValidator;
      }
    }
    console.log(hotColumns.length);
    const config = {
      data: hotTableData,
      colHeaders: true,
      // dragToScroll: true,
      columns: hotColumns,
      maxRows: maxRows < 3 ? 3 : maxRows,
      // height: 'auto', // 自动高度
      manualColumnResize: true, // 手动拉伸宽度
      height: tbHeight,
      // colWidths: 160, // 固定列宽度，将使autoColumnSize失效
      autoColumnSize: true,
      afterGetColHeader: function (col, th) {
        if (col !== -1) {
          if (hotColumns[col].isRequired === 'required') {
            th.style.backgroundColor = '#c8e6cb';
          } else if (hotColumns[col].isRequired === 'optional') {
            th.style.backgroundColor = '#e7e5a5';
          } else if (hotColumns[col].isRequired === 'recommend') {
            th.style.backgroundColor = 'rgba(143, 169, 218, 0.5)';
          }

          if (hotColumns[col].des) {
            th.classList.add('hoveredTH');
          }
        }
      },
      afterOnCellMouseOver: function (e, coords, th) {
        if (coords.row === -1 && coords.col !== -1) {
          if (hotColumns[coords.col].des) {
            popover = document.createElement('div');
            popover.classList.add('hover-comment');
            popover.innerHTML = hotColumns[coords.col].des;
            // popover.innerText = hotColumns[coords.col].des;

            // 设置位置
            const W = popover.offsetWidth / 2;
            const H = popover.offsetHeight / 2;
            popover.style.top = `${event.pageY - H}px`;
            popover.style.left = `${event.pageX - W + 10}px`;
            document.body.appendChild(popover);
          }
        }
      },
      afterOnCellMouseOut: function (e, coords, th) {
        if (
          popover &&
          coords.row === -1 &&
          coords.col !== -1 &&
          hotColumns[coords.col].des
        ) {
          document.body.removeChild(popover);
        }
      },
      comments: true,
      currentRowClassName: 'currentRow', // 突出显示行
      currentColClassName: 'currentCol', // 突出显示列
      rowHeaders: true, // 显示行号
      copyable: true, // 允许复制
      copyPaste: true, //复制粘贴
      filters: true, // 使用过滤功能
      readOnly: false,
      dropdownMenu: true, // 下拉菜单具体功能
      columnSorting: true, // 开启排序
      autoColumnValidation: true,
      contextMenu: {
        items: {
          cus_col: {
            name: t('htTable.contextMenu.addNewColumn'),
            hidden() {
              // `hidden` can be a boolean or a function
              // Hide the option when the first column was clicked
              // Returns the last coordinates applied to the table as a an array [startRow, startCol, endRow, endCol].
              return this.getSelectedLast()[1] === -1; // `this` === hot
            },
            callback: (key, option) => {
              insertCol(key, option);
            },
          },
          /*cus_col_right: {
    name: 'Insert column right',
    hidden() {
      return this.getSelectedLast()[1] === -1;
    },
    callback: (key, option) => {
      insertCol(key, option);
    },
  },*/
          cus_rename_col: {
            name: t('htTable.contextMenu.renameColumn'),
            hidden() {
              return this.getSelectedLast()[1] === -1;
            },
            callback: (key, option) => {
              renameCol(key, option);
            },
          },
          sp1: '---------',
          row_above: {},
          row_below: {},
          sp2: '---------',
          remove_row: {},
          cus_remove_col: {
            name: t('htTable.contextMenu.removeColumn'),
            hidden() {
              return this.getSelectedLast()[1] === -1;
            },
            callback: (key, option) => {
              removeCol(key, option);
            },
          },
          clear_column: {},
          sp3: '---------',
          undo: {},
          redo: {},
          sp4: '---------',
          make_read_only: {},
          sp5: '---------',
          alignment: {},
          sp6: '---------',
          cut: {},
          copy: {},
          sp7: '---------',
          /*export_csv: {
            name: 'Export Csv',
            callback: (key, option) => {
              exportCsv();
            },
          },*/
        },
      },
      licenseKey: 'non-commercial-and-evaluation', //去除底部非商用声明
      hiddenColumns: {
        columns: [], // 要隐藏的列索引数组
      },
      columnSummary: [],
    };

    // 列的增删改操作
    if (!customColumn) {
      delete config.contextMenu.items.cus_col;
      // delete config.contextMenu.items.cus_col_right;
      delete config.contextMenu.items.cus_rename_col;
      delete config.contextMenu.items.cus_remove_col;
    }

    // 设置默认隐藏列
    if (!isArrEmpty(hideColIndex)) {
      config.hiddenColumns.columns = hideColIndex;
    }
    if (props.stretchH) {
      config['stretchH'] = props.stretchH;
      config['width'] = '100%';
    }
    hotInstance.value = new Handsontable(container, config);
    hideColPlugin = hotInstance.value.getPlugin('hiddenColumns');
    nextTick(() => {
      if (dataLength !== 0) {
        hotInstance.value.validateCells();
      }
      // 记录旧数据
      recordOldData();
      isLoading.value = false;
    });
  }

  function exportCsv() {
    if (hotInstance.value) {
      const exportPlugin = hotInstance.value.getPlugin('exportFile');
      exportPlugin.downloadFile('csv', {
        bom: false,
        columnDelimiter: '\t',
        columnHeaders: true,
        exportHiddenColumns: true,
        exportHiddenRows: true,
        fileExtension: 'tsv',
        filename: `export-csv_[YYYY]-[MM]-[DD]`,
        mimeType: 'text/tab-separated-values',
        rowDelimiter: '\r\n',
        rowHeaders: false,
      });
    }
  }

  // 新增列
  function insertCol(key, option) {
    /*const start = option[0].start.col;
let i;
if (key === 'cus_col_left') {
i = start;
} else {
i = start + 1;
}
hotColumns.splice(i, 0, initColObj('new_' + trimStr(newColNo.value++)));
*/
    let colObj = initColObj();
    if (colObj) {
      hotColumns.push(colObj);
      hotInstance.value.updateSettings({
        columns: hotColumns,
      });
      selectColByIndex(hotColumns.length - 1);
    }
  }

  // 选中最后一行
  function selectColByIndex(index) {
    nextTick(async () => {
      doSelectHeadCell(0);
      await sleep(100);
      doSelectHeadCell(index);
      await sleep(100);
      doSelectHeadCell(index);
      // hotInstance.value.scrollViewportTo({ col: start });
    });
  }

  // 新增列配置对象
  function initColObj() {
    let title;
    let all = hotInstance.value.getColHeader();
    let notSame = false;
    for (let i = 0; i < 20; i++) {
      title = 'attributes_' + trimStr(newColNo.value++);
      if (!all.includes(title)) {
        notSame = true;
        break;
      }
    }
    if (!notSame) {
      return null;
    }
    return colConstructor(title, null);
  }

  function colConstructor(title, des) {
    return {
      customAttrFlag: true,
      title: title,
      data: title,
      allowCreate: false,
      des: des ? des : null,
      show: true,
      isRequired: 'optional',
      type: 'text',
      wordWrap: false,
      validator: chineseValidator,
    };
  }

  // 选中首行指定列的单元格
  function doSelectHeadCell(i) {
    // hotInstance.value.selectColumns(i < 0 ? 0 : i);
    // hotInstance.value.scrollViewportTo({ col: start });
    hotInstance.value.selectCell(0, i < 0 ? 0 : i);
  }

  // 删除列
  function removeCol(key, option) {
    const start = option[0].start.col;
    if (hotColumns[start].isRequired === 'required') {
      proxy.$modal.alertError(t('htTable.messages.cannotDeleteRequiredColumn'));
      return false;
    }
    hotColumns.splice(start, 1);
    hotInstance.value.updateSettings({
      columns: hotColumns,
    });
  }

  // 列重命名
  function renameCol(key, option) {
    const start = option[0].start.col;
    let hotColumn;
    const otherTitleSet = new Set();
    hotColumns.forEach((item, index) => {
      if (index === start) {
        hotColumn = item;
      } else {
        otherTitleSet.add(trimStr(item.title));
      }
    });
    if (!hotColumn) {
      return false;
    }
    if (hotColumn.isRequired === 'required') {
      proxy.$modal.alertError(t('htTable.messages.cannotRenameRequiredColumn'));
      return false;
    }

    renameColIndex.value = start;
    renameDiaOpen.value = true;
    const currRow = proxy.$_.cloneDeep(toRaw(hotColumn));
    form.value.name = trimStr(currRow.title);
    form.value.description = trimStr(currRow.des);
    /*ElMessageBox.prompt('Please input column name', 'Rename column', {
confirmButtonText: 'Confirm',
cancelButtonText: 'Cancel',
type: 'warning',
inputValue: hotColumn.title,
inputPattern: /^[a-zA-Z0-9_ (){}[\]]{1,40}$/,
inputErrorMessage: 'Invalid column name',
})
.then(({ value }) => {
value = trimStr(value);
if (!value) {
  proxy.$modal.alertError('Column name cannot be empty');
  return false;
}
if (otherTitleSet.has(value)) {
  proxy.$modal.alertError('Column name already exists');
  return false;
}
hotColumns[start].title = value;
// let sourceData = hotInstance.value.getSourceData(start);
// let sourceData = hotInstance.value.getSourceDataAtCol(start);
hotInstance.value.updateSettings({
  columns: hotColumns,
});
})
.catch(() => {
// console.log(2);
});*/
  }

  // 重命名
  function doRename() {
    proxy.$refs['renameRef'].validate(valid => {
      if (valid) {
        const start = renameColIndex.value;
        let hotColumn;
        const otherTitleSet = new Set();
        hotColumns.forEach((item, index) => {
          if (index === start) {
            hotColumn = item;
          } else {
            otherTitleSet.add(trimStr(item.title));
          }
        });
        if (!hotColumn) {
          return false;
        }
        let value = form.value.name;
        let description = form.value.description;
        if (otherTitleSet.has(value)) {
          proxy.$modal.alertError(
            t('htTable.messages.columnNameAlreadyExists'),
          );
          return false;
        }
        hotColumns[start].title = value;
        hotColumns[start].des = description;
        // let sourceData = hotInstance.value.getSourceData(start);
        // let sourceData = hotInstance.value.getSourceDataAtCol(start);
        hotInstance.value.updateSettings({
          columns: hotColumns,
        });
        renameDiaOpen.value = false;
        selectColByIndex(start);
      }
    });
  }

  const oldData = ref('');

  /** 等所有的子组件全部渲染完成后序列化json */
  function recordOldData() {
    // 等所有的子组件全部渲染完成后序列化json
    nextTick().then(() => {
      oldData.value = dataHashStr();
    });
  }

  /**
   * 获取表格数据哈希值，用于判断数据是否变动
   */
  function dataHashStr() {
    return trimStr(
      sha256(JSON.stringify(hotInstance.value.getData())).toString(),
    );
  }

  /** 校验表单数据是否被更改 */
  function validateChanged() {
    return trimStr(oldData.value) === dataHashStr();
  }

  /** 刷新旧数据 */
  function refreshOldData() {
    oldData.value = dataHashStr();
  }

  // 表格对象里面的实例变量
  const hotInstance = ref({});
  const newColNo = ref(0);
  // 表头描述弹窗
  var popover = null;
  // 表格列隐藏显示插件
  let hideColPlugin = null;
  /**--- 表格列隐藏显示功能 start ---*/
  const visible = ref(false);
  const checkList = ref([]);
  const colShow = ref('');
  const checkAll = ref(false);
  const filterText = ref('');
  const map = new Map();

  function isNotHide(item) {
    return item.isRequired !== 'hide';
  }

  const getShowCol = () => {
    hotColumns.forEach(item => {
      if (item.show && isNotHide(item)) {
        map.set(item.title, item.title);
      }
    });
  };

  const selectColumn = () => {
    colShow.value = '';
    const columnIndexes = allColIndex();
    const showIndex = checkList.value.map(item =>
      hotColumns.findIndex(obj => obj.title === item),
    );
    hideColPlugin.hideColumns(columnIndexes);
    hideColPlugin.showColumns(showIndex);
    hotInstance.value.render();
    visible.value = false;
  };

  const handleCheckAllChange = val => {
    getShowCol();
    map.forEach((item, key) => {
      if (val) {
        checkList.value.push(key);
      } else {
        const index = checkList.value.indexOf(key);
        if (index > -1) {
          checkList.value.splice(index, 1);
        }
      }
    });
    checkList.value = [...new Set(checkList.value)];
    map.clear();
  };

  const changeRadioStatus = () => {
    getShowCol();
    const allValuesExist = Array.from(map.values()).every(value =>
      checkList.value.includes(value),
    );
    checkAll.value = !!allValuesExist;
    map.clear();
  };

  const clearable = () => {
    checkAll.value = hotColumns.length === checkList.value.length;
  };

  function allColIndex() {
    const arr = [];
    hotColumns.forEach(function (item, index) {
      if (isNotHide(item)) {
        arr.push(index);
      }
    });
    return arr;
  }

  const colVisibility = () => {
    checkList.value = [];
    const columnIndexes = allColIndex();
    let showIndex;
    if (colShow.value === 'all') {
      showIndex = [...columnIndexes];
    } else {
      showIndex = hotColumns
        .map((it, index) => {
          if (it.isRequired === colShow.value) {
            return index;
          }
        })
        .filter(ele => ele !== undefined);
    }
    hideColPlugin.hideColumns(columnIndexes);
    hideColPlugin.showColumns(showIndex);
    hotInstance.value.render();

    visible.value = false;
  };

  const openPopover = () => {
    visible.value = !visible.value;
    filterText.value = '';
    clearable();
  };

  // 表格列隐藏/显示面板--搜索框字段监听器
  watch(filterText, (newValue, oldValue) => {
    // const keyWord = trim(newVal); // 去除空格;
    let val = trimStr(newValue).toLowerCase();
    if (val) {
      hotColumns.forEach(ele => {
        if (isNotHide(ele)) {
          ele.show = ele.title.toLowerCase().includes(val);
        }
      });
    } else {
      hotColumns.forEach(ele => {
        if (isNotHide(ele)) {
          ele.show = true;
        }
      });
    }
  });

  /** excel上传后修改表格数据 */
  function changeTbData(excelData) {
    hotTableData.length = 0;
    excelData.forEach((item, index) => {
      let row = {};
      for (let key2 in item) {
        row[key2] = item[key2]['value'];
      }
      hotTableData.push(row);
    });
    initHandsontable();
  }

  /** 打开样本筛选弹窗 */
  function showSampleDialog() {
    emits('openSampleDialog');
  }

  /** 删除空行 */
  function deleteEmptyRow() {
    let datas = hotInstance.value.getData();
    const len = datas.length;
    if (len === 0) {
      return false;
    }
    const emptyRowIndex = [];
    for (let i = 0; i < len; i++) {
      let row = datas[i];
      let isEmpty = true;
      if (row && row.length > 0) {
        let colCount = row.length;
        for (let j = 0; j < colCount; j++) {
          if (trimStr(row[j])) {
            isEmpty = false;
            break;
          }
        }
      }
      if (isEmpty) {
        emptyRowIndex.push(i);
      }
    }
    if (!isArrEmpty(emptyRowIndex)) {
      // 对行号进行降序排序，避免删除后的行号变化导致的问题
      const sortedRows = emptyRowIndex.sort((a, b) => b - a);
      sortedRows.forEach(row => {
        hotInstance.value.alter('remove_row', row);
      });
    }
  }

  /** 删除空行，校验表格数据，并选中第一个校验失败的单元格 */
  function validatePromise() {
    return new Promise((resolve, reject) => {
      // 先删除空行
      deleteEmptyRow();
      // 然后调用校验器
      hotInstance.value.validateCells(valid => {
        if (!valid) {
          const cells = hotInstance.value.getCellsMeta();
          if (cells) {
            // cells.reverse();
            // 选中验证出错的项目
            (async () => {
              for (let item of cells) {
                if (!item.valid) {
                  doSelectHeadCell(0);
                  await sleep(100);
                  hotInstance.value.selectCell(item.row, item.col);
                  break; // 直接跳出循环
                }
              }
            })();
          }
          // hotInstance.value.setCellMeta(2, 1, 'valid', false);
          // 校验失败
          resolve(false);
        } else {
          resolve(true);
        }
      });
    });
  }

  /** 获取表格自定义属性的标题和描述信息 */
  function allAttrDes() {
    let desData = null;
    if (hotColumns) {
      const length = hotColumns.length;
      if (length === 0) {
        return desData;
      }
      desData = [];
      for (let i = 0; i < length; i++) {
        let item = hotColumns[i];
        let des = trimStr(item.des);
        if (des && item.customAttrFlag) {
          desData.push({
            attributesField: item.title,
            description: des,
          });
        }
      }
    }
    return desData;
  }

  // 暴露子组件变量和方法，父组件使用$refs可以调用
  defineExpose({
    hotInstance,
    hotTableData,
    initHandsontable,
    changeTbData,
    validateChanged,
    refreshOldData,
    updateHtData,
    validatePromise,
    allAttrDes,
  });
</script>
<style scoped lang="scss">
  .select-column {
    .select-col-btn {
      color: #ffffff;

      :deep(.el-tag__content) {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: #ffffff;
      }

      &:hover {
        cursor: pointer;
      }
    }

    .el-tag {
      color: #333333;
    }

    .required-tag {
      border: none;
      background-color: #c8e6cb;
    }

    .recommend-tag {
      border: none;
      background-color: rgba(143, 169, 218, 0.5);
    }

    .optional-tag {
      border: none;
      background-color: #e7e5a5;
    }

    .invalid-tag {
      border: none;
      background-color: #ffbeba;
    }

    .col-radio {
      padding: 2px 6px;
      border-radius: 12px;
    }

    :deep(.el-radio.el-radio--large .el-radio__label) {
      font-size: 12px !important;
    }

    .color-key {
      font-size: 13px;
    }

    .w-85 {
      width: 85%;
    }

    .popover-btn {
      :deep(span) {
        padding-top: 1px;
      }
    }
  }

  .el-checkbox-group {
    height: 200px;
    overflow-y: auto;

    :deep(.el-checkbox) {
      width: 100%;
      margin-right: 65px;
      //&:nth-child(odd) {
      //  margin-right: 70px;
      //}
    }

    :deep(.el-checkbox__label) {
      font-size: 12px;
    }
  }

  :deep(.el-upload-dragger) {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 16px;
    background-color: #eff3f9;

    .el-icon--upload {
      margin-right: 0.5rem;
      font-size: 40px;
      color: #fe7f2b;
      margin-bottom: 0;
    }
  }

  :deep(.el-upload-list) {
    margin: 0;
  }

  :deep(.submit-ht .handsontable th) {
    max-width: 360px !important;
    text-overflow: ellipsis;
  }

  :deep(.submit-ht .handsontable td) {
    max-width: 360px !important;
    text-overflow: ellipsis;
  }
</style>
