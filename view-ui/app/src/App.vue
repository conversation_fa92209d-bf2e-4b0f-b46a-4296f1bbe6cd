<script setup>
  import { RouterView } from 'vue-router';
  import Header from '@/components/header.vue';
  // 导入 Element Plus 语言包
  import zhCn from 'element-plus/es/locale/lang/zh-cn';
  import enEl from 'element-plus/es/locale/lang/en';
  import { computed, onMounted, ref } from 'vue';
  import { useI18n } from 'vue-i18n';

  let showScrollBtn = ref(false);

  onMounted(() => {
    window.addEventListener('scroll', () => {
      let scrollHeight = document.documentElement.scrollTop;
      showScrollBtn.value = scrollHeight >= 50;
    });
  });

  function handleBackToTop() {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  }

  const { locale } = useI18n(); // 获取 vue-i18n 的当前 locale

  // Element Plus 语言映射
  const elLangMap = {
    zh: zhCn,
    en: enEl,
  };

  // 根据 vue-i18n 的 locale 计算 Element Plus 的语言包
  const currentElLocale = computed(() => {
    console.log('Current vue-i18n locale:', locale.value);
    console.log('Element Plus locale:', elLangMap[locale.value]);
    return elLangMap[locale.value];
  });
</script>

<template>
  <el-config-provider :locale="currentElLocale">
    <div class="app-container">
      <Header />
      <div class="page-content">
        <router-view v-slot="{ Component, route }">
          <transition name="el-fade-in">
            <keep-alive v-if="route.meta.keepAlive">
              <component :is="Component" />
            </keep-alive>
            <component :is="Component" v-else />
          </transition>
        </router-view>
      </div>
      <el-button
        v-if="showScrollBtn"
        class="scrollBtn"
        type="primary"
        @click="handleBackToTop"
      >
        <i class="mdi mdi-arrow-up" />
      </el-button>
    </div>
  </el-config-provider>
</template>

<style lang="scss" scoped>
  .page-content {
    min-height: calc(100vh - 100px);
  }

  .scrollBtn {
    position: fixed;
    bottom: 20px;
    right: 30px;
    z-index: 99;
    padding: 8px !important;

    &:hover {
      animation: fade-up 1.5s infinite linear;
    }
  }

  @keyframes fade-up {
    0% {
      transform: translateY(0);
      opacity: 1;
    }
    75% {
      transform: translateY(-20px);
      opacity: 0;
    }
  }
</style>
