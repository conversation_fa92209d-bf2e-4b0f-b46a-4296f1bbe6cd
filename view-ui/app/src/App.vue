<script setup>
  import { RouterView } from 'vue-router';
  import Header from '@/components/header.vue';
  import { onMounted, onUnmounted, ref } from 'vue';
  import { getCurrentElementLocale } from '@/main.js';

  let showScrollBtn = ref(false);
  let currentElementLocale = ref(getCurrentElementLocale());

  // 监听语言切换事件
  const handleLocaleChange = (event) => {
    currentElementLocale.value = event.detail.elementLocale;
  };

  onMounted(() => {
    window.addEventListener('scroll', () => {
      let scrollHeight = document.documentElement.scrollTop;
      showScrollBtn.value = scrollHeight >= 50;
    });

    // 监听语言切换事件
    window.addEventListener('locale-change', handleLocaleChange);
  });

  onUnmounted(() => {
    window.removeEventListener('locale-change', handleLocaleChange);
  });

  function handleBackToTop() {
    window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  }
</script>

<template>
  <el-config-provider :locale="currentElementLocale">
    <div class="app-container">
      <Header />
      <div class="page-content">
        <router-view v-slot="{ Component, route }">
          <transition name="el-fade-in">
            <keep-alive v-if="route.meta.keepAlive">
              <component :is="Component" />
            </keep-alive>
            <component :is="Component" v-else />
          </transition>
        </router-view>
      </div>
      <el-button
        v-if="showScrollBtn"
        class="scrollBtn"
        type="primary"
        @click="handleBackToTop"
      >
        <i class="mdi mdi-arrow-up" />
      </el-button>
    </div>
  </el-config-provider>
</template>

<style lang="scss" scoped>
  .page-content {
    min-height: calc(100vh - 100px);
  }

  .scrollBtn {
    position: fixed;
    bottom: 20px;
    right: 30px;
    z-index: 99;
    padding: 8px !important;

    &:hover {
      animation: fade-up 1.5s infinite linear;
    }
  }

  @keyframes fade-up {
    0% {
      transform: translateY(0);
      opacity: 1;
    }
    75% {
      transform: translateY(-20px);
      opacity: 0;
    }
  }
</style>
