// src/stores/chineseConfig.js
import { defineStore } from 'pinia';
import { getConfigKey } from '@/api/system/config';

export const useChineseConfigStore = defineStore('chineseConfig', {
  state: () => ({
    chineseValidate: null,
    loaded: false,
  }),
  getters: {
    chineseValidateValue() {
      return this.chineseValidate;
    },
  },
  actions: {
    async loadConfig() {
      // 避免重复加载
      if (this.loaded) return;

      try {
        const res = await getConfigKey('node.chinese.validate');
        this.chineseValidate = res?.msg || null;
        this.loaded = true;
      } catch (e) {
        console.error('加载配置失败:', e);
      }
    },
  },
});
