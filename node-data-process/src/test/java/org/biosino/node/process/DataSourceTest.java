package org.biosino.node.process;

import lombok.extern.slf4j.Slf4j;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.node.process.config.NodeMongoConfig;
import org.biosino.node.process.config.NodeV1MongoConfig;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/5/27
 */
@SpringBootTest
@Slf4j
public class DataSourceTest {
    @Resource
    @Qualifier(NodeMongoConfig.MONGO_TEMPLATE)
    private MongoTemplate nodeV2Template;
    @Resource
    @Qualifier(NodeV1MongoConfig.MONGO_TEMPLATE)
    private MongoTemplate nodeV1Template;

    @Test
    public void testV2() {
        Analysis analysis = nodeV2Template.findOne(new Query(), Analysis.class);
        log.info(analysis.toString());
    }

    @Test
    public void testV1() {
        Analysis analysis = nodeV1Template.findOne(new Query(), Analysis.class);
        log.info(analysis.toString());
    }

    @Test
    public void testPattern() {
        String pattern = "^([A-Za-z]+)([0-9]{2,8}$)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher("OEP000034");
        System.out.println(m.matches());
    }

}
