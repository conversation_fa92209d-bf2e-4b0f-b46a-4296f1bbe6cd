server:
  port: 8091
spring:
  data:
    mongodb:
      # node数据源
      node:
        uri: ***********************************************************************************
      # node-v1数据源
      node1:
        uri: ***************************************************************************
      # taxonomy数据源
      taxonomy:
        uri: **************************************************************************

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **************************************************************************************************************************
      username: root
      password: Lfgzs@2021
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: false
        wall:
          config:
            multi-statement-allow: true
