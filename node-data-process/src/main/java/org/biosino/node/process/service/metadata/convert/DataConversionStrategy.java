package org.biosino.node.process.service.metadata.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.ArchiveEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.mongo.entity.Data;
import org.biosino.node.process.model.mongo.v1.DataV1;
import org.biosino.node.process.service.common.ConversionCommon;
import org.biosino.node.process.service.common.ConversionStrategy;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.biosino.node.process.service.common.ConversionCommon.*;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Slf4j
public class DataConversionStrategy implements ConversionStrategy<DataV1, Data> {


    private static final File logFile = FileUtil.isWindows()
            ? FileUtil.file("D:/test/node/transfer/data.tsv")
            : FileUtil.file("/tmp/data.tsv");

    @Override
    public Data convert(DataV1 source) {
        return copyV1ToV2(source);
    }

    private Data copyV1ToV2(DataV1 v1) {
        String dataNo = v1.getDatNo();
        String analNo = v1.getAnalNo();
        String runNo = v1.getRunNo();

        if (StrUtil.isBlank(dataNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "dat_no", "dat_no is null"));
            return null;
        }

        if (!isNodeNo(dataNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "dat_no", "dat_no is not node number"));
            return null;
        }

        if (StrUtil.isBlank(v1.getFilePath())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "file_path", "file_path is null"));
            return null;
        }

        if (!StrUtil.equalsAny(v1.getArchived(), ArchiveEnum.yes.name(), ArchiveEnum.no.name())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "archived", "archived is not yes or no"));
            return null;
        }

        if (v1.getCreateDate() == null) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "submission_date", "submission_date is null"));
            return null;
        }

        if (StrUtil.isBlank(v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is null"));
            return null;
        }

        if (!StrUtil.equals(v1.getOwnership(), OwnershipEnum.self_support.getDesc())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "ownership", "ownership is not self_support"));
            return null;
        }

        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), v1.getSecurity())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "security", "security is not in includeAllSecurity[Public,Restricted,Private]"));
            return null;
        }

        if (ArrayUtil.contains(ConversionCommon.blackMemberIdArr, v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is in black member list"));
            return null;
        }


        Data item = new Data();

        BeanUtil.copyProperties(v1, item);

        // array 2 list
        if (ArrayUtil.isNotEmpty(v1.getUsedIds())) {
            item.setUsedIds(CollUtil.newArrayList(v1.getUsedIds()));
        }

        // 默认值
        if (item.getUpdateDate() == null) {
            item.setUpdateDate(item.getCreateDate());
        }

        if (StrUtil.isBlank(v1.getFileName())) {
            item.setFileName(v1.getName());
        }

        if (StrUtil.isBlank(v1.getName())) {
            item.setName(v1.getFileName());
        }

        // id 升级
        if (StrUtil.isNotBlank(runNo)) {
            String newRunNo = getNewNo(runNo);
            item.setRunNo(newRunNo);
        }
        if (StrUtil.isNotBlank(analNo)) {
            String newAnalNo = getNewNo(analNo);
            item.setAnalNo(newAnalNo);
        }

        String newDataNo = getNewNo(dataNo);
        item.setDatNo(newDataNo);

        List<String> usedIds = item.getUsedIds() != null ? item.getUsedIds() : new ArrayList<>();
        if (!CollUtil.contains(usedIds, dataNo)) {
            usedIds.add(dataNo);
            item.setUsedIds(usedIds);
        }

        // 如果数据没有归档，就得复制一份tempData到里面
        if (StrUtil.equals(item.getArchived(), ArchiveEnum.no.name())) {
            Data tempData = new Data();
            BeanUtil.copyProperties(item, tempData);
            item.setTempData(tempData);
        }

        return item;
    }
}
