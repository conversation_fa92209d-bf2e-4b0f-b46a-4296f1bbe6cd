package org.biosino.node.process.service.metadata.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Analysis;
import org.biosino.common.mongo.entity.other.AnalysisTarget;
import org.biosino.common.mongo.entity.other.Pipeline;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.node.process.model.mongo.v1.AnalysisTargetV1;
import org.biosino.node.process.model.mongo.v1.AnalysisV1;
import org.biosino.node.process.service.common.ConversionCommon;
import org.biosino.node.process.service.common.ConversionStrategy;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static org.biosino.node.process.service.common.ConversionCommon.*;

/**
 * <AUTHOR> Li
 * @date 2024/6/13
 */
@Slf4j
public class AnalysisConversionStrategy implements ConversionStrategy<AnalysisV1, Analysis> {

    private static final File logFile = FileUtil.isWindows() ?
            FileUtil.file("D:/test/node/transfer/analysis.tsv") :
            FileUtil.file("/tmp/analysis.tsv");

    @Override
    public Analysis convert(AnalysisV1 source) {
        return copyV1ToV2(source);
    }

    private Analysis copyV1ToV2(AnalysisV1 v1) {

        String analNo = v1.getAnalysisNo();

        if (StrUtil.isBlank(analNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "anal_no", "anal_no is null"));
            return null;
        }

        if (!isNodeNo(analNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "anal_no", "anal_no is not node number"));
            return null;
        }

        if (v1.getCreateDate() == null) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "submission_date", "submission_date is null"));
            return null;
        }

        if (v1.getCreator() == null) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is null"));
            return null;
        }

        if (!StrUtil.equals(v1.getOwnership(), OwnershipEnum.self_support.getDesc())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "ownership", "ownership is not self_support"));
            return null;
        }

        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), v1.getSecurity())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "security", "security is not in includeAllSecurity[Public,Restricted,Private]"));
            return null;
        }

        if (!CollUtil.contains(VisibleStatusEnum.includeExistsVisibleStatus(), v1.getVisibleStatus())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "visible_status", "visible_status is not in includeAllVisibleStatus[Accessible,Unaccessible]"));
            return null;
        }

        if (ArrayUtil.contains(ConversionCommon.blackMemberIdArr, v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is in black member list"));
            return null;
        }

        Analysis item = new Analysis();
        BeanUtil.copyProperties(v1, item);

        // array 2 list
        item.setPipeline(CollUtil.newArrayList(v1.getPipeline()));

        // 默认值
        if (v1.getSecurity() != null && v1.getSecurity().contains("Deleted")) {
            item.setVisibleStatus(VisibleStatusEnum.Deleted.name());
        }

        if (item.getUpdateDate() == null) {
            item.setUpdateDate(item.getCreateDate());
        }

        if (item.getExportNum() == null) {
            item.setExportNum(0L);
        }
        if (item.getHitNum() == null) {
            item.setHitNum(0L);
        }
        if (StrUtil.isNotBlank(v1.getSourceProject())) {
            item.setSourceProject(CollUtil.newArrayList(v1.getSourceProject()));
        } else {
            item.setSourceProject(null);
        }
        item.setAudited(AuditEnum.audited.name());

        // 完善submitter
        if (item.getSubmitter() == null) {
            Submitter submitter = getSubmitter(item.getCreator());
            item.setSubmitter(submitter);
        } else {
            item.setSubmitter(fillInMember(item.getSubmitter()));
        }

        if (v1.getTarget() != null) {
            setTarget(v1, item);
        }
        // pipeline中的output升级
        if (ArrayUtil.isNotEmpty(v1.getPipeline())) {
            item.setPipeline(item.getPipeline().stream().map(x -> {
                if (CollUtil.isNotEmpty(x.getOutput())) {
                    // 过滤掉空字符串再进行id升级
                    x.setOutput(x.getOutput().stream().filter(StrUtil::isNotBlank).map(ConversionCommon::getNewNo).collect(Collectors.toList()));
                }
                return x;
            }).collect(Collectors.toList()));
        }

        // id 升级
        String newAnalNo = getNewNo(analNo);
        item.setAnalysisNo(newAnalNo);
        List<String> usedIds = item.getUsedIds() != null ? item.getUsedIds() : new ArrayList<>();
        if (!CollUtil.contains(usedIds, analNo)) {
            usedIds.add(analNo);
            item.setUsedIds(usedIds);
        }

        return item;
    }

    private void setPipeline(AnalysisV1 v1, Analysis item) {
        for (Pipeline pipelinev1 : v1.getPipeline()) {
            Pipeline pipeline = new Pipeline();
            BeanUtil.copyProperties(pipelinev1, pipeline);
            pipeline.setOutput(pipelinev1.getOutput().stream().map(ConversionCommon::getNewNo).collect(Collectors.toList()));
        }
    }

    private static void setTarget(AnalysisV1 v1, Analysis item) {
        List<AnalysisTarget> analysisTargets = new ArrayList<>();

        AnalysisTargetV1[] analysisV1Target = v1.getTarget();

        Set<String> projNoSet = new HashSet<>();
        Set<String> expNoSet = new HashSet<>();
        Set<String> sapNoSet = new HashSet<>();
        Set<String> runNoSet = new HashSet<>();
        Set<String> analNoSet = new HashSet<>();
        Set<String> dataNoSet = new HashSet<>();

        for (AnalysisTargetV1 analysisTargetV1 : analysisV1Target) {
            String[] projNo = analysisTargetV1.getProj_no();
            if (projNo != null && projNo.length != 0) {
                for (String no : projNo) {
                    projNoSet.add(getNewNo(no));
                }
            }

            String[] expNo = analysisTargetV1.getExp_no();
            if (expNo != null && expNo.length != 0) {
                for (String no : expNo) {
                    expNoSet.add(getNewNo(no));
                }
            }

            String[] sapNo = analysisTargetV1.getSap_no();
            if (sapNo != null && sapNo.length != 0) {
                for (String no : sapNo) {
                    sapNoSet.add(getNewNo(no));
                }
            }

            String[] runNo = analysisTargetV1.getRun_no();
            if (runNo != null && runNo.length != 0) {
                for (String no : runNo) {
                    runNoSet.add(getNewNo(no));
                }
            }

            String[] dataNo = analysisTargetV1.getData_no();
            if (dataNo != null && dataNo.length != 0) {
                for (String no : dataNo) {
                    dataNoSet.add(getNewNo(no));
                }
            }

            String[] analNo = analysisTargetV1.getAnal_no();
            if (analNo != null && analNo.length != 0) {
                for (String no : analNo) {
                    analNoSet.add(getNewNo(no));
                }
            }
        }

        if (CollUtil.isNotEmpty(projNoSet)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setNos(CollUtil.newArrayList(projNoSet));
            analysisTarget.setType("project");
            analysisTargets.add(analysisTarget);
        }

        if (CollUtil.isNotEmpty(expNoSet)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setNos(CollUtil.newArrayList(expNoSet));
            analysisTarget.setType("experiment");
            analysisTargets.add(analysisTarget);
        }

        if (CollUtil.isNotEmpty(sapNoSet)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setNos(CollUtil.newArrayList(sapNoSet));
            analysisTarget.setType("sample");
            analysisTargets.add(analysisTarget);
        }

        if (CollUtil.isNotEmpty(runNoSet)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setNos(CollUtil.newArrayList(runNoSet));
            analysisTarget.setType("run");
            analysisTargets.add(analysisTarget);
        }

        if (CollUtil.isNotEmpty(analNoSet)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setNos(CollUtil.newArrayList(analNoSet));
            analysisTarget.setType("analysis");
            analysisTargets.add(analysisTarget);
        }

        if (CollUtil.isNotEmpty(dataNoSet)) {
            AnalysisTarget analysisTarget = new AnalysisTarget();
            analysisTarget.setNos(CollUtil.newArrayList(dataNoSet));
            analysisTarget.setType("data");
            analysisTargets.add(analysisTarget);
        }

        if (CollUtil.isNotEmpty(analysisTargets)) {
            item.setTarget(analysisTargets);
        }
    }

}
