package org.biosino.node.process.service.metadata.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Project;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.node.process.dto.ConfigRule;
import org.biosino.node.process.model.mongo.v1.ProjectV1;
import org.biosino.node.process.service.common.ConversionCommon;
import org.biosino.node.process.service.common.ConversionStrategy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.annotation.Transient;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.*;

import static org.biosino.node.process.service.common.ConversionCommon.*;

/**
 * <AUTHOR> Li
 * @date 2024/6/6
 */
@Slf4j
public class ProjectConversionStrategy implements ConversionStrategy<ProjectV1, Project> {

    private static final File logFile = FileUtil.isWindows()
            ? FileUtil.file("D:/test/node/transfer/project.tsv")
            : FileUtil.file("/tmp/project.tsv");

    private static List<ConfigRule> configRules = new ArrayList<>();

    static {
        CsvReader reader = CsvUtil.getReader();
        // 读取类路径下conversion/project.csv
        ClassPathResource resource = new ClassPathResource("conversion/project.csv");
        try {
            InputStream inputStream = resource.getInputStream();
            configRules = reader.read(new InputStreamReader(inputStream), ConfigRule.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public HashMap<String, Field> getMongoFieldMap(Class clazz) {
        LinkedHashMap<String, Field> map = new LinkedHashMap<>();
        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            org.springframework.data.mongodb.core.mapping.Field annotation = field.getAnnotation(org.springframework.data.mongodb.core.mapping.Field.class);
            Transient aTransient = field.getAnnotation(Transient.class);
            if (aTransient != null) {
                continue;
            }
            String fieldName;
            if (annotation != null) {
                fieldName = annotation.value();
            } else {
                fieldName = field.getName();
            }
            map.put(fieldName, field);
        }
        return map;
    }

    @Override
    public Project convert(ProjectV1 source) {
        return copyV1ToV2(source);
    }

    /* private Project copyV1ToV2(ProjectV1 v1) {
        HashMap<String, Field> v1MongFieldMap = getMongoFieldMap(v1.getClass());

        Project item = new Project();
        HashMap<String, Field> itemMongoFieldMap = getMongoFieldMap(item.getClass());

        String newNo = null;
        for (ConfigRule configRule : configRules) {
            String fieldName = configRule.getFieldName();
            String ruleType = configRule.getRuleType();
            String rule = configRule.getRule();
            String ruleDetail = configRule.getRuleDetail();
            Field sourceField = v1MongFieldMap.get(fieldName);
            Field targetField = itemMongoFieldMap.get(fieldName);
            Object sourceFieldValue = ReflectUtil.getFieldValue(v1, sourceField);
            switch (ruleType) {
                case "id":
                    ReflectUtil.setFieldValue(item, targetField, sourceFieldValue);
                    break;
                case "no":
                    newNo = getNewNo(String.valueOf(sourceField));
                    ReflectUtil.setFieldValue(item, targetField, newNo);
                    break;
                case "必填":
                    if (StrUtil.equals("skip", rule)) {
                        if (sourceField == null) {
                            log.info("项目Id【{}】字段【{}】值为空，跳过", v1.getId(), fieldName);
                            return null;
                        }
                        ReflectUtil.setFieldValue(item, targetField, sourceFieldValue);
                    } else if (StrUtil.equals("filled", rule)) {
                        if (sourceFieldValue == null) {
                            ReflectUtil.setFieldValue(item, targetField, ReflectUtil.getFieldValue(v1, v1MongFieldMap.get(ruleDetail)));
                        } else {
                            ReflectUtil.setFieldValue(item, targetField, sourceFieldValue);
                        }
                    } else if (StrUtil.equals("default", rule)) {
                        if (sourceFieldValue == null) {
                            ReflectUtil.setFieldValue(item, targetField, ruleDetail);
                        } else {
                            ReflectUtil.setFieldValue(item, targetField, sourceFieldValue);
                        }
                    } else if (StrUtil.equals("range", rule)) {
                        if (!ArrayUtil.contains(ruleDetail.split(","), String.valueOf(sourceFieldValue))) {
                            log.info("项目Id【{}】字段【{}】值【{}】不在范围内，跳过", v1.getId(), fieldName, sourceFieldValue);
                            return null;
                        }
                        ReflectUtil.setFieldValue(item, targetField, sourceFieldValue);
                    }
                    break;
                case "used_ids":
                    if (sourceFieldValue.getClass().isArray()) {
                        // arrayToList
                        List<Object> usedIds = arrayToList(sourceFieldValue);
                        usedIds.add(newNo);
                        ReflectUtil.setFieldValue(item, targetField, usedIds);
                    }
                    break;
                default:
                    break;
            }
        }


        return null;
    } */

    // 将数组转换为List的方法
    public static List<Object> arrayToList(Object array) {
        int length = Array.getLength(array);
        Object[] arrayCopy = new Object[length];
        for (int i = 0; i < length; i++) {
            arrayCopy[i] = Array.get(array, i);
        }
        return Arrays.asList(arrayCopy);
    }

    private Project copyV1ToV2(ProjectV1 v1) {

        String projNo = v1.getProjectNo();

        if (StrUtil.isBlank(projNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "proj_no", "proj_no is null"));
            return null;
        }
        if (!isNodeNo(projNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "proj_no", "proj_no is not node number"));
            return null;
        }
        if (v1.getCreateDate() == null) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "submission_date", "submission_date is null"));
            return null;
        }
        if (!StrUtil.equals(v1.getOwnership(), OwnershipEnum.self_support.getDesc())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "ownership", "ownership is not self_support"));
            return null;
        }
        if (StrUtil.isBlank(v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is null"));
            return null;
        }

        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), v1.getSecurity())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "security", "security is not in includeAllSecurity[Public,Restricted,Private]"));
            return null;
        }

        if (!CollUtil.contains(VisibleStatusEnum.includeExistsVisibleStatus(), v1.getVisibleStatus())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "visible_status", "visible_status is not in includeAllVisibleStatus[Accessible,Unaccessible]"));
            return null;
        }

        if (ArrayUtil.contains(ConversionCommon.blackMemberIdArr, v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is in black member list"));
            return null;
        }

        Project item = new Project();
        BeanUtil.copyProperties(v1, item);

        // array 2 list
        if (ArrayUtil.isNotEmpty(v1.getRelatedLinks())) {
            item.setRelatedLinks(CollUtil.newArrayList(v1.getRelatedLinks()));
        }

        if (ArrayUtil.isNotEmpty(v1.getOtherIds())) {
            item.setOtherIds(CollUtil.newArrayList(v1.getOtherIds()));
        }

        if (ArrayUtil.isNotEmpty(v1.getUsedIds())) {
            item.setUsedIds(CollUtil.newArrayList(v1.getUsedIds()));
        }

        // 默认值
        if (v1.getSecurity() != null && v1.getSecurity().contains("Deleted")) {
            item.setVisibleStatus(VisibleStatusEnum.Deleted.name());
        }

        if (item.getUpdateDate() == null) {
            item.setUpdateDate(item.getCreateDate());
        }

        if (item.getExportNum() == null) {
            item.setExportNum(0L);
        }
        if (item.getHitNum() == null) {
            item.setHitNum(0L);
        }

        if (StrUtil.isNotBlank(v1.getSourceProject())) {
            item.setSourceProject(CollUtil.newArrayList(v1.getSourceProject()));
        } else {
            item.setSourceProject(null);
        }
        item.setAudited(AuditEnum.audited.name());

        // 完善submitter
        if (item.getSubmitter() == null) {
            Submitter submitter = getSubmitter(item.getCreator());
            item.setSubmitter(submitter);
        } else {
            item.setSubmitter(fillInMember(item.getSubmitter()));
        }

        // id 升级
        String newProjNo = getNewNo(projNo);

        item.setProjectNo(newProjNo);

        List<String> usedIds = item.getUsedIds() != null ? item.getUsedIds() : new ArrayList<>();
        if (!CollUtil.contains(usedIds, projNo)) {
            usedIds.add(projNo);
            item.setUsedIds(usedIds);
        }

        return item;
    }

}
