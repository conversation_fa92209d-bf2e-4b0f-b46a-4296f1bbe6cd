package org.biosino.node.process.service.metadata.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.core.exception.ServiceException;
import org.biosino.common.mongo.entity.Sample;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.node.process.model.mongo.v1.SampleV1;
import org.biosino.node.process.service.common.ConversionCommon;
import org.biosino.node.process.service.common.ConversionStrategy;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.biosino.node.process.service.common.ConversionCommon.*;

/**
 * <AUTHOR> Li
 * @date 2024/6/6
 */
@Slf4j
public class SampleConversionStrategy implements ConversionStrategy<SampleV1, Sample> {

    private static final File logFile = FileUtil.isWindows()
            ? FileUtil.file("D:/test/node/transfer/sample.tsv")
            : FileUtil.file("/tmp/sample.tsv");

    private static Map<String, Map<String, String>> sapTypeTranConfigMap = new HashMap<>();

    static {
        CsvReader reader = CsvUtil.getReader();
        // 读取类路径下conversion/project.csv
        ClassPathResource resource = new ClassPathResource("conversion/数据升级规则（多维表格）_字段规则_sample (2).csv");
        try {
            InputStream inputStream = resource.getInputStream();
            CsvData csvData = reader.read(new InputStreamReader(inputStream));
            for (CsvRow line : csvData.getRows()) {
                String sapType = line.get(4);
                String sourceField = line.get(2);
                String targetRule = line.get(9);
                if (StrUtil.isBlank(sapType)) {
                    continue;
                }
                Map<String, String> configMap = sapTypeTranConfigMap.get(sapType) == null ? new HashMap<>() : sapTypeTranConfigMap.get(sapType);
                configMap.put(sourceField, targetRule);
                sapTypeTranConfigMap.put(sapType, configMap);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private Map<String, List<String>> sampleTypeToAttrFieldsMap;

    public SampleConversionStrategy(Map<String, List<String>> sampleTypeToAttrFieldsMap) {
        this.sampleTypeToAttrFieldsMap = sampleTypeToAttrFieldsMap;
    }

    public SampleConversionStrategy() {
    }

    @Override
    public Sample convert(SampleV1 source) {
        // 实现 SampleV1 到 Sample 的转换逻辑
        return copyV1ToV2(source);
    }

    private Sample copyV1ToV2(SampleV1 v1) {
        String sapNo = v1.getSapNo();

        if (StrUtil.isBlank(sapNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "sap_no", "sap_no is null"));
            return null;
        }
        if (!isNodeNo(sapNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "sap_no", "sap_no is not node number"));
            return null;
        }
        if (v1.getCreateDate() == null) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "submission_date", "submission_date is null"));
            return null;
        }
        if (!StrUtil.equals(v1.getOwnership(), OwnershipEnum.self_support.getDesc())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "ownership", "ownership is not self_support"));
            return null;
        }
        if (StrUtil.isBlank(v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is null"));
            return null;
        }
        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), v1.getSecurity())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "security", "security is not in includeAllSecurity[Public,Restricted,Private]"));
            return null;
        }
        if (!CollUtil.contains(VisibleStatusEnum.includeExistsVisibleStatus(), v1.getVisibleStatus())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "visible_status", "visible_status is not in includeAllVisibleStatus[Accessible,Unaccessible]"));
            return null;
        }

        if (ArrayUtil.contains(ConversionCommon.blackMemberIdArr, v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is in black member list"));
            return null;
        }

        Sample item = new Sample();
        BeanUtil.copyProperties(v1, item);

        // array 2 list
        if (ArrayUtil.isNotEmpty(v1.getRelatedLinks())) {
            item.setRelatedLinks(CollUtil.newArrayList(v1.getRelatedLinks()));
        }

        if (ArrayUtil.isNotEmpty(v1.getOtherIds())) {
            item.setOtherIds(CollUtil.newArrayList(v1.getOtherIds()));
        }

        if (ArrayUtil.isNotEmpty(v1.getUsedIds())) {
            item.setUsedIds(CollUtil.newArrayList(v1.getUsedIds()));
        }

        // 默认值
        if (v1.getSecurity() != null && v1.getSecurity().contains("Deleted")) {
            item.setVisibleStatus(VisibleStatusEnum.Deleted.name());
        }

        if (item.getUpdateDate() == null) {
            item.setUpdateDate(item.getCreateDate());
        }

        if (item.getExportNum() == null) {
            item.setExportNum(0L);
        }
        if (item.getHitNum() == null) {
            item.setHitNum(0L);
        }
        if (StrUtil.isNotBlank(v1.getTax_id())) {
            item.setTaxId(v1.getTax_id());
        }

        if (StrUtil.isNotBlank(v1.getSourceProject())) {
            item.setSourceProject(CollUtil.newArrayList(v1.getSourceProject()));
        } else {
            item.setSourceProject(null);
        }

        item.setAudited(AuditEnum.audited.name());

        // 完善submitter
        if (item.getSubmitter() == null) {
            Submitter submitter = getSubmitter(item.getCreator());
            item.setSubmitter(submitter);
        } else {
            item.setSubmitter(fillInMember(item.getSubmitter()));
        }

        // id升级
        String newSapNo = getNewNo(sapNo);

        item.setSapNo(newSapNo);

        List<String> usedIds = item.getUsedIds() != null ? item.getUsedIds() : new ArrayList<>();
        if (!CollUtil.contains(usedIds, sapNo)) {
            usedIds.add(sapNo);
            item.setUsedIds(usedIds);
        }

        // attributes处理
        Map<String, String> configMap = sapTypeTranConfigMap.get(item.getSubjectType());

        Map<String, String> attributes = item.getAttributes();

        Map<String, String> customAttr = new LinkedHashMap<>();

        if (StrUtil.isNotBlank(v1.getAmount())) {
            customAttr.put("attributes_amount", v1.getAmount());
        }
        if (StrUtil.isNotBlank(v1.getBatch())) {
            attributes.put("batch", v1.getBatch());
        }
        if (v1.getCollectDate() != null) {
            attributes.put("sample_collection_date", DateUtil.format(v1.getCollectDate(), DatePattern.NORM_DATETIME_PATTERN));
        }
        if (StrUtil.isNotBlank(v1.getExtractedMolType())) {
            attributes.put("molecular_types_extracted_from_samples", v1.getExtractedMolType());
        }
        if (StrUtil.isNotBlank(v1.getExtractionReagent())) {
            attributes.put("reagents_used_to_extract_molecules_from_samples", v1.getExtractionReagent());
        }
        if (StrUtil.isNotBlank(v1.getProtocol())) {
            attributes.put("protocol", v1.getProtocol());
        }
        if (StrUtil.isNotBlank(v1.getProtocolType())) {
            customAttr.put("attributes_protocol_type", v1.getProtocolType());
        }
        if (StrUtil.isNotBlank(v1.getProvider())) {
            attributes.put("biomaterial_provider", v1.getProvider());
        }
        if (StrUtil.isNotBlank(v1.getSampleLoc())) {
            attributes.put("geographic_location", v1.getSampleLoc());
        }
        if (StrUtil.isNotBlank(v1.getStorageDuration())) {
            attributes.put("sample_storage_duration", v1.getStorageDuration());
        }
        if (StrUtil.isNotBlank(v1.getStorageLocation())) {
            attributes.put("sample_storage_location", v1.getStorageLocation());
        }
        if (StrUtil.isNotBlank(v1.getStorageTemperature())) {
            attributes.put("storage_temperature", v1.getStorageTemperature());
        }
        if (StrUtil.isNotBlank(v1.getStorageType())) {
            attributes.put("sample_storage_type", v1.getStorageType());
        }
        if (StrUtil.isNotBlank(v1.getSubjectId())) {
            attributes.put("subject_id", v1.getSubjectId());
        }
        if (StrUtil.isNotBlank(v1.getTreatment())) {
            attributes.put("treatment", v1.getTreatment());
        }

        Map<String, String> tempAttr = new LinkedHashMap<>();


        if (CollUtil.isNotEmpty(attributes) && CollUtil.isNotEmpty(configMap)) {
            List<String> templateAttrFields = sampleTypeToAttrFieldsMap.get(item.getSubjectType());
            for (String key : attributes.keySet()) {
                String value = attributes.get(key);
                String targetConfig = configMap.get("attributes." + key);
                // 如果更新规则里面没有对应的规则 且 模板里面没有对应的字段，则放到custom_attr
                if (targetConfig == null && !templateAttrFields.contains(key)) {
                    customAttr.put(key, value);
                    continue;
                }
                // 如果更新规则里面没有对应的规则 但是 模板里面有对应的字段，则放到attributes
                if (targetConfig == null && templateAttrFields.contains(key)) {
                    tempAttr.put(key, value);
                    continue;
                }
                if (targetConfig.contains("重命名")) {
                    String targetField = extractEnglish(targetConfig);
                    if (targetField == null) {
                        throw new ServiceException("处理出错：" + targetConfig);
                    }
                    String[] split = targetField.split("\\.");
                    String baseField = split[0];
                    String innerField = split[1];
                    if (baseField.equals("custom_attr")) {
                        customAttr.put(innerField, value);
                    } else if (baseField.equals("attributes")) {
                        tempAttr.put(innerField, value);
                    } else {
                        throw new ServiceException("处理出错：" + targetConfig);
                    }
                } else if (targetConfig.contains("拆分")) {
                    tempAttr.put(key, value);
                    tempAttr.put("latitude", value);
                    tempAttr.put("longitude", value);
                } else {
                    tempAttr.put(key, value);
                }
            }
        }

        item.setAttributes(tempAttr);
        item.setCustomAttr(customAttr);
        return item;
    }

    public static String extractEnglish(String input) {
        // 正则表达式匹配模式
        String pattern = "[a-zA-Z0-9._]+";

        Pattern r = Pattern.compile(pattern);

        Matcher m = r.matcher(input);
        while (m.find()) {
            return m.group();
        }
        return input;
    }
}
