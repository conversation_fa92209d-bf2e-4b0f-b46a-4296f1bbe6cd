package org.biosino.node.process.service.metadata.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.AuditEnum;
import org.biosino.common.core.enums.OwnershipEnum;
import org.biosino.common.core.enums.SecurityEnum;
import org.biosino.common.core.enums.VisibleStatusEnum;
import org.biosino.common.mongo.entity.Run;
import org.biosino.common.mongo.entity.other.Submitter;
import org.biosino.node.process.model.mongo.v1.RunV1;
import org.biosino.node.process.service.common.ConversionCommon;
import org.biosino.node.process.service.common.ConversionStrategy;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import static org.biosino.node.process.service.common.ConversionCommon.*;

/**
 * <AUTHOR>
 * @date 2024/6/13
 */
@Slf4j
public class RunConversionStrategy implements ConversionStrategy<RunV1, Run> {

    private static final File logFile = FileUtil.isWindows()
            ? FileUtil.file("D:/test/node/transfer/run.tsv")
            : FileUtil.file("/tmp/run.tsv");

    @Override
    public Run convert(RunV1 source) {
        return copyV1ToV2(source);
    }

    private Run copyV1ToV2(RunV1 v1) {
        String runNo = v1.getRunNo();
        String expNo = v1.getExpNo();
        String sapNo = v1.getSapNo();

        if (StrUtil.isBlank(runNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "run_no", "run_no is null"));
            return null;
        }

        if (!isNodeNo(runNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "run_no", "run_no is not node number"));
            return null;
        }

        if (StrUtil.isBlank(expNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "exp_no", "exp_no is null"));
            return null;
        }

        if (StrUtil.isBlank(sapNo)) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "sap_no", "sap_no is null"));
            return null;
        } else if (sapNo.contains("OED")){
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "sap_no", "sap_no format error"));
            return null;
        }

        if (v1.getCreateDate() == null) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "submission_date", "submission_date is null"));
            return null;
        }

        if (!StrUtil.equals(v1.getOwnership(), OwnershipEnum.self_support.getDesc())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "ownership", "ownership is not self_support"));
            return null;
        }

        if (!CollUtil.contains(SecurityEnum.includeAllSecurity(), v1.getSecurity())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "security", "security is not in includeAllSecurity[Public,Restricted,Private]"));
            return null;
        }

        if (!CollUtil.contains(VisibleStatusEnum.includeExistsVisibleStatus(), v1.getVisibleStatus())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "visible_status", "visible_status is not in includeAllVisibleStatus[Accessible,Unaccessible]"));
            return null;
        }

        if (ArrayUtil.contains(ConversionCommon.blackMemberIdArr, v1.getCreator())) {
            echoLog(logFile, StrUtil.format("{}\t{}\t{}\n", v1.getId(), "creator", "creator is in black member list"));
            return null;
        }

        Run item = new Run();

        BeanUtil.copyProperties(v1, item);
        if (ArrayUtil.isNotEmpty(v1.getRelatedLinks())) {
            item.setRelatedLinks(CollUtil.newArrayList(v1.getRelatedLinks()));
        }

        if (ArrayUtil.isNotEmpty(v1.getOtherIds())) {
            item.setOtherIds(CollUtil.newArrayList(v1.getOtherIds()));
        }

        if (ArrayUtil.isNotEmpty(v1.getUsedIds())) {
            item.setUsedIds(CollUtil.newArrayList(v1.getUsedIds()));
        }

        // 默认值
        if (v1.getSecurity() != null && v1.getSecurity().contains("Deleted")) {
            item.setVisibleStatus(VisibleStatusEnum.Deleted.name());
        }
        if (item.getUpdateDate() == null) {
            item.setUpdateDate(item.getCreateDate());
        }

        if (item.getExportNum() == null) {
            item.setExportNum(0L);
        }
        if (item.getHitNum() == null) {
            item.setHitNum(0L);
        }

        if (StrUtil.isBlank(item.getName())) {
            item.setName(item.getRunNo());
        }

        if (StrUtil.isNotBlank(v1.getSourceProject())) {
            item.setSourceProject(CollUtil.newArrayList(v1.getSourceProject()));
        } else {
            item.setSourceProject(null);
        }

        item.setAudited(AuditEnum.audited.name());

        // 完善submitter
        if (item.getSubmitter() == null) {
            Submitter submitter = getSubmitter(item.getCreator());
            item.setSubmitter(submitter);
        } else {
            item.setSubmitter(fillInMember(item.getSubmitter()));
        }

        // id升级
        String newRunNo = getNewNo(runNo);
        item.setRunNo(newRunNo);

        List<String> usedIds = item.getUsedIds() != null ? item.getUsedIds() : new ArrayList<>();
        if (!CollUtil.contains(usedIds, runNo)) {
            usedIds.add(runNo);
            item.setUsedIds(usedIds);
        }

        item.setExpNo(getNewNo(expNo));
        item.setSapNo(getNewNo(sapNo));

        return item;
    }

}
