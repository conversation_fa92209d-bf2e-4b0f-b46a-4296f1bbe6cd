package org.biosino.node.process.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.biosino.common.core.enums.DownloadType;
import org.biosino.common.mongo.entity.*;
import org.biosino.node.process.config.NodeMongoConfig;
import org.biosino.node.process.config.NodeV1MongoConfig;
import org.biosino.node.process.dto.IpAddr;
import org.biosino.node.process.enums.AuthorizeType;
import org.biosino.node.process.enums.OwnershipEnum;
import org.biosino.node.process.enums.SecurityEnum;
import org.biosino.node.process.enums.VisibleStatusEnum;
import org.biosino.node.process.model.mongo.v1.DownloadByExportInfoV1;
import org.biosino.node.process.model.mongo.v1.DownloadDataInfo;
import org.biosino.node.process.service.common.ConversionCommon;
import org.biosino.node.process.utils.IpUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;

/**
 * 用户下载导出日志表
 * 10小时
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DownloadLogService {

    @Resource
    @Qualifier(NodeMongoConfig.MONGO_TEMPLATE)
    private MongoTemplate nodeV2Template;
    @Resource
    @Qualifier(NodeV1MongoConfig.MONGO_TEMPLATE)
    private MongoTemplate nodeV1Template;

    public void downloadByExport(int size) {
        final Map<String, String> ownerMap = new HashMap<>(5000);
        final Map<String, String> ipMap = new HashMap<>(10000);

        Set<String> mainTypeSet = AuthorizeType.getMainTypeSet();

        Query query = new Query();
        long total = nodeV1Template.count(query, DownloadByExportInfoV1.class);
        // 读取次数
        long page = total / size;
        int skip;

        for (int i = 0; i <= page; i++) {
            log.info("page:{}", i);
            List<DownloadLog> downloadLogs = new ArrayList<>();

            skip = i * size;
            List<DownloadByExportInfoV1> exportInfoV1List = nodeV1Template.find(query.limit(size).skip(skip), DownloadByExportInfoV1.class);
            for (DownloadByExportInfoV1 exportInfoV1 : exportInfoV1List) {

                DownloadLog downloadLog = new DownloadLog();

                String typeId = exportInfoV1.getTypeId();
                String type = exportInfoV1.getType();

                if (StrUtil.isBlank(type) || StrUtil.isBlank(typeId)) {
                    continue;
                }

                if (!mainTypeSet.contains(type)) {
                    continue;
                }

                String ownerId = getOwnerId(ownerMap, typeId, type);
                if (ownerId == null) {
                    continue;
                }

                downloadLog.setId(null);
                downloadLog.setType(exportInfoV1.getType());
                downloadLog.setTypeNo(ConversionCommon.getNewNo(exportInfoV1.getTypeId()));
                downloadLog.setCreateTime(exportInfoV1.getExportDate());
                downloadLog.setMemberId(exportInfoV1.getMemberId());
                downloadLog.setDownloadType(DownloadType.http.name());
                downloadLog.setIp(exportInfoV1.getIp());
                downloadLog.setOwnerId(ownerId);

                String ip = downloadLog.getIp();
                String country = null;
                if (ipMap.containsKey(ip)) {
                    country = ipMap.get(ip);
                } else {
                    IpAddr ipCountry = IpUtils.getIpCountry(ip);
                    if (ipCountry != null) {
                        country = ipCountry.getCountry();
                    }
                    ipMap.put(ip, country);
                }
                downloadLog.setCountry(country);
                downloadLogs.add(downloadLog);
            }
            if (!downloadLogs.isEmpty()) {
                nodeV2Template.insert(downloadLogs, DownloadLog.class);
            }
        }
    }

    public void downloadByData(int size) {
        final Map<String, String> ownerMap = new HashMap<>(5000);
        final Map<String, String> ipMap = new HashMap<>(10000);

        Query query = new Query();
        long total = nodeV1Template.count(query, DownloadDataInfo.class);
        // 读取次数
        long page = total / size;
        int skip;

        for (int i = 0; i <= page; i++) {
            log.info("page:{}", i);
            List<DownloadLog> downloadLogs = new ArrayList<>();

            skip = i * size;
            List<DownloadDataInfo> dataInfos = nodeV1Template.find(query.limit(size).skip(skip), DownloadDataInfo.class);
            for (DownloadDataInfo dataInfo : dataInfos) {

                DownloadLog downloadLog = new DownloadLog();

                String typeId = dataInfo.getDataNo();
                String type = AuthorizeType.data.name();

                if (StrUtil.isBlank(typeId)) {
                    continue;
                }

                if (!typeId.startsWith("OED")) {
                    continue;
                }

                String ownerId;
                if (ownerMap.containsKey(typeId)) {
                    ownerId = ownerMap.get(typeId);
                } else {
                    // 填充 Owner Id
                    Data data = findDataByNo(typeId);
                    if (data == null) {
                        ownerMap.put(typeId, "");
                        continue;
                    }
                    ownerId = data.getCreator();
                    ownerMap.put(typeId, data.getCreator());
                }
                if (StrUtil.isBlank(ownerId)) {
                    continue;
                }

                downloadLog.setId(null);
                downloadLog.setType(type);
                downloadLog.setTypeNo(ConversionCommon.getNewNo(typeId));
                downloadLog.setCreateTime(dataInfo.getDownTime());
                downloadLog.setMemberId(dataInfo.getDownUser());
                downloadLog.setIp(dataInfo.getIp());
                downloadLog.setOwnerId(ownerId);
                downloadLog.setDownloadType(DownloadType.http.name());

                String ip = downloadLog.getIp();
                String country = null;
                if (ipMap.containsKey(ip)) {
                    country = ipMap.get(ip);
                } else {
                    IpAddr ipCountry = IpUtils.getIpCountry(ip);
                    if (ipCountry != null) {
                        country = ipCountry.getCountry();
                    }
                    ipMap.put(ip, country);
                }
                downloadLog.setCountry(country);
                downloadLogs.add(downloadLog);
            }
            if (!downloadLogs.isEmpty()) {
                nodeV2Template.insert(downloadLogs, DownloadLog.class);
            }
        }
    }

    public void downloadByFtpData() {

        Calendar calendar = Calendar.getInstance();
        // 获取当前年份
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();

        for (int year = 2021; year <= currentYear; year++) {
            for (int month = 0; month < 12; month++) {
                if (year == 2021 && month < 8) {
                    continue;
                }
                // 设置当前年份和月份
                calendar.set(Calendar.YEAR, year);
                calendar.set(Calendar.MONTH, month);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);

                // 获取月份的开始时间
                Date startOfMonth = calendar.getTime();

                LocalDate localDateFromCalendar = LocalDate.of(
                        calendar.get(Calendar.YEAR),
                        calendar.get(Calendar.MONTH) + 1,
                        calendar.get(Calendar.DAY_OF_MONTH)
                );

                if (localDateFromCalendar.isAfter(currentDate)) {
                    continue;
                }

                // 格式化月份开始时间
                SimpleDateFormat format = new SimpleDateFormat("yyyy.MM");

                String formattedStart = format.format(startOfMonth);

                generateFtpData(formattedStart);
            }
        }
    }

    private void generateFtpData(String month) {
        // 计算每种类型的访问次数
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM");
        Date startOfMonth = null;
        Date endOfMonth = null;

        try {
            // 解析传入的月份字符串
            startOfMonth = formatter.parse(month);

            // 使用Calendar来找到月份的结束日期
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startOfMonth);
            // 增加一个月
            calendar.add(Calendar.MONTH, 1);
            // 减去一秒以回到当前月的最后一天
            calendar.add(Calendar.SECOND, -1);

            endOfMonth = calendar.getTime();
        } catch (ParseException e) {
            log.error("日期格式错误，解析识别：{}", month);
        }

        if (startOfMonth == null) {
            return;
        }

        Query query = new Query(Criteria.where("down_time").gte(startOfMonth).lt(endOfMonth).and("ip").is(DownloadType.ftp.name()));
        List<String> memberIds = nodeV1Template.findDistinct(query, "data_user", DownloadDataInfo.class, String.class);

        if (CollUtil.isEmpty(memberIds)) {
            return;
        }

        for (String memberId : memberIds) {
            List<DownloadLog> downloadLogs = new ArrayList<>();

            Query query2 = new Query(Criteria.where("data_user").is(memberId).and("ip").is(DownloadType.ftp.name()).and("down_time").gte(startOfMonth).lte(endOfMonth));
            List<DownloadDataInfo> dataInfos = nodeV1Template.find(query2, DownloadDataInfo.class);
            if (CollUtil.isEmpty(dataInfos)) {
                continue;
            }
            Set<String> existId = new HashSet<>();

            for (DownloadDataInfo dataInfo : dataInfos) {

                String typeId = dataInfo.getDataNo();

                if (StrUtil.isBlank(typeId)) {
                    continue;
                }

                if (existId.contains(typeId)) {
                    continue;
                } else {
                    existId.add(typeId);
                }

                List<Data> dataList = null;

                if (typeId.startsWith("OER")) {
                    dataList = findDataByRunNo(typeId);
                } else if (typeId.startsWith("OEZ")) {
                    dataList = findDataByAnalNo(typeId);
                }

                if (CollUtil.isEmpty(dataList)) {
                    continue;
                }

                for (Data data : dataList) {
                    DownloadLog downloadLog = new DownloadLog();

                    downloadLog.setId(null);
                    downloadLog.setType(AuthorizeType.data.name());
                    downloadLog.setTypeNo(ConversionCommon.getNewNo(data.getDatNo()));
                    downloadLog.setCreateTime(dataInfo.getDownTime());
                    downloadLog.setMemberId(dataInfo.getDownUser());
                    downloadLog.setIp(null);
                    downloadLog.setOwnerId(data.getCreator());
                    downloadLog.setDownloadType(DownloadType.ftp.name());
                    // 一期没有存储IP地址
                    downloadLog.setIp("unknown");

                    downloadLogs.add(downloadLog);
                }
            }

            if (!downloadLogs.isEmpty()) {
                nodeV2Template.insert(downloadLogs, DownloadLog.class);
            }
        }
    }

    private String getOwnerId(Map<String, String> ownerMap, String typeId, String type) {
        String ownerId;
        if (ownerMap.containsKey(typeId)) {
            ownerId = ownerMap.get(typeId);
        } else {
            // 填充 Owner Id
            if (AuthorizeType.project.name().equals(type)) {
                Project project = findProjectByNo(typeId);
                if (project == null) {
                    ownerMap.put(typeId, "");
                    return null;
                }
                ownerId = project.getCreator();
                ownerMap.put(typeId, project.getCreator());
            } else if (AuthorizeType.experiment.name().equals(type)) {
                Experiment project = findExpByNo(typeId);
                if (project == null) {
                    ownerMap.put(typeId, "");
                    return null;
                }
                ownerId = project.getCreator();
                ownerMap.put(typeId, project.getCreator());
            } else if (AuthorizeType.sample.name().equals(type)) {
                Sample project = findSapByNo(typeId);
                if (project == null) {
                    ownerMap.put(typeId, "");
                    return null;
                }
                ownerId = project.getCreator();
                ownerMap.put(typeId, project.getCreator());
            } else if (AuthorizeType.analysis.name().equals(type)) {
                Analysis project = findAnalysisByNo(typeId);
                if (project == null) {
                    ownerMap.put(typeId, "");
                    return null;
                }
                ownerId = project.getCreator();
                ownerMap.put(typeId, project.getCreator());
            } else {
                ownerMap.put(typeId, "");
                return null;
            }
        }
        if (StrUtil.isBlank(ownerId)) {
            return null;
        }
        return ownerId;
    }

    public Project findProjectByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("proj_no").is(analNo);
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return nodeV1Template.findOne(query, Project.class);
    }

    public Experiment findExpByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("exp_no").is(analNo);
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return nodeV1Template.findOne(query, Experiment.class);
    }

    public Sample findSapByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("sap_no").is(analNo);
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return nodeV1Template.findOne(query, Sample.class);
    }

    public Analysis findAnalysisByNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analNo);
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return nodeV1Template.findOne(query, Analysis.class);
    }

    public Run findRunByNo(String runNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").is(runNo);
        criteria.and("visible_status").in(VisibleStatusEnum.includeExistsVisibleStatus());
        criteria.and("ownership").is(OwnershipEnum.self_support.getDesc());
        query.addCriteria(criteria);
        return nodeV1Template.findOne(query, Run.class);
    }

    public Data findDataByNo(String dataNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("dat_no").is(dataNo);
        criteria.and("security").in(SecurityEnum.includeAllSecurity());
        query.addCriteria(criteria);
        return nodeV1Template.findOne(query, Data.class);
    }

    public List<Data> findDataByRunNo(String runNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("run_no").is(runNo);
        criteria.and("security").in(SecurityEnum.includeAllSecurity());
        query.addCriteria(criteria);
        return nodeV1Template.find(query, Data.class);
    }

    public List<Data> findDataByAnalNo(String analNo) {
        Query query = new Query();
        Criteria criteria = Criteria.where("anal_no").is(analNo);
        criteria.and("security").in(SecurityEnum.includeAllSecurity());
        query.addCriteria(criteria);
        return nodeV1Template.find(query, Data.class);
    }
}
