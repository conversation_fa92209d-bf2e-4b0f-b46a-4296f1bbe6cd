<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.biosino.node</groupId>
    <artifactId>node</artifactId>
    <version>3.6.3</version>

    <name>node</name>
    <url>https://www.biosino.org/node</url>
    <description>多组学数据提交系统</description>

    <properties>
        <node.version>3.6.3</node.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>
        <spring-cloud-alibaba.version>2021.0.6.0</spring-cloud-alibaba.version>
        <spring-boot-admin.version>2.7.11</spring-boot-admin.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <tobato.version>1.27.2</tobato.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.0.0</pagehelper.boot.version>
        <druid.version>1.2.20</druid.version>
        <dynamic-ds.version>4.2.0</dynamic-ds.version>
        <commons.io.version>2.15.1</commons.io.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <velocity.version>2.3</velocity.version>
        <fastjson2.version>2.0.51</fastjson2.version>
        <fastjson.version>1.2.83_noneautotype</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>5.2.5</poi.version>
        <transmittable-thread-local.version>2.14.4</transmittable-thread-local.version>

        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <lombok.version>1.18.30</lombok.version>

        <hutool.version>5.8.29</hutool.version>
        <easy.es.version>2.0.0</easy.es.version>
        <elasticsearch.version>7.14.2</elasticsearch.version>

        <spring-security.version>5.7.12</spring-security.version>
        <spring-framework.version>5.3.39</spring-framework.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!--指定spring框架版本-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-core</artifactId>
                <version>${easy.es.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.easy-es</groupId>
                <artifactId>easy-es-boot-starter</artifactId>
                <version>${easy.es.version}</version>
            </dependency>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringSecurity的依赖配置-->
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-bom</artifactId>
                <version>${spring-security.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-core</artifactId>
                <version>${spring-security.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.security</groupId>
                <artifactId>spring-security-crypto</artifactId>
                <version>${spring-security.version}</version>
            </dependency>

            <!-- FastDFS 分布式文件系统 -->
            <dependency>
                <groupId>com.github.tobato</groupId>
                <artifactId>fastdfs-client</artifactId>
                <version>${tobato.version}</version>
            </dependency>

            <!-- Swagger 依赖配置 -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.core.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- 核心模块 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-core</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-swagger</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-security</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-datascope</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-datasource</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-seata</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-log</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-redis</artifactId>
                <version>${node.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-api-system</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-api-es</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-api-app</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-api-upload</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-api-job</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-mongo</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-es</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-common-rabbitmq</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-notification</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.biosino.node</groupId>
                <artifactId>node-task</artifactId>
                <version>${node.version}</version>
            </dependency>

            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${elasticsearch.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.elasticsearch</groupId>
                        <artifactId>elasticsearch</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.elasticsearch.client</groupId>
                        <artifactId>elasticsearch-rest-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-client</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.elasticsearch</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${elasticsearch.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <scope>provided</scope>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!-- hutool -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>node-auth</module>
        <module>node-gateway</module>
        <module>node-visual</module>
        <module>node-modules</module>
        <module>node-api</module>
        <module>node-common</module>
        <module>node-fastqc</module>
        <module>node-data-process</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp/</directory>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>logback*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <profile>
            <id>dev-lwj</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.server>10.10.122.21:31132</nacos.server>
                <nacos.namespace>85fe3384-f9db-4d48-9965-11812ddfc81d</nacos.namespace>
            </properties>
        </profile>
        <profile>
            <id>dev-test</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <nacos.server>dev-nacos:8848</nacos.server>
                <nacos.namespace>9ff2f7ec-1076-47c4-9de5-390a6e98a99e</nacos.namespace>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>prod</profiles.active>
                <nacos.server>node-nacos:8848</nacos.server>
                <nacos.namespace>9a59468e-961d-47b4-b8d4-3d90c3343a9b</nacos.namespace>
            </properties>
        </profile>
    </profiles>
</project>
